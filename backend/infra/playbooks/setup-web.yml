---
- hosts: web
  roles:
    - dokku_bot.ansible_dokku
  vars:
    dokku_version: '{{ clash_dokku_version }}'
    dokku_users:
      - name: michal
        username: michal
        ssh_key: "{{lookup('file', '~/.ssh/michal.pub')}}"
      - name: clash
        username: clash
        ssh_key: "{{lookup('file', '~/.ssh/clash.pub')}}"
  tasks:
    - name: Load variables from vars/web.yml
      ansible.builtin.include_vars:
        file: web.yml

    - name: Create apps
      dokku_app:
        app: '{{ item }}'
      loop:
        - main
        - client
        - socket
      tags: dokku

    - name: client domain setup
      dokku_domains:
        app: client
        domains:
          - '{{ clash_domain }}'
          - '{{ inventory_hostname }}.{{ clash_domain }}'
      tags: dokku

    - name: socket domain setup
      dokku_domains:
        app: socket
        domains:
          - 'ws.{{ clash_domain }}'
          - '{{ inventory_hostname }}-ws.{{ clash_domain }}'
      tags: dokku

    - name: docker-options APPNAME
      dokku_docker_options:
        app: '{{ item }}'
        phase: build
        option: '--build-arg APPNAME={{ item }}'
      loop:
        - main
        - socket
      tags: dokku

    - name: docker-options client
      dokku_docker_options:
        app: client
        phase: build
        option: '--build-arg {{ item }}'
      loop:
        - 'API_URL=http://main.web:5000'
        - 'WS_URL=ws.{{ clash_domain }}'
        - 'CRISP_WEBSITE_ID={{ clash_crisp_website_id }}'
        - 'STRAPI_API_KEY={{ clash_strapi_api_key }}'
        - 'STRAPI_API_URL={{ clash_strapi_api_url }}'
      tags: dokku

    - name: create network
      shell: dokku network:create clash
      tags: dokku

    - name: connect network
      shell: 'dokku network:set {{ item }} attach-post-deploy clash'
      loop:
        - main
        - client
      tags: dokku

    - name: set app pararrelism
      shell: 'dokku scheduler-docker-local:set {{ item }} parallel-schedule-count 16'
      loop:
        - main
        - client
        - socket
      tags: dokku

    - name: scale apps
      shell: 'dokku ps:scale {{ item.app }} web={{ item.web }}'
      with_items:
        - { app: 'main', web: 32 }
        - { app: 'client', web: 32 }
        - { app: 'socket', web: 8 }
      tags: dokku

    - name: add client port mapping
      shell: dokku ports:add client http:80:3000
      tags: dokku

    - name: update nginx worker_connections
      ansible.builtin.lineinfile:
        path: /etc/nginx/nginx.conf
        regexp: '^(\s*)worker_connections\s+\d+;'
        line: 'worker_connections {{ nginx_worker_connections }};'
      tags: nginx

    - name: update nginx ulimit
      ansible.builtin.lineinfile:
        path: /etc/nginx/nginx.conf
        regexp: '^(\s*)worker_rlimit_nofile\s+\d+;'
        line: 'worker_rlimit_nofile {{ nginx_ulimit }};'
      tags: nginx

    - name: add clash ssh key private
      copy:
        src: '~/.ssh/clash'
        dest: '~/.ssh/id_rsa'
        mode: '0600'
      tags: repositories

    - name: add clash ssh key public
      copy:
        src: '~/.ssh/clash.pub'
        dest: '~/.ssh/id_rsa.pub'
        mode: '0600'
      tags: repositories

    - name: add github.com to known hosts
      lineinfile:
        dest: /root/.ssh/known_hosts
        create: yes
        state: present
        line: "{{ lookup('pipe', 'ssh-keyscan -t rsa github.com') }}"
        regexp: "^github\\.com"
      tags: repositories

    - name: check if repositories exist
      stat:
        path: '~/apps'
      register: clash_check_repositories
      tags: repositories

    - name: clone repositories
      shell: |
        cd ~
        git clone {{ item.repo }} apps/{{ item.app }}
        cd apps/{{ item.app }}
        git remote add dokku dokku@localhost:{{ item.app }}
      with_items: '{{ clash_repositories }}'
      when: not clash_check_repositories.stat.exists
      tags: repositories
