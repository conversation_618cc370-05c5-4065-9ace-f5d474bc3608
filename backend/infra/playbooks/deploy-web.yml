---
- hosts: web
  gather_facts: no
  tasks:
    - name: Pull changes
      shell: |
        cd ~/apps/{{ deploy_app }}
        git clean -f
        git fetch origin
        git reset --hard origin/{{ deploy_ref }}
        git checkout origin/{{ deploy_ref }}

    - name: Push to dokku
      shell: |
        cd ~/apps/{{ deploy_app }}
        export GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no"
        git push dokku origin/{{ deploy_ref }}:main -f
      register: clash_dokku_push

    - debug: var=clash_dokku_push.stdout_lines
