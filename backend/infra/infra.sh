#!/usr/bin/env bash

# Deploy a web app
# Usage: deploy <app> <ref>
function deploy() {
   ansible-playbook --extra-vars "deploy_app=$1 deploy_ref=$2" ./playbooks/deploy-web.yml
}

# Set environment variable for an app and restart
# Usage: set-env <app> <key> <val>
function set-env() {
  ansible web -m ansible.builtin.shell -a "dokku config:set '$1' '$2'='$3'"
}

# Set environment variable for an app without restart
# Usage: set-env-no-restart <app> <key> <val>
function set-env-no-restart() {
  ansible web -m ansible.builtin.shell -a "dokku config:set '$1' '$2'='$3' --no-restart"
}

# Restart an app
# Usage: restart <app>
function restart() {
  ansible web -m ansible.builtin.shell -a "dokku ps:restart '$1'"
}

batcat infra.sh --paging=never