HOSTNAME=web01
USERNAME=michal
PASSWORD=""
PUBLIC_KEY=""

####
# Basic user, SSH and firewall setup
####

# Set hostname
hostnamectl set-hostname $HOSTNAME

# Upgrade packages
apt-get update
apt-get -y upgrade
apt-get -y dist-upgrade

# Install fail2ban
apt-get -y install fail2ban

# Create sudo user
adduser --gecos "" --disabled-password --shell /bin/bash $USERNAME
usermod -aG sudo $USERNAME
chpasswd <<< "$USERNAME:$PASSWORD"

# Copy public key
su - $USERNAME -c "mkdir -p ~/.ssh \
  && chmod 700 ~/.ssh \
  && echo $PUBLIC_KEY > ~/.ssh/authorized_keys \
  && chmod 600 ~/.ssh/authorized_keys"

# Disable root login and password authentication
sed -i '/^PermitRootLogin/s/yes/no/' /etc/ssh/sshd_config
sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/g' /etc/ssh/sshd_config
sed -i 's/#PubkeyAuthentication yes/PubkeyAuthentication yes/g' /etc/ssh/sshd_config
echo "ChallengeResponseAuthentication no" >> /etc/ssh/sshd_config

# Allow the non-root user to run commands as root without a password
echo "non-root-user ALL=(ALL) NOPASSWD: ALL" > /etc/sudoers.d/10-nopaswd.conf

# Set SSH port to 2002
sed -i 's/#Port 22/Port 2002/g' /etc/ssh/sshd_config
sudo ufw allow 22/tcp
sudo ufw allow 2002/tcp

# Allow HTTP and HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

####
# Performance and throughput
####

echo "net.core.somaxconn=8192" >> /etc/sysctl.conf
echo "net.ipv4.ip_local_port_range=20000 65535" >> /etc/sysctl.conf

echo "fs.file-max=9223372036854775807" >> /etc/sysctl.conf
echo "fs.inotify.max_user_watches=1048576" >> /etc/sysctl.conf
echo "* soft nofile 1048576" >> /etc/security/limits.d/90-nofile.conf

# Set custom DNS
sed -i 's/#DNS=/DNS=******* 2606:4700:4700::1111/g' /etc/systemd/resolved.conf
sed -i 's/#FallbackDNS=/FallbackDNS=******* 2001:4860:4860::8888/g' /etc/systemd/resolved.conf

####
# Finalize
####

sudo systemctl restart sshd

# Enable firewall
sudo ufw enable
sudo ufw status verbose

reboot

####
# After reboot
####

sudo ufw delete allow 22/tcp
