{"version": "2.0.0", "tasks": [{"label": "main", "type": "shell", "command": "yarn main dev", "presentation": {"reveal": "always", "panel": "new", "group": "develop"}, "icon": {"id": "console"}, "isBackground": true, "runOptions": {"instanceLimit": 1}, "problemMatcher": []}, {"label": "socket", "type": "shell", "command": "yarn socket dev", "presentation": {"reveal": "always", "panel": "new"}, "icon": {"id": "console"}, "isBackground": true, "runOptions": {"instanceLimit": 1}, "problemMatcher": []}, {"label": "crash", "type": "shell", "command": "cd packages/crash&&yarn dev", "presentation": {"reveal": "always", "panel": "new"}, "icon": {"id": "console"}, "isBackground": true, "runOptions": {"instanceLimit": 1}, "problemMatcher": []}, {"label": "images", "type": "shell", "command": "yarn images dev", "presentation": {"reveal": "always", "panel": "new"}, "icon": {"id": "console"}, "isBackground": true, "runOptions": {"instanceLimit": 1}, "problemMatcher": []}, {"label": "inventory", "type": "shell", "command": "yarn inventory dev", "presentation": {"reveal": "always", "panel": "new"}, "icon": {"id": "console"}, "isBackground": true, "runOptions": {"instanceLimit": 1}, "problemMatcher": []}, {"label": "prisma", "type": "shell", "command": "npx prisma studio", "presentation": {"reveal": "always", "panel": "new"}, "icon": {"id": "database"}, "isBackground": true, "runOptions": {"instanceLimit": 1}, "problemMatcher": []}]}