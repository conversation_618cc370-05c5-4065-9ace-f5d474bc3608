import {
  Prisma,
  PrismaClient,
  User,
  Bet,
  InventoryStatus,
  FreeCaseStatus,
  UserBadge,
} from '@prisma/client'
import assert from 'assert'
import {
  isTxClient,
  logger,
  PrismaTx,
  isBonusExempt,
  incrementRakeback,
  handleAffiliateBet,
  PrismaAny,
} from '.'
import { Float, Int, Currency, REAL_CURRENCIES } from '..'
import {
  addTx,
  addTxAsync,
  BALANCE_KEYS,
  TransactionDetails,
} from './transactions'
import EventEmitter from 'events'
import {
  FIAT_RELEASE_EVENT,
  FiatReleaseEvent,
} from './events/fiat-release.event'
import { getUserRestrictions, UserRestrictions } from './user'
import { warn } from 'console'

interface WagerOrAmount {
  wager?: Int
  amount: Int
}
interface ProcessBet extends WagerOrAmount {
  currency: Currency
  betId: Int
  ticketsEarned?: Int
  affiliateEarnings?: Float
  transactionData?: Partial<Prisma.TransactionCreateInput>
  doWagerChanges?: boolean
  isAsync?: boolean
  userId: Int
  chargeAmount?: Int
}

export interface PlaceBet extends Omit<ProcessBet, 'betId'> {
  game: string
  betData?: Partial<Prisma.BetCreateInput>
}

export interface AddToBet extends ProcessBet {}

const isRealWager = (currency: Currency) => REAL_CURRENCIES.includes(currency)

/**
 * Charges the user and creates transaction/bet records, has to run in a transaction
 *
 * Note: We need the bet ID, so using nested writes in this case is not ideal
 * @see https://github.com/prisma/prisma/discussions/4788
 */
export const placeBet = async (prisma: PrismaTx, details: PlaceBet) => {
  if (process.env.MAINTENANCE === '1') {
    throw new Error('feature_temporarily_disabled')
  }

  const safeBetAmount = getSafeAmount(details.amount)

  const bet = await prisma.bet.create({
    data: {
      user: { connect: { id: details.userId } },
      betAmount: safeBetAmount,
      game: details.game,
      currency: details.currency,
      ...details.betData,
    },
  })

  const placeBetResult = await processBet(prisma, {
    ...details,
    betId: bet.id,
  })
  const { createTransaction, user, affiliateUpdates } = placeBetResult
  logger.debug('placeBet %o', placeBetResult)

  const [txid] = await Promise.all([createTransaction, ...affiliateUpdates])
  logger.info('Placed bet=%o betId=%d txId=%d', details, bet.id, txid)

  return { user, bet, txid }
}

export const addToBet = async (prisma: PrismaTx, details: AddToBet) => {
  const { createTransaction, affiliateUpdates, user } = await processBet(
    prisma,
    details
  )

  const alterBet = prisma.bet.update({
    where: { id: details.betId },
    data: {
      betAmount: { increment: details.amount },
    },
  })

  const [txid, bet] = await Promise.all([
    createTransaction,
    alterBet,
    ...affiliateUpdates,
  ])
  logger.info('Altered bet=%o betId=%d txId=%d', details, bet.id, txid)

  return { user, bet, txid }
}

const processBet = async (prisma: PrismaTx, details: ProcessBet) => {
  assert(isTxClient(prisma), 'Must be a transaction client')
  logger.info('Attempting to place bet=%o', details)

  const balanceKey = BALANCE_KEYS[details.currency]

  details.doWagerChanges ??= true
  details.ticketsEarned ??= details.amount
  details.ticketsEarned = getSafeAmount(details.ticketsEarned)
  const ticketChange = Math.round(details.ticketsEarned)

  const userChanges: Prisma.UserUpdateInput = {
    [balanceKey]: { decrement: details.chargeAmount ?? details.amount },
  }
  if (details.isAsync) userChanges.asyncBets = { increment: 1 }

  const user = await prisma.user.update({
    where: { id: details.userId },
    data: userChanges,
  })

  logger.info('Got user bet=%o', details)

  if (user[balanceKey] < 0) {
    logger.info('Cannot place bet - insufficient balance bet=%o', details)
    throw new Error('insufficient_balance')
  }

  let affiliateUpdates: Prisma.PrismaPromise<any>[] = []
  if (isRealWager(details.currency)) {
    if (user.isFiat) {
      await prisma.user.update({
        where: { id: details.userId },
        data: { balanceAmoutFiat: { decrement: details.amount } },
      })
    }

    if (details.doWagerChanges) {
      const wagerChanges = await handleWagerChanges({
        userId: details.userId,
        betId: details.betId,
        wager: details.wager ?? details.amount,
        affiliateEarnings: details.affiliateEarnings,
        ticketChange,
        prisma,
      })

      affiliateUpdates = wagerChanges.affiliateUpdates
    }
  }

  const createTransaction = createBetTransaction(
    prisma,
    details.userId,
    -details.amount,
    0, // TODO
    {
      ticketChange,
      currency: details.currency,
      ...details.transactionData,
    }
  )

  return { user, createTransaction, affiliateUpdates }
}

export interface HandleWagerChangesOptions {
  userId: Int
  betId: Int
  wager: Int
  prisma: PrismaTx
  affiliateEarnings?: Float
  ticketChange?: Int
}

export const handleWagerChanges = async (
  options: HandleWagerChangesOptions
) => {
  const { userId, wager, prisma, affiliateEarnings, ticketChange, betId } =
    options

  const safeWager = getSafeAmount(wager)

  const getRestrictions = getUserRestrictions(prisma, userId)
  const updateBet = prisma.bet.update({
    where: { id: betId },
    data: { riskAdjustedWager: { increment: safeWager } },
  })

  const [userRestrictions] = await Promise.all([getRestrictions, updateBet])

  const tickets = ticketChange ?? wager
  const safeTickets = getSafeAmount(tickets)
  const xpEarned = getXpEarned({ wager } as WagerOrAmount, userRestrictions)

  const user = await prisma.user.update({
    where: { id: userId },
    data: {
      tickets: { increment: safeTickets },
      xp: { increment: xpEarned },
      totalWagered: { increment: wager },
    },
  })

  if (user.badge === UserBadge.VERIFIED) {
    await prisma.user.update({
      where: { id: userId },
      data: {
        tickets: { decrement: safeTickets },
      },
    })
  }

  const affiliateUpdates = await handleAffiliateBet({
    tx: prisma,
    userId,
    wagerAmount: wager,
    affiliateEarnings:
      user.badge === UserBadge.VERIFIED ? 0 : affiliateEarnings,
  })

  if (!isBonusExempt(userId)) {
    await incrementRakeback(prisma, userId, wager)
  }

  await decreaseWagerReq({
    userId,
    amount: wager,
    prisma,
  })

  return { affiliateUpdates }
}

export const stornoBet = async (tx: PrismaTx, bet: Bet) => {
  const currency = (bet.currency as Currency) ?? Currency.REAL
  const addTransaction = addTx(tx, {
    userId: bet.userId,
    balanceChange: bet.betAmount,
    balanceChangeSite: -bet.betAmount,
    currency,
    category: 'bet',
    message: `Bet storno #${bet.id}`,
  })

  const updateBet = tx.bet.update({
    where: { id: bet.id },
    data: { betAmount: 0 },
  })

  const balanceKey = BALANCE_KEYS[currency]
  const userChanges: Prisma.UserUpdateInput = {
    [balanceKey]: { increment: bet.betAmount },
  }

  const isReal = isRealWager(currency)
  if (isReal) {
    userChanges.totalWagered = { decrement: bet.betAmount }
    // TODO: handle custom ticket rates
    userChanges.tickets = { decrement: bet.betAmount }

    const userRestrictions = await getUserRestrictions(tx, bet.userId)
    if (!userRestrictions.isLevelLoanActive) {
      userChanges.xp = { decrement: bet.winningAmount }
    }
  }

  const rollbackUser = tx.user.update({
    where: { id: bet.userId },
    data: userChanges,
  })

  const [user] = await Promise.all([rollbackUser, updateBet, addTransaction])

  if (isReal) {
    const promises = await handleAffiliateBet({
      tx,
      userId: bet.userId,
      wagerAmount: -bet.betAmount,
      isRevert: true,
    })
    await Promise.all(promises)

    await decreaseWagerReq({
      userId: bet.userId,
      amount: -bet.betAmount,
      prisma: tx,
    })

    if (!isBonusExempt(bet.userId)) {
      await incrementRakeback(tx, bet.userId, -bet.betAmount)
    }
  }

  return { user }
}

interface DecreaseWagerReqParams {
  userId: Int
  amount: Int
  prisma: PrismaAny
}

export const decreaseWagerReq = async (params: DecreaseWagerReqParams) => {
  const { userId, amount, prisma } = params

  await prisma.userRestrictions
    .update({
      where: { userId },
      data: { wagerRequirement: { decrement: amount } },
    })
    .then(async (rawRestriction) => {
      const dbWagerKeys = ['wagerRequirement']

      const bonusDecrease = Math.min(
        Number(rawRestriction.bonusWagerRequirement),
        amount
      )

      if (bonusDecrease !== 0) {
        dbWagerKeys.push('bonusWagerRequirement')
        await prisma.userRestrictions.update({
          where: { userId },
          data: { bonusWagerRequirement: { decrement: bonusDecrease } },
        })
      }

      const isBig =
        rawRestriction.wagerRequirement >
        100 * Number(rawRestriction.wagerRequirementScale)

      logger.info(
        'decrease=%o user=%d amount=%d big=%d',
        dbWagerKeys,
        userId,
        amount,
        isBig ? 1 : 0
      )
    })
    .catch((err) => {
      if (err.code === 'P2025') {
        logger.info(
          'Skipping decreaseWagerRequirement - no wager found userId=%d',
          userId
        )
        return
      }
      throw err
    })
}

export interface HandleWin {
  betId: Int
  winningAmount: Int
  increaseBalance?: Int
  balanceChangeSite?: Int
  betData?: Partial<Prisma.BetUpdateInput>
  transactionData?: Partial<Prisma.TransactionCreateInput>
  currency?: Currency
  isAsync?: boolean
}

// Workaround for maximum win in Skywind. Winner takes the Clash
export const getSafeAmount = (amount: Int) => {
  const safeAmount = 10_000_000_00
  return Math.min(safeAmount, amount)
}

/**
 * Credits the user and creates transaction/bet records, has to run in a transaction
 */
export const handleWin = async (prisma: PrismaTx, details: HandleWin) => {
  assert(isTxClient(prisma), 'Must be a transaction client')
  logger.debug('Handling win bet=%o', details)
  const balanceChange = details.increaseBalance ?? details.winningAmount

  const safeTransactionAmount = getSafeAmount(balanceChange)
  const safeWinningAmount = getSafeAmount(details.winningAmount)

  const bet = await prisma.bet.update({
    where: { id: details.betId },
    data: {
      winningAmount: safeWinningAmount,
      ...details.betData,
    },
  })

  const balanceKey = BALANCE_KEYS[details.currency || Currency.REAL]
  const updateUser = prisma.user.update({
    where: { id: bet.userId },
    data: {
      [balanceKey]: {
        increment: details.increaseBalance ?? details.winningAmount,
      },
    },
  })

  const createTransaction = createBetTransaction(
    prisma,
    bet.userId,
    safeTransactionAmount,
    0,
    {
      currency: details.currency,
      ...details.transactionData,
    }
  )

  const [user, transaction] = await Promise.all([updateUser, createTransaction])
  logger.info('Winnings paid out bet=%o userId=%d', details, user.id)

  await onBetFinished(
    prisma,
    user.id,
    safeTransactionAmount,
    details.isAsync,
    details.currency
  )

  return { user, bet, transaction }
}

export const stornoWin = async (
  prisma: PrismaTx,
  bet: Bet,
  transactionData?: Partial<Prisma.TransactionCreateInput>
) => {
  assert(isTxClient(prisma), 'Must be a transaction client')
  logger.debug('Handling storno win bet=%o', bet)
  const balanceChange = bet.winningAmount
  const currency = bet.currency as Currency

  const safeWinningAmount = getSafeAmount(balanceChange)

  const newBet = await prisma.bet.update({
    where: { id: bet.id },
    data: {
      winningAmount: 0,
      data: { ...(bet.data as object), stornoWin: balanceChange },
    },
  })

  const balanceKey = BALANCE_KEYS[bet.currency || Currency.REAL]
  const updateUser = prisma.user.update({
    where: { id: bet.userId },
    data: {
      [balanceKey]: {
        decrement: balanceChange,
      },
    },
  })

  const createTransaction = createBetTransaction(
    prisma,
    bet.userId,
    safeWinningAmount,
    0,
    {
      currency,
      ...transactionData,
    }
  )

  const [user, transaction] = await Promise.all([updateUser, createTransaction])
  logger.info('Storno winnings bet=%o userId=%d', bet, user.id)

  await onWinStorno(prisma, user.id, safeWinningAmount, currency)

  return { user, bet: newBet, transaction }
}

export interface HandleItemWin {
  betId: Int
  userId: Int
  items: Prisma.InventoryItemCreateManyInput[]
  currency: Currency.REAL
  winAmount?: Int
  betData?: Partial<Prisma.BetUpdateInput>
  transactionData?: Partial<Prisma.TransactionCreateInput>
  isAsync?: boolean
}

/**
 * Insert items and creates transaction/bet records, has to run in a transaction
 */
export const handleItemWin = async (
  prisma: PrismaTx,
  details: HandleItemWin
) => {
  assert(isTxClient(prisma), 'Must be a transaction client')
  assert(REAL_CURRENCIES.includes(details.currency), 'Currency must be REAL')
  logger.debug('Handling item win details=%o', details)

  if (!details.winAmount)
    details.winAmount = details.items.reduce((acc, item) => acc + item.price, 0)

  const updateBet = prisma.bet.update({
    where: { id: details.betId },
    data: {
      winningAmount: details.winAmount,
      ...details.betData,
    },
    include: { user: true },
  })

  const insertItems = prisma.inventoryItem.createManyAndReturn({
    data: details.items,
    select: { id: true },
  })

  const createTransaction = createBetTransaction(
    prisma,
    details.userId,
    details.winAmount,
    0,
    {
      currency: details.currency,
      ...details.transactionData,
    }
  )

  const [{ user, ...bet }, items, transaction] = await Promise.all([
    updateBet,
    insertItems,
    createTransaction,
  ])

  logger.info(
    'Winnings paid out bet=%o items=%o userId=%d',
    details,
    items,
    user.id
  )

  await onBetFinished(
    prisma,
    details.userId,
    details.winAmount,
    details.isAsync,
    details.currency
  )

  return { user, items, bet, transaction }
}

const createBetTransaction = (
  prisma: PrismaTx,
  userId: Int,
  balanceChange: Int,
  affiliateEarnings: Float,
  transactionData?: Partial<TransactionDetails>
) => {
  const safeBalanceChange = getSafeAmount(balanceChange)
  return addTxAsync(prisma, {
    userId,
    balanceChange: safeBalanceChange,
    category: 'bet',
    balanceChangeSite: -safeBalanceChange - affiliateEarnings,
    ...transactionData,
  })
}

export const onBetFinished = async (
  prisma: PrismaClient | PrismaTx,
  userId: Int,
  winAmount: Int = 0,
  isAsync = false,
  currency = Currency.REAL
) => {
  logger.info(
    `onBetFinished userId=%d win=%d async=%d currency=%s`,
    userId,
    winAmount,
    isAsync,
    currency
  )

  const realWinAmount = REAL_CURRENCIES.includes(currency) ? winAmount : 0

  const user = await prisma.user.update({
    where: { id: userId },
    data: {
      ...(winAmount && { balanceAmoutFiat: { increment: realWinAmount } }),
      ...(isAsync && { asyncBets: { decrement: 1 } }),
    },
  })

  if (REAL_CURRENCIES.includes(currency)) {
    await Promise.all([
      _checkFiatRelease(prisma, user, { winAmount, isAsync }).catch(warn),
      correctRestrictions(prisma, user).catch(warn),
    ])
  }

  return user
}

export const onWinStorno = async (
  prisma: PrismaClient | PrismaTx,
  userId: Int,
  winStornoAmount: Int = 0,
  currency = Currency.REAL
) => {
  logger.info(
    `onWinStorno userId=%d win=%d currency=%s`,
    userId,
    winStornoAmount,
    currency
  )

  const realWinAmount = REAL_CURRENCIES.includes(currency) ? winStornoAmount : 0

  const user = await prisma.user.update({
    where: { id: userId },
    data: {
      ...(winStornoAmount && {
        balanceAmoutFiat: { decrement: realWinAmount },
      }),
    },
  })

  if (REAL_CURRENCIES.includes(currency)) {
    await Promise.all([
      _checkFiatRelease(prisma, user, { winStornoAmount }).catch(warn),
      correctRestrictions(prisma, user).catch(warn),
    ])
  }

  return user
}

export const recordFiatDeposit = async (
  prisma: PrismaClient,
  userId: Int,
  amount: Int
) => {
  const user = await prisma.user.update({
    where: { id: userId },
    data: {
      isFiat: true,
      balanceAmoutFiat: { increment: amount },
    },
  })

  return user
}

const getWager = (details: WagerOrAmount) => details.wager ?? details.amount

const getXpEarned = (
  details: WagerOrAmount,
  userRestrictions: UserRestrictions
) => {
  if (userRestrictions.isLevelLoanActive) {
    return 0
  }

  return getWager(details)
}

// Please forgive me, can't think of a better way to do this without big refactor
export const fiatReleaseEmitter = new EventEmitter()
const _checkFiatRelease = async (
  prisma: PrismaClient | PrismaTx,
  user: User,
  ctx?: any
) => {
  if (user.asyncBets > 0) return

  if (user.balanceAmoutFiat < 0) {
    await prisma.user.update({
      where: { id: user.id },
      data: { isFiat: false, balanceAmoutFiat: 0 },
    })
    await prisma.userLog.create({
      data: {
        userId: user.id,
        category: 'account',
        message:
          `Disabling FIAT because baf=${user.balanceAmoutFiat} ab=${user.asyncBets}` +
          ` ctx=${JSON.stringify(ctx)}`,
      },
    })

    fiatReleaseEmitter.emit(
      FIAT_RELEASE_EVENT,
      new FiatReleaseEvent({ userId: user.id, ...ctx })
    )
  }
}

const correctRestrictions = async (
  prisma: PrismaClient | PrismaTx,
  user: User
) => {
  if (user.asyncBets > 0 || user.balance > 0) return
  const getRestrictions = getUserRestrictions(prisma, user.id)
  const getItemsCount = prisma.inventoryItem.count({
    where: { userId: user.id, status: InventoryStatus.AVAILABLE },
  })
  const getFreeCasesCount = prisma.affiliateUse.count({
    where: {
      userId: user.id,
      freeCases: { some: { status: FreeCaseStatus.AVAILABLE } },
    },
  })
  const [userRestrictions, userItemsCount, hasFreeCases] = await Promise.all([
    getRestrictions,
    getItemsCount,
    getFreeCasesCount,
  ])
  if (userRestrictions.wagerFromGift && !userItemsCount && !hasFreeCases) {
    await decreaseWagerReq({
      prisma,
      userId: user.id,
      amount: userRestrictions.wagerFromGift,
    })
    await prisma.user.update({
      where: { id: user.id },
      data: {
        restrictions: {
          update: {
            wagerFromGift: 0,
          },
        },
      },
    })
  }
}
