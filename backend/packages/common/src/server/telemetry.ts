import { NodeSD<PERSON>, NodeSDKConfiguration } from '@opentelemetry/sdk-node'
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http'
import { Resource } from '@opentelemetry/resources'
import { SemanticResourceAttributes } from '@opentelemetry/semantic-conventions'
import {
  BatchSpanProcessor,
  NoopSpanProcessor,
} from '@opentelemetry/sdk-trace-base'
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node'
import { PeriodicExportingMetricReader } from '@opentelemetry/sdk-metrics'
import { OTLPMetricExporter } from '@opentelemetry/exporter-metrics-otlp-http'
import { WSInstrumentation } from 'opentelemetry-instrumentation-ws'
import { containerDetector } from '@opentelemetry/resource-detector-container'

const headers = { 'uptrace-dsn': process.env.OTLP_UPTRACE_DSN }

const tracesExporter = process.env.OTLP_METRICS_URL
  ? new BatchSpanProcessor(
      new OTLPTraceExporter({
        url: process.env.OTLP_TRACES_URL,
        headers,
      })
    )
  : new NoopSpanProcessor()

const metricExporter = process.env.OTLP_METRICS_URL
  ? new PeriodicExportingMetricReader({
      exporter: new OTLPMetricExporter({
        url: process.env.OTLP_METRICS_URL,
        headers,
      }),
    })
  : null

export const otelConfig = (
  serviceName: string
): Partial<NodeSDKConfiguration> => {
  return {
    serviceName: serviceName === 'main' ? 'main' : undefined,
    resource: new Resource({
      [SemanticResourceAttributes.SERVICE_NAME]: serviceName,
      [SemanticResourceAttributes.SERVICE_VERSION]:
        process.env.GIT_REV?.substring(0, 7),
      [SemanticResourceAttributes.HOST_ID]: process.env.SERVER_ID,
    }),
    spanProcessor: tracesExporter,
    metricReader: metricExporter,
  }
}

export const registerOTel = (serviceName: string) => {
  const sdk = new NodeSDK({
    ...otelConfig(serviceName),
    resourceDetectors: [containerDetector],
    instrumentations: [getNodeAutoInstrumentations(), new WSInstrumentation()],
  })

  sdk.start()

  process.on('SIGTERM', async () => {
    await sdk.shutdown()
  })
}
