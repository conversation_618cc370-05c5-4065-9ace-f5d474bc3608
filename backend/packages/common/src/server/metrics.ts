import { logger } from '..'
import { Float, Int, <PERSON><PERSON> } from '../types'
const { eventLoopUtilization } = require('perf_hooks').performance

class ELUTracker {
  static PRECISION = 4
  private lastResults: Float[] = []

  constructor(
    private duration: <PERSON><PERSON>,
    private limit: Int
  ) {
    this.#track()
  }

  get average() {
    const sum = this.lastResults.reduce((acc, v) => acc + v, 0)
    return sum / this.lastResults.length
  }

  get values() {
    const precision = 10 ** ELUTracker.PRECISION
    return this.lastResults.map((v) => Math.round(v * precision) / precision)
  }

  get metricString() {
    const roundedAvg = this.average.toFixed(ELUTracker.PRECISION)
    return `avg=${roundedAvg} vals=${this.values.join(', ')}`
  }

  async #track() {
    let previous = eventLoopUtilization()
    while (true) {
      await new Promise((resolve) => setTimeout(resolve, this.duration))
      const now = eventLoopUtilization()
      const stats = eventLoopUtilization(now, previous)
      previous = now

      this.lastResults.unshift(stats.utilization)
      if (this.lastResults.length === this.limit) {
        this.lastResults.pop()
      }
    }
  }
}

const DURATION = 5_000
const LIMIT = 60_000 / DURATION
export const eluTracker = new ELUTracker(DURATION, LIMIT)

setInterval(() => {
  logger.info('ELU metrics: %s', eluTracker.metricString)
}, DURATION * LIMIT)
