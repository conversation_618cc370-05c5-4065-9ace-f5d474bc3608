import {
  App,
  Templated<PERSON><PERSON>,
  WebSocket,
  WebSocketBehavior,
  // eslint-disable-next-line camelcase
  us_listen_socket_close,
  DEDICATED_COMPRESSOR_4KB,
} from 'uWebSockets.js'
import { v4 as uuidv4 } from 'uuid'
import { EventEmitter } from 'events'
import assert from 'assert'
import { sleep } from '..'
import { AccessJWT, Int } from '@crashgg/types'
import jwt from 'jsonwebtoken'
import { logger } from '.'

declare const TextDecoder: any
const textDecoder = new TextDecoder('utf-8')

const BROADCAST = 'broadcast'
const GUEST_TIMEOUT = parseInt(process.env.GUEST_TIMEOUT) || 15 * 60 * 1000
const CONNECTIONS_PER_USER = parseInt(process.env.CONNECTIONS_PER_USER) || 100

export interface UserData {
  id: string
  user?: AccessJWT
  isUamPassed?: boolean
  publicEventHandler?: any
  battleEventHandler?: any
  userEventHandler?: any
  subscribedTopics: string[]
}

export interface ServerOptions {
  port?: number
  wsBehavior?: WebSocketBehavior<UserData>
  jwtSecret?: string
}

export type WSEvent<T = any> = [string, T?]

export type WSClient = WebSocket<UserData>

export interface MessageCtx<T = any> {
  op: string
  payload?: T
  ws: WSClient
  reply: <T>(wsEvent: WSEvent<T> | T) => Promise<boolean>
  error: (message: string) => Promise<boolean>
}

export class WSServer extends EventEmitter {
  private app: TemplatedApp
  private sockets: Map<string, WSClient> = new Map()
  private listenSocket: any
  private logger = logger.child({ module: 'WS' })
  private connectionsPerUser: Record<Int, Int> = {}
  onlineCountInstance = 0
  private isUnderAttack = false

  constructor(private options: ServerOptions) {
    super()
    this._initAuthListener()
    this._initUamListener()
  }

  create() {
    this.app = App()
      .ws<UserData>('/*', {
        idleTimeout: 120,
        maxPayloadLength: 1024 ** 2,
        maxBackpressure: 1024 ** 2,
        // 4kb dedicated compressor if dealing with smaller JSON messages
        // @see https://github.com/uNetworking/uWebSockets/blob/master/misc/READMORE.md#settings
        compression: DEDICATED_COMPRESSOR_4KB,
        ...this.options.wsBehavior,

        upgrade: (res, req, context) => {
          const id = uuidv4()
          this.logger.info(
            'Upgrade id=%s %s %s %s %s',
            id,
            req.getHeader('sec-websocket-key'),
            req.getHeader('sec-websocket-protocol'),
            req.getHeader('sec-websocket-extensions'),
            req.getHeader('connection')
          )

          if (!this.listenSocket) {
            this.logger.warn('No listenSocket, closing upgrade')
            res.close()
            return
          }

          res.upgrade(
            { id, subscribedTopics: [] } as Partial<UserData>,
            req.getHeader('sec-websocket-key'),
            req.getHeader('sec-websocket-protocol'),
            req.getHeader('sec-websocket-extensions'),
            context
          )
        },

        open: (ws) => {
          const userData = ws.getUserData()
          this.sockets.set(userData.id, ws)
          this.onlineCountInstance++
          if (!this.isUnderAttack) ws.subscribe(BROADCAST)
          this.emit('connect', ws)

          setTimeout(() => {
            if (!this.sockets.has(userData.id)) return

            if (!userData.user) ws.end()
          }, GUEST_TIMEOUT)
        },

        close: (ws, code) => {
          this.logger.info('Disconnect code=%d', code)
          const userData = ws.getUserData()
          this.sockets.delete(userData.id)
          this.onlineCountInstance--
          this.emit('disconnect', ws)

          if (userData.user) {
            this.connectionsPerUser[userData.user.userId] -= 1
          }
        },

        drain: (ws) => {
          this.logger.info(
            'Backpressure socketId=%s buffered=%d',
            ws.getUserData().id,
            ws.getBufferedAmount()
          )
        },

        message: (ws, rawMessage, isBinary) => {
          if (rateLimit(ws)) {
            this.logger.info(`Disconnecting ratelimited client %o`, {
              user: ws.getUserData().user,
            })
            return ws.end()
          }

          const event = this._parseRawMessage(rawMessage)
          if (!event) return
          const [op, payload] = event

          const reply = async (response: WSEvent | any) => {
            if (Array.isArray(response)) {
              return this.send(ws, response as WSEvent, isBinary)
            }

            return this.send(ws, [op, response], isBinary)
          }

          const error = async (message: string) => {
            return reply({ success: false, message })
          }

          const context: MessageCtx = { op, payload, ws, reply, error }

          if (
            !['connect', 'disconnect', 'authorized', 'message'].includes(op)
          ) {
            this.emit(op, context)
          }

          this.emit('message', context)
        },
      })
      .any('/*', (res) => {
        res
          .writeHeader('Content-Type', 'application/json')
          .end(JSON.stringify({ name: 'uws-server', time: Date.now() }))
      })
      .listen(this.options.port, (token) => {
        if (token) {
          this.listenSocket = token
          this.logger.info(`Listening on port ${this.options.port}`)
        }
      })

    return this
  }

  broadcast(wsEvent: WSEvent) {
    return this.publish(BROADCAST, wsEvent)
  }

  toUser(userId: Int, wsEvent: WSEvent) {
    return this.publish(`user:${userId}`, wsEvent)
  }

  publish(room: string, wsEvent: WSEvent) {
    const message = JSON.stringify(wsEvent)
    this.app.publish(room, message, false, true)
  }

  shutdown() {
    if (!this.listenSocket) return

    this.logger.info('Shutting down...')
    us_listen_socket_close(this.listenSocket)
    this.listenSocket = null

    for (const ws of this.sockets.values()) {
      ws.close()
    }
  }

  setUnderAttack(isUnderAttack: boolean) {
    if (this.isUnderAttack === isUnderAttack) return
    this.logger.info('Under attack mode uam=%s', String(isUnderAttack))

    this.isUnderAttack = isUnderAttack

    if (isUnderAttack) {
      setTimeout(() => {
        this.runUamCheck()
      }, 10_000)
    }
  }

  private async runUamCheck() {
    if (!this.isUnderAttack) return

    let disconnected = 0
    this.sockets.forEach((ws) => {
      if (!ws.getUserData().isUamPassed) {
        ws.end()
        disconnected += 1
      }
    })

    this.logger.info('UAM check disconnected=%s', disconnected)
  }

  private _initUamListener() {
    this.on('uam', (ctx: MessageCtx) => {
      if (this.isUnderAttack) {
        ctx.ws.getUserData().isUamPassed = true
      }
    })
  }

  async send(ws: WSClient, event: WSEvent, isBinary?: boolean) {
    const data = JSON.stringify(event)
    return this._send(ws, data, isBinary)
  }

  async _send(ws: WSClient, data: any, isBinary = false) {
    const id = ws.getUserData().id
    if (!this.sockets.has(id)) {
      return false
    }

    if (ws.getBufferedAmount() > 1024 ** 2) {
      this.logger.info(
        `Sending later, max backpressure reached id=%s chars=%d`,
        id,
        data?.length
      )
      await sleep(20)
      return this._send(ws, data, isBinary)
    }

    return ws.send(data, isBinary, true)
  }

  private _parseRawMessage(rawMessage: ArrayBuffer): WSEvent | false {
    const asText = textDecoder.decode(rawMessage)

    try {
      const parsed: WSEvent = JSON.parse(asText)
      assert(
        Array.isArray(parsed) &&
          parsed.length <= 2 &&
          typeof parsed[0] === 'string',
        'Invalid message format'
      )

      return parsed
    } catch (err) {
      return false
    }
  }

  private _initAuthListener() {
    if (!this.options.jwtSecret) return

    this.on('auth', (ctx: MessageCtx) => {
      if (ctx.ws.getUserData().user) return ctx.reply('invalid_state')
      if (typeof ctx.payload !== 'string') return ctx.reply('invalid_input')

      jwt.verify(
        ctx.payload,
        this.options.jwtSecret,
        (err, jwtPayload: AccessJWT) => {
          if (err) return ctx.reply('invalid_token')
          if (jwtPayload.type !== 'access') return ctx.reply('invalid_type')
          if (jwtPayload.bannedUntil > Date.now()) return ctx.reply('banned')

          const connections = this.connectionsPerUser[jwtPayload.userId] || 0
          this.connectionsPerUser[jwtPayload.userId] = connections + 1

          if (connections > CONNECTIONS_PER_USER) {
            this.logger.warn(
              'Too many connections for userId=%d',
              jwtPayload.userId
            )
            return ctx.ws.end()
          }

          ctx.ws.getUserData().user = jwtPayload
          ctx.ws.subscribe(`user:${jwtPayload.userId}`)
          if (!ctx.ws.isSubscribed(BROADCAST)) ctx.ws.subscribe(BROADCAST)
          ctx.reply(jwtPayload)

          this.emit('authorized', ctx.ws)
        }
      )
    })
  }
}

const RateLimit = (limit: number, interval: number) => {
  let now = 0
  setInterval(() => ++now, interval)

  const last = Symbol('last') as unknown as string
  const count = Symbol('count') as unknown as string

  return (ws: WSClient) => {
    if (ws[last] !== now) {
      ws[last] = now
      ws[count] = 1
    } else {
      return ++ws[count] > limit
    }
  }
}

const rateLimit = RateLimit(25, 1000)
