import IORedis, * as redis from 'ioredis'
import { EventEmitter } from 'stream'
import { Int, logger } from '..'
import { Redlock } from '@sesamecare-oss/redlock'

export class Redis extends EventEmitter {
  client: redis.Redis
  clientSub: redis.Redis
  private subscribers: Record<string, Int> = {}
  private redlock: Redlock
  private customErrorHandler: null | ((error: Error) => void) = null

  constructor(host: string, options?: redis.RedisOptions) {
    super()

    this.client = new IORedis(host, options)
    this.clientSub = new IORedis(host, options)
    this._handleError(this.client)
    this._handleError(this.clientSub)

    this._listenToMessages()

    this.redlock = new Redlock([this.client], {
      retryCount: ~~(10_000 / 400),
      retryDelay: 300,
      retryJitter: 200,
    })
  }

  setCustomErrorHandler(handler: (error: Error) => void) {
    this.customErrorHandler = handler
  }

  _handleError(client: IORedis) {
    client.on('connect', async () => {
      const isReplica = await isRedisReplica(client)
      if (isReplica) {
        logger.error('Redis is in replica mode. Exiting')
        process.exit(1)
      }
    })

    client.on('error', (error: Error) => {
      if (this.customErrorHandler) {
        this.customErrorHandler(error)
        return
      }
      logger.error('Redis error', error.message)
      setTimeout(() => {
        logger.info('Exiting because of redis')
        process.exit(1)
      }, 5000)
    })
  }

  async get(key: string) {
    return this.client.get(key)
  }

  async getJSON<T>(key: string): Promise<T | null> {
    const value = await this.client.get(key)

    if (!value) return null
    return JSON.parse(value)
  }

  async set(key: string, value: unknown, ttl?: Int /* in seconds */) {
    const stringified: any = this._toValue(value)

    if (ttl) return this.client.set(key, stringified, 'EX', ttl)
    return this.client.set(key, stringified)
  }

  async del(key: string) {
    return this.client.del(key)
  }

  async publish(channel: string, message: unknown) {
    const val = this._toValue(message) as string

    return this.client.publish(channel, val)
  }

  async publishPublic(channel: string, message: unknown) {
    return this.publish(`public:${channel}`, message)
  }

  async lock(keys: string[], ttl = 10_000) {
    keys = keys.map((key) => `{lock}:${key}`)
    const lock = await this.redlock.acquire(keys, ttl).catch(() => {
      throw new Error('please_slow_down')
    })

    const release = () => {
      lock.release().catch((err) => {
        console.warn('Failed to release locks message=', err.message)
      })
    }
    return { lock, release }
  }

  async psubscribe(channel: string) {
    this.subscribers[channel] = (this.subscribers[channel] || 0) + 1

    if (this.subscribers[channel] === 1) {
      return this.clientSub.psubscribe(channel)
    }
  }

  async punsubscribe(channel: string) {
    this.subscribers[channel] = (this.subscribers[channel] || 0) - 1

    if (this.subscribers[channel] === 0) {
      return this.clientSub.punsubscribe(channel)
    }
  }

  private _listenToMessages() {
    this.clientSub.on('pmessage', (channel, pattern, message) => {
      try {
        message = JSON.parse(message)
      } catch (e) {
        // noop
      }

      this.emit(channel, { channel: pattern, message })
    })
  }

  private _toValue(value: unknown): redis.RedisValue {
    return ['string', 'number'].includes(typeof value)
      ? (value as redis.RedisValue)
      : JSON.stringify(value)
  }

  async getCachedJSON<T>(
    key: string,
    ttlSeconds: Int,
    fetchData: () => Promise<T>,
    skipLock = false
  ): Promise<T> {
    const cached = await this.getJSON<T>(key)
    if (cached) {
      return cached
    }

    if (skipLock) {
      const data = await fetchData()
      await this.set(key, data, ttlSeconds)

      return data
    }

    const cachePrefix = process.env.CACHE_PREFIX || 'cache'
    const lock = await this.lock([`${cachePrefix}:${key}`])
    try {
      const cached = await this.getJSON<T>(key)
      if (cached) {
        return cached
      }

      const data = await fetchData()
      await this.set(key, data, ttlSeconds)

      return data
    } finally {
      lock.release()
    }
  }
}

const redisSingleton = (url: string) => {
  let redis: Redis

  return () => {
    if (!redis) {
      redis = new Redis(url, {
        retryStrategy: (times) => {
          if (times > 50) {
            logger.error('Redis retry limit reached. Exiting')
            process.exit(1)
          }
          const delay = Math.min(times * 500, 10000)
          logger.error(`Redis connection failed. Retrying in ${delay}ms`)
          return delay
        },
      })
    }

    return redis
  }
}

export const isRedisReplica = async (client: IORedis): Promise<boolean> => {
  const info = await client.info('replication')
  const roleLine = info.split('\n').find((line) => line.startsWith('role:'))
  return roleLine?.includes('slave') || roleLine?.includes('replica') || false
}

export const getRedis = redisSingleton(process.env.REDIS_URL)

export const getRedisLock = redisSingleton(
  process.env.REDIS_URL_LOCK ?? process.env.REDIS_URL
)
