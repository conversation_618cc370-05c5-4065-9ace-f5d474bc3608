import { Prisma, PrismaPromise } from '@prisma/client'
import assert from 'assert'
import { Currency, Int } from '../types'
import { PrismaAny } from './prisma'
import { Feature, isFeature } from './features'

export type TransactionDetails = Prisma.TransactionCreateWithoutUserInput & {
  userId: Int
  currency?: Currency
}

export const BALANCE_KEYS = {
  [Currency.REAL]: 'balance',
  [Currency.PLAY]: isFeature(Feature.PlayBalance) ? 'balancePlay' : 'balance',
  [Currency.CCY]: isFeature(Feature.CryptoBalance)
    ? 'balanceCrypto'
    : 'balance',
  [Currency.BON]: isFeature(Feature.BonusBalance) ? 'balanceBonus' : 'balance',
} as const

export const BALANCE_KEYS_RAW = {
  ...BALANCE_KEYS,
  [Currency.PLAY]: isFeature(Feature.PlayBalance) ? 'balanceFiat' : 'balance',
}

export const getBalance = <C extends Currency>(
  user: Record<(typeof BALANCE_KEYS)[C], bigint>,
  currency: C
): bigint => {
  const key = BALANCE_KEYS[currency]
  if (!key) return -1n

  return user[key]
}

const clampInt = (value: Int) => {
  return Math.min(Math.max(value, -2_100_000_000), 2_100_000_000)
}

/**
 * Create a transaction object with balance after the transaction is settled
 * Should be used in a transaction after balance is deducted
 */
export const addTx = (
  prisma: PrismaAny,
  {
    userId,
    balanceChange = 0,
    balanceChangeSite = 0,
    ticketChange = 0,
    category,
    currency,
    message,
  }: TransactionDetails
): PrismaPromise<[{ id: Int }]> => {
  assert(typeof balanceChange === 'number')
  assert(typeof balanceChangeSite === 'number')

  // Transaction amounts are stored as integers
  balanceChange = clampInt(balanceChange)
  balanceChangeSite = clampInt(balanceChangeSite)

  message = `${message.slice(0, 2000)} (${currency})`

  // For some reason, it can't parse balanceChange / balanceChangeSite
  // when using `$queryRaw`. This is nasty, but the only workaround
  // I'm aware of.
  // These values are typechecked numbers, so there should be no
  // SQL injection risk.
  return prisma.$queryRawUnsafe(
    `INSERT INTO "Transaction"
    ("userId","ticketChange","category","message","balanceChange","balanceChangeSite","balanceAfter")
    VALUES (
      $1, $2, $3, $4, ${balanceChange}, ${balanceChangeSite},
      (SELECT "${BALANCE_KEYS_RAW[currency]}" FROM "User" WHERE "id" = $1)
    )
    RETURNING "Transaction"."id";`,
    userId,
    ticketChange,
    category,
    message
  )
}

export const addTxAsync = async (
  prisma: PrismaAny,
  details: TransactionDetails
): Promise<Int> => {
  const res = await addTx(prisma, details)

  return res[0]?.id
}
