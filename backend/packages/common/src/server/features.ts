export enum Feature {
  Products = 'products',
  Inventory = 'inventory',
  AffiliateLevels = 'affiliate-levels',
  AffiliateTiers = 'affiliate-tiers',
  PlayBalance = 'play-balance',
  Faucet = 'faucet',
  PlayFaucet = 'play-faucet',
  Vip = 'vip',
  Missions = 'missions',
  Raffles = 'raffles',
  Calendar = 'calendar',
  Lottery = 'lottery',
  Sportsbook = 'sportsbook',
  FairnessV1 = 'fairnessv1',
  FairnessV2 = 'fairnessv2',
  Roulette = 'roulette',
  Crash = 'crash',
  BombRun = 'bomb-run',
  Dice = 'dice',
  Blackjack = 'blackjack',
  Jackpot = 'jackpot',
  SteamP2P = 'steam-p2p',
  Duels = 'duels',
  Coinflip = 'coinflip',
  Slots = 'slots',
  Plinko = 'plinko',
  Mines = 'mines',
  Steam = 'steam',
  Premium = 'premium',
  LevelLoans = 'level-loans',
  Seasonal = 'seasonal',
  ExtraBonus = 'extra-bonus',
  ItemCoinflip = 'item-coinflip',
  Keno = 'keno',
  RouletteSp = 'roulette-sp',
  LossbackCase = 'lossback-case',
  CryptoBalance = 'crypto-balance',
  PasswordlessAuth = 'passwordless-auth',
  DailyBonus = 'daily-bonus',
  AffiliateLeaderboard = 'affiliate-leaderboard',
  AffiliateGiveaway = 'affiliate-giveaway',
  Craps = 'craps',
  Baccarat = 'baccarat',
  MarketingOutreach = 'marketing-outreach',
  BonusBalance = 'bonus-balance',
  FtdBonus = 'ftd-bonus',
  WagerRaces = 'wager-races',

  PremiumRaffles = 'premium-raffles',
}

export const FEATURE_DEPENDENCIES: Partial<Record<Feature, Feature[]>> = {
  [Feature.Inventory]: [Feature.Products],
  [Feature.ItemCoinflip]: [Feature.Inventory],
  [Feature.PlayFaucet]: [Feature.PlayBalance],
  [Feature.RouletteSp]: [Feature.FairnessV2],
}

export const getFeatures = <T>(): T[] => {
  return (process.env.FEATURES?.split(',') ?? []) as T[]
}

export const isFeature = <T>(feature: T) => {
  const features = getFeatures()
  return (
    (features.includes(feature) || features.includes('*')) &&
    !features.includes(`!${feature}`)
  )
}

export const ensureFeatureDependenciesMetFor = <T extends string>(
  features: T[],
  dependenciesList: Partial<Record<T, T[]>>
) => {
  for (const feature of features) {
    const dependencies = dependenciesList[feature as T]
    const isValid = !dependencies || dependencies.every(isFeature)

    if (!isValid) {
      throw new Error(
        `Feature ${feature} missing dependencies: ${dependencies.join()}`
      )
    }
  }
}

export const ensureFeatureDependenciesMet = () => {
  const features = getFeatures<Feature>()
  ensureFeatureDependenciesMetFor<Feature>(features, FEATURE_DEPENDENCIES)
}
