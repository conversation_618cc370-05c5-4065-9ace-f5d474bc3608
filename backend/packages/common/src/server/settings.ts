import { RedisService } from '../nest'
import { getRedis, Redis } from './redis'

export const getPath = (settings: any, pathParts: string[]) => {
  let current = settings
  for (const part of pathParts) {
    current = current?.[part]
  }

  return current
}

export const getSettings = async (
  document: string,
  redis?: RedisService | Redis
) => {
  if (!redis) redis = getRedis()
  const settings = await redis.getJSON(`settings:${document}`)

  return settings
}

export const getSpecificSetting = async (
  document: string,
  path: string,
  redis?: RedisService | Redis
) => {
  const settings = await getSettings(document, redis)
  const pathParts = path.split('.')
  const current = getPath(settings, pathParts)

  return current
}
