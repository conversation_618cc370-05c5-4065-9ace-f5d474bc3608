import { Prisma } from 'prisma'
import { PrismaAny } from './prisma'

export const BALANCE_PRODUCT_ID = 1

export const getAllProducts = async (
  prisma: PrismaAny,
  where?: Prisma.InventoryProductWhereInput
) => {
  const products = await prisma.inventoryProduct.findMany({
    ...(where && { where }),
    include: {
      tags: { select: { tagId: true } },
    },
  })

  return products.map((product) => {
    const tagIds = product.tags.map((tag) => tag.tagId)
    delete product.tags

    return { ...product, tagIds }
  })
}
