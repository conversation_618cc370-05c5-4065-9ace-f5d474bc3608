import { BAD_WORDS } from '../constants/bad-words'

export type FilterContext = 'chat' | 'case'

const CONTEXT_WORDS: Record<FilterContext, (keyof typeof BAD_WORDS)[]> = {
  chat: ['racist', 'nazism'],
  case: ['racist', 'nazism'],
}

const getFlatContextWords = (context: string) => {
  const wordsSet = new Set<string>()

  CONTEXT_WORDS[context].forEach((genre) => {
    BAD_WORDS[genre].forEach((word) => {
      wordsSet.add(word)
    })
  })
  return wordsSet
}

const WORD_CACHE: Record<FilterContext, Set<string>> = {
  chat: getFlatContextWords('chat'),
  case: getFlatContextWords('case'),
}

const normalize = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/0/g, 'o')
    .replace(/1/g, 'i')
    .replace(/3/g, 'e')
    .replace(/[4@]/g, 'a')
    .replace(/[5$]/g, 's')
    .replace(/7/g, 't')
    .replace(/[^a-z]/g, '')
}

export const containsBadWords = (
  text: string,
  context: FilterContext
): boolean => {
  const input = normalize(text)
  const words = WORD_CACHE[context]

  for (const word of words) {
    if (input.includes(word)) return true
  }

  return false
}
