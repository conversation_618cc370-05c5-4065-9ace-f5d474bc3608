import { Prisma, UserBadge, AffiliateCode } from '@prisma/client'
import { Float, Int } from 'packages/types/src'
import { PrismaTx } from '../prisma'
import { affiliateTiers } from './affiliate-tiers'
import { getEventQueue } from '../event-queue'
import { REWARD_RECEIVE_EVENT, RewardReceiveEvent, RewardType } from '../events'

export interface HandleAffiliateBetOptions {
  tx: PrismaTx
  userId: Int
  wagerAmount: Int
  affiliateEarnings?: Float
  isRevert?: boolean
}

export const handleAffiliateBet = async (
  options: HandleAffiliateBetOptions
): Promise<Prisma.PrismaPromise<any>[]> => {
  const { tx, userId } = options

  const use = await getUse(tx, userId)
  if (!use || !use.code) {
    return []
  }

  if (use.user.badge === UserBadge.VERIFIED) {
    return []
  }

  const mainAffiliate = await handleSingleAffiliateBet({
    ...options,
    code: use.code,
  })
  const promises: Prisma.PrismaPromise<any>[] = [...mainAffiliate]

  if (use.isFirstSticky && use.code.userId !== use.codeFirst?.userId) {
    const stickyAffiliate = await handleSingleAffiliateBet({
      ...options,
      code: use.codeFirst,
    })
    promises.push(...stickyAffiliate)
  }

  return promises
}

const getUse = async (tx: PrismaTx, userId: Int) => {
  const codeQuery: Prisma.AffiliateCodeSelect = {
    overrideTier: true,
    totalWagered: true,
    userId: true,
  }

  return await tx.affiliateUse.findUnique({
    where: { userId },
    include: {
      code: { select: codeQuery },
      codeFirst: { select: codeQuery },
      user: {
        select: { badge: true },
      },
    },
  })
}

interface HandleSingleAffiliateBetOptions extends HandleAffiliateBetOptions {
  code: Pick<AffiliateCode, 'overrideTier' | 'totalWagered' | 'userId'>
}

const eventQueue = getEventQueue()

const handleSingleAffiliateBet = async (
  options: HandleSingleAffiliateBetOptions
): Promise<Prisma.PrismaPromise<any>[]> => {
  const { tx, userId, wagerAmount, isRevert, code } = options
  if (!code) {
    return []
  }

  const referredById = code.userId
  const tier = getTier(code)
  const direction = isRevert ? 'decrement' : 'increment'
  const earnings = options.affiliateEarnings ?? wagerAmount * tier.betMultiplier

  const updateCode = tx.affiliateCode.update({
    where: { userId: referredById },
    data: {
      totalWagered: { [direction]: wagerAmount },
      unclaimedEarnings: { [direction]: earnings },
      totalEarnings: { [direction]: earnings },
    },
  })

  const addEarningsEntry = tx.affiliateEarnings.create({
    data: {
      userId,
      referredById,
      wager: wagerAmount,
      earned: earnings,
    },
  })

  await eventQueue.add(
    REWARD_RECEIVE_EVENT,
    new RewardReceiveEvent({
      userId: referredById,
      reward: RewardType.AFFILIATE,
      amount: earnings,
      isRevert,
    }),
    { removeOnComplete: true }
  )

  return [updateCode, addEarningsEntry]
}

const getTier = (
  code: Pick<AffiliateCode, 'overrideTier' | 'totalWagered'>
) => {
  if (code.overrideTier) {
    return affiliateTiers[code.overrideTier]
  }

  const totalWageredNum = Number(code.totalWagered)
  return Object.values(affiliateTiers).findLast((tier) => {
    return tier.wagerRequired <= totalWageredNum
  })
}
