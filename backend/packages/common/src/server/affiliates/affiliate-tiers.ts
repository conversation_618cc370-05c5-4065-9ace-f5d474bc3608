import { Int } from '../..'
import { Feature, isFeature } from '../features'

interface AffiliateTier {
  wagerRequired: Int
  betMultiplier: Int
  isCustom?: boolean
}

const BASE_AFFILIATE_MULTIPLIER = Number(process.env.AFFILIATE_RATE ?? 0.001)

export const affiliateTiers: Record<number, AffiliateTier> = {
  // Tier 1
  0: {
    wagerRequired: 0,
    betMultiplier: BASE_AFFILIATE_MULTIPLIER,
  },
  // Double tier 1
  100: {
    wagerRequired: Number.MAX_SAFE_INTEGER,
    betMultiplier: BASE_AFFILIATE_MULTIPLIER * 2,
    isCustom: true,
  },
  // Fixed 1%
  110: {
    wagerRequired: Number.MAX_SAFE_INTEGER,
    betMultiplier: 0.01,
    isCustom: true,
  },
  // Fixed 1.5%
  120: {
    wagerRequired: Number.MAX_SAFE_INTEGER,
    betMultiplier: 0.015,
    isCustom: true,
  },
}

if (isFeature(Feature.AffiliateTiers)) {
  // Tier 2
  affiliateTiers[20] = {
    wagerRequired: 25_000_00,
    betMultiplier: BASE_AFFILIATE_MULTIPLIER * 1.5,
  }

  // Tier 3
  affiliateTiers[30] = {
    wagerRequired: 50_000_00,
    betMultiplier: BASE_AFFILIATE_MULTIPLIER * 2,
  }

  // Tier 4
  affiliateTiers[40] = {
    wagerRequired: 100_000_00,
    betMultiplier: BASE_AFFILIATE_MULTIPLIER * 2.5,
  }

  // Tier 5
  affiliateTiers[50] = {
    wagerRequired: 250_000_00,
    betMultiplier: BASE_AFFILIATE_MULTIPLIER * 3,
  }

  // Double tier 5
  affiliateTiers[100] = {
    wagerRequired: Number.MAX_SAFE_INTEGER,
    betMultiplier: affiliateTiers[50].betMultiplier * 2,
    isCustom: true,
  }

  // Triple tier 5
  affiliateTiers[110] = {
    wagerRequired: Number.MAX_SAFE_INTEGER,
    betMultiplier: affiliateTiers[50].betMultiplier * 3,
    isCustom: true,
  }

  // Quadriple tier 5
  affiliateTiers[120] = {
    wagerRequired: Number.MAX_SAFE_INTEGER,
    betMultiplier: affiliateTiers[50].betMultiplier * 4,
    isCustom: true,
  }

  // Quintuple tier 5
  affiliateTiers[130] = {
    wagerRequired: Number.MAX_SAFE_INTEGER,
    betMultiplier: affiliateTiers[50].betMultiplier * 5,
    isCustom: true,
  }
}
