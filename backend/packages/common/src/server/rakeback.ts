import { RakebackType } from '@prisma/client'
import { Float, Int, <PERSON>is } from '../types'
import { PrismaTx } from './prisma'
import { getSpecificSetting } from './settings'
import { MINUTE_MS } from '../constants'
import { deepmerge } from 'deepmerge-ts'
import { getEventQueue } from './event-queue'
import { REWARD_RECEIVE_EVENT, RewardReceiveEvent } from './events'

interface RakebackConfig {
  multiplier: Float
  timeout: Millis
  nextRedemptionDate: (lastClaim: Date, now: Date) => Date
}

const cache = new Map()

export type RakebackModesConfig = Record<RakebackType, RakebackConfig>

export type RakebackMultipliers = Record<
  RakebackType,
  Pick<RakebackConfig, 'multiplier'>
>

const DAY_MS = 24 * 60 * 60 * 1000

export const rakebackConfig: RakebackModesConfig = {
  [RakebackType.DAILY]: {
    multiplier: 0.1 / 100,
    timeout: DAY_MS,
    nextRedemptionDate: (lastClaimedAt: Date) => {
      return new Date(lastClaimedAt.getTime() + DAY_MS)
    },
  },
  [RakebackType.WEEKLY]: {
    multiplier: 0.05 / 100,
    timeout: 7 * DAY_MS,
    nextRedemptionDate: (lastClaimedAt: Date, now: Date) => {
      const SUNDAY = 0
      const currentDay = now.getDay()
      const nextSunday = new Date(now.getTime())
      const isClaimedToday = lastClaimedAt.toDateString() === now.toDateString()

      if (currentDay === SUNDAY && !isClaimedToday) {
        nextSunday.setHours(0, 0, 0, 0)
        return nextSunday
      }

      const daysUntilSunday = 7 - currentDay
      nextSunday.setDate(nextSunday.getDate() + daysUntilSunday)
      nextSunday.setHours(0, 0, 0, 0)

      return nextSunday
    },
  },
  [RakebackType.MONTHLY]: {
    multiplier: 0.025 / 100,
    timeout: 30 * DAY_MS,
    nextRedemptionDate: (lastClaimedAt: Date, now: Date) => {
      const currentDay = now.getDate()
      const isClaimedToday = lastClaimedAt.toDateString() === now.toDateString()

      if (currentDay === 1 && !isClaimedToday) {
        const today = new Date(now.getTime())
        today.setHours(0, 0, 0, 0)
        return today
      }

      let year = lastClaimedAt.getFullYear()
      let month = lastClaimedAt.getMonth() + 1
      if (month > 11) {
        month = 0
        year += 1
      }

      const firstOfNextMonth = new Date(year, month, 1)
      firstOfNextMonth.setHours(0, 0, 0, 0)

      return firstOfNextMonth
    },
  },
}

export const getRakebackTypes = (localRakebackConfig?: RakebackModesConfig) =>
  Object.keys(localRakebackConfig ?? rakebackConfig) as RakebackType[]

export const getRakebackConfigEntries = async () => {
  if (cache.has('rakebackConfigEntries')) {
    const cachedEntries: { ttl: number; data: [string, RakebackConfig][] } =
      cache.get('rakebackConfigEntries')
    if (cachedEntries.ttl >= Date.now()) {
      return cachedEntries.data
    }
  }

  const rakebackSettings = (await getSpecificSetting(
    'main-config',
    'rakeback.rakebackConfig'
  )) as RakebackMultipliers

  let configsEntries = Object.entries(rakebackConfig)
  if (rakebackSettings) {
    const mergedConfig = deepmerge(rakebackConfig, rakebackSettings)
    configsEntries = Object.entries(mergedConfig) as any
  }
  cache.set('rakebackConfigEntries', {
    data: configsEntries,
    ttl: Date.now() + MINUTE_MS,
  })

  return configsEntries
}

export const incrementRakeback = async (
  prisma: PrismaTx,
  userId: Int,
  betAmount: Float
) => {
  const rakebackConfigEntries = await getRakebackConfigEntries()
  const eventQueue = getEventQueue()

  const promises = rakebackConfigEntries.flatMap(([typeKey, config]) => {
    const type =
      RakebackType[typeKey as keyof typeof RakebackType] ||
      (typeKey as RakebackType)
    const amount = betAmount * config.multiplier

    const upsertRakeback = prisma.rakeback.upsert({
      where: {
        userId_type: { userId, type },
      },
      update: {
        earnedTotal: { increment: amount },
        claimable: { increment: amount },
      },
      create: {
        userId,
        type,
        earnedTotal: amount,
        claimable: amount,
        lastClaimedAt: new Date(),
      },
    })

    const emitRewardEvent = eventQueue.add(
      REWARD_RECEIVE_EVENT,
      new RewardReceiveEvent({
        userId,
        reward: `rakeback.${type}`,
        amount,
      }),
      { removeOnComplete: true }
    )

    return [upsertRakeback, emitRewardEvent]
  })

  return await Promise.all(promises)
}
