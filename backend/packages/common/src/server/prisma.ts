import { PrismaClient } from '@prisma/client'
import { Int } from '..'

export type PrismaTx = Omit<
  PrismaClient,
  '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
>

export type PrismaAny = PrismaClient | PrismaTx

export const isTxClient = (prisma: PrismaAny) => {
  return (prisma as PrismaClient).$transaction === undefined
}

/**
 * Wraps a function into an interactive transaction
 */
export const runAsTx = async <T, A>(
  prisma: PrismaClient,
  fn: (prismaTx: PrismaTx, ...args: A[]) => Promise<T>,
  ...args: A[]
) => {
  return prisma.$transaction(
    async (txClient) => {
      return fn(txClient, ...args)
    },
    { timeout: 10_000 }
  )
}

/**
 * Returns an identifier of the last entry in a list
 * Used for cursor-based pagination
 * @see https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination
 */
export const getNextCursor = <T>(
  entries: T[],
  cursorKey = 'id'
): Int | null => {
  const lastEntry = entries[entries.length - 1]
  if (!lastEntry) return null

  return lastEntry[cursorKey]
}
