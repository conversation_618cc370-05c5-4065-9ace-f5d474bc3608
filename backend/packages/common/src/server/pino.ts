import { v4 as uuidv4 } from 'uuid'
import pino from 'pino'

const isProd = process.env.NODE_ENV === 'production' && !process.env.IS_STAGING

export const PINO_PRETTY_CONFIG = {
  singleLine: true,
  translateTime: true,
  ignore: 'pid,hostname',
}

const isSafeEnvValue = (value: string | undefined) => {
  if (['true', 'false', undefined].includes(value)) return true
  if (!isNaN(Number(value))) return true
  if (value.length <= 6) return true
  try {
    const url = new URL(value)
    return !!url
  } catch {
    return false
  }
}

const SENSITIVE_ENV_VALUES = Object.values(process.env)
  .map((value) => {
    return !isSafeEnvValue(value) ? value : null
  })
  .filter(Boolean)

const REGEX_SENSITIVE_VALUES = new RegExp(
  SENSITIVE_ENV_VALUES.map(
    (val) =>
      '(?<!\\w)' +
      val.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&') + // Escape special regex characters
      '(?!\\w)'
  ).join('|'),
  'gi'
)

// @see https://stackoverflow.com/questions/4816099/chrome-sendrequest-error-typeerror-converting-circular-structure-to-json
const replacer = (censor: object) => {
  let i = 0

  return (_key, value) => {
    if (
      i !== 0 &&
      typeof censor === 'object' &&
      typeof value === 'object' &&
      censor === value
    )
      return '[Circular]'

    if (i >= 200) return '[Capped]'
    ++i

    return value
  }
}

const censor = (value: unknown) => {
  if (typeof value === 'object') {
    const stringifiedValue = JSON.stringify(value, replacer(value))
    return JSON.parse(
      stringifiedValue.replace(REGEX_SENSITIVE_VALUES, '[CENSORED]')
    )
  }

  if (typeof value !== 'string') return value
  return value.replace(REGEX_SENSITIVE_VALUES, '[CENSORED]')
}

export const logger = pino({
  formatters: {
    bindings: () => ({ instanceId: global.INSTANCE_ID }),
    level: (label) => ({
      level: label,
    }),
  },
  level: isProd ? 'info' : 'debug',
  // useLevelLabels: true,
  redact: {
    paths: ['*'],
    censor: process.env.NODE_ENV === 'development' ? (v) => v : censor,
  },
  ...(!isProd && {
    transport: {
      target: 'pino-pretty',
      options: PINO_PRETTY_CONFIG,
    },
  }),
})

export const PINO_HTTP_CONFIG = {
  quietReqLogger: true,
  genReqId: () => uuidv4(),
  logger,
  serializers: {
    req: (req) => ({
      url: req.url,
      method: req.method,
      ip: req.headers['cf-connecting-ip'],
    }),
    res: (res) => ({
      statusCode: res.statusCode,
      get uid() {
        return res.raw.req.user?.userId
      },
    }),
  },
}
