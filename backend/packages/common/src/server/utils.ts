import { Int } from '../types'
import crypto from 'crypto'
import { logger } from './pino'

export const strBool = (
  value: string | undefined,
  defaultValue = false
): boolean => {
  const lowercaseValue = value?.toLowerCase()
  if (['1', 'true'].includes(lowercaseValue)) return true
  if (['0', 'false'].includes(lowercaseValue)) return false
  return defaultValue
}

const isRustclash = process.env.SITE_TAG === 'rustclash'

export const xpToLevel = isRustclash
  ? (xp: bigint): Int => ~~Math.sqrt(Number(xp) / 1000)
  : (xp: bigint): Int => ~~((Number(xp) / 100) ** (1 / 3))

export const levelToXp = isRustclash
  ? (level: Int): bigint => BigInt(level) ** 2n * 1000n
  : (level: Int): bigint => BigInt(level) ** 3n * 100n

export const isBonusExempt = (userId: Int): boolean => {
  if (!process.env.BONUS_EXEMPT) return false

  return process.env.BONUS_EXEMPT.split(';').includes(String(userId))
}

export const noop = () => {
  // noop
}

export const generateRandomDigits = (length: Int): string => {
  const buffer = crypto.randomBytes(length)
  const code = buffer.map((byte) => byte % 10).join('')
  return code
}

export const bigintConversion = (bigint: bigint): Int => {
  if (bigint > Number.MAX_SAFE_INTEGER || bigint < Number.MIN_SAFE_INTEGER) {
    logger.warn(`BigInt value ${bigint.toString()} exceeds safe integer ranges`)
  }

  return Number(bigint)
}

export const safeBigintConversion = (bigint: bigint): Int | string => {
  if (bigint < Number.MAX_SAFE_INTEGER && bigint > Number.MIN_SAFE_INTEGER) {
    return Number(bigint)
  }

  return bigint.toString()
}

export const overwriteBigintToJson = () => {
  ;(BigInt.prototype as any).toJSON = function () {
    return safeBigintConversion(this)
  }
}
