import { EventEmitter } from 'events'
import { logger } from './pino'
import { RawData, ServerOptions, WebSocket, WebSocketServer } from 'ws'
import { randomUUID } from 'crypto'
import { WSEvent } from './uws-server'
import assert from 'assert'
import { Logger } from 'pino'
import { AccessJWT, Int } from '../types'
import jwt from 'jsonwebtoken'

export class WSServer extends EventEmitter {
  private userToClients: Map<Int, Set<WSClient>> = new Map()
  private logger = logger.child({ module: 'WS' })
  private wss: WebSocketServer
  private epipeErrorCount = 0
  onlineCountInstance = 0

  constructor(private options: Options) {
    super()
    this.#initialize()
  }

  #initialize() {
    this.wss = new WebSocketServer(this.options.server)
    this.wss.on('connection', this.#handleConnection.bind(this))
    this.#addAuthHandler()
    this.#addPingCheck()
    this.logger.info('WS Server initialized!')
    this.wss.on('close', () =>
      setTimeout(() => {
        logger.warn('Restarting WS Server')
        this.#initialize()
      }, 5000)
    )
  }

  #handleConnection(ws: WSClient) {
    ws.uid = randomUUID()
    ws.getUserData = () => ws
    ws.logger = this.logger.child({ uid: ws.uid })
    ws.isAlive = true

    ws.logger.info('Connection open')
    this.onlineCountInstance++

    this.emit('connect', ws)
    logger.info('wss client size=%s', this.wss.clients.size)

    ws.on('error', (err) => {
      logger.error(err)
    })

    ws.on('pong', () => {
      ws.isAlive = true
    })

    ws.on('close', () => {
      ws.logger.info('Connection closed')
      this.onlineCountInstance--

      if (ws.user) {
        this.userToClients.get(ws.user.userId).delete(ws)
        // TODO: if map is empty, remove the user entry
      }

      this.emit('disconnect', ws)
    })

    ws.on('message', (message) => {
      if (rateLimit(ws)) {
        ws.logger.info('Closing, ratelimited')
        ws.close()
        return
      }

      const event = this.#parseRawMessage(message)
      if (!event) {
        return
      }

      const [op] = event
      const ctx = this.#buildCtx(ws, event)

      const blacklistedOps = ['connect', 'disconnect', 'authorized', 'message']
      if (!blacklistedOps.includes(op)) {
        this.emit(op, ctx)
      }

      this.emit('message', ctx)
    })
  }

  #parseRawMessage(message: RawData): WSEvent | false {
    const text = message.toString()

    try {
      const parsed: WSEvent = JSON.parse(text)
      assert(
        Array.isArray(parsed) &&
          parsed.length <= 2 &&
          typeof parsed[0] === 'string',
        'Invalid message format'
      )

      return parsed
    } catch (err) {
      return false
    }
  }

  #buildCtx(ws: WSClient, event: WSEvent) {
    const [op, payload] = event

    const reply = async (response: WSEvent | any) => {
      if (Array.isArray(response)) {
        return this.send(ws, response as WSEvent)
      }

      return this.send(ws, [op, response])
    }

    const error = async (message: string) => {
      return reply({ success: false, message })
    }

    const context: MessageCtx = { op, payload, ws, reply, error }
    return context
  }

  #addAuthHandler() {
    if (!this.options.jwtSecret) return

    this.on('auth', (ctx: MessageCtx) => {
      if (ctx.ws.getUserData().user) return ctx.reply('invalid_state')
      if (typeof ctx.payload !== 'string') return ctx.reply('invalid_input')

      jwt.verify(
        ctx.payload,
        this.options.jwtSecret,
        (err, jwtPayload: any) => {
          if (err) return ctx.reply('invalid_token')
          const { userId, type, bannedUntil } = jwtPayload

          if (!['access', 'pass'].includes(type))
            return ctx.reply('invalid_type')
          if (bannedUntil > Date.now()) return ctx.reply('banned')

          if (type === 'pass' && jwtPayload.scope !== 'ws') {
            return ctx.reply('invalid_scope')
          }

          ctx.ws.getUserData().user = jwtPayload

          if (!this.userToClients.has(userId)) {
            this.userToClients.set(userId, new Set())
          }
          this.userToClients.get(userId).add(ctx.ws)

          ctx.reply(jwtPayload)

          this.emit('authorized', ctx.ws)
        }
      )
    })
  }

  #addPingCheck() {
    const interval = setInterval(() => {
      this.wss.clients.forEach((ws: WSClient) => {
        if (!ws.isAlive) {
          ws.logger.info('Did not respond to ping, closing')
          return ws.close()
        }

        ws.isAlive = false
        ws.ping()
      })
    }, 30_000)

    this.wss.on('close', () => {
      clearInterval(interval)
    })
  }

  send(ws: WebSocket, event: WSEvent) {
    if (ws.readyState !== WebSocket.OPEN) {
      return
    }

    ws.send(JSON.stringify(event), (err) => {
      if (err) {
        this.logger.warn(
          'Failed to send, closing connection err=%s',
          err.message
        )
        ws.close()

        if (err.message.includes('EPIPE')) {
          this.epipeErrorCount += 1
          logger.warn('EPIPE error, count=%s', this.epipeErrorCount)

          if (this.epipeErrorCount > 20) {
            logger.warn('EPIPE limit reached, shutting down')
            this.shutdown(1)
          } else {
            setTimeout(() => {
              this.epipeErrorCount -= 1
            }, 300_000)
          }
        }
      }
    })
  }

  broadcast(event: WSEvent) {
    this.wss.clients.forEach((ws) => {
      this.send(ws, event)
    })
  }

  toUser(userId: Int, event: WSEvent) {
    if (!this.userToClients.has(userId)) {
      return
    }

    this.userToClients.get(userId).forEach((ws) => {
      this.send(ws, event)
    })
  }

  setUnderAttack(isUnderAttack: boolean) {
    this.logger.info('Setting under attack uam=%s', isUnderAttack)
    // TODO
  }

  shutdown(code: Int) {
    this.wss.close()
    setTimeout(() => {
      this.logger.info('Exiting!')
      process.exit(code)
    }, 5_000)
  }
}

export interface WSClient extends WebSocket, Record<any, any> {
  uid: string
  logger: Logger
  getUserData: () => WSClient
  user?: AccessJWT
  subscribedTopics: string[]
  publicEventHandler: (e: any) => void
  battleEventHandler: (e: any) => void
  isAlive: boolean
}

export interface Options {
  server: ServerOptions
  jwtSecret: string
}

export interface MessageCtx<T = any> {
  op: string
  payload?: T
  ws: WSClient
  reply: <T>(wsEvent: WSEvent<T> | T) => void
  error: (message: string) => void
}

const RateLimit = (limit: number, interval: number) => {
  let now = 0
  setInterval(() => ++now, interval)

  const last = Symbol('last') as unknown as string
  const count = Symbol('count') as unknown as string

  return (ws: WSClient) => {
    if (ws[last] !== now) {
      ws[last] = now
      ws[count] = 1
    } else {
      return ++ws[count] > limit
    }
  }
}

const rateLimit = RateLimit(5 * 10, 1000 * 10)
