import { Float, Int } from 'packages/types/src'

export const REWARD_RECEIVE_EVENT = 'user.reward-receive'
export const REWARD_CLAIM_EVENT = 'user.reward-claim'

export const RewardType = {
  RAKEBACK_DAILY: 'rakeback.DAILY',
  RAKEBACK_WEEKLY: 'rakeback.WEEKLY',
  RAKEBACK_MONTHLY: 'rakeback.MONTHLY',
  AFFILIATE: 'affiliate.earnings',
  CASES: 'cases.commission',
  LEADERBOARD: 'leaderboard.race',
  AF_LEADERBOARD: 'affiliate.leaderboard',
  AF_GIVEAWAY: 'affiliate.giveaway',
} as const
export type RewardType = (typeof RewardType)[keyof typeof RewardType]

class RewardEvent {
  userId: Int
  reward: RewardType
  date?: Date = new Date()
  isRevert?: boolean = false
}

export class RewardReceiveEvent extends RewardEvent {
  amount: Float

  constructor(data: RewardReceiveEvent) {
    super()
    Object.assign(this, data)
  }
}

export class RewardClaimEvent extends RewardEvent {
  constructor(data: RewardClaimEvent) {
    super()
    Object.assign(this, data)
  }
}
