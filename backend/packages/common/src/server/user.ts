import { Int } from '../types'
import { PrismaAny } from './prisma'

export interface UserRestrictions {
  isLevelLoanActive: boolean
  wagerFromGift: Int
}

export const getUserRestrictions = async (
  prisma: PrismaAny,
  userId: Int
): Promise<UserRestrictions> => {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      restrictions: true,
      levelLoan: true,
    },
  })

  return {
    isLevelLoanActive: user.levelLoan?.isActive ?? false,
    wagerFromGift: user.restrictions?.wagerFromGift ?? 0,
  }
}
