import Bull from 'bull'

export const EVENT_QUEUE_NAME = '{global-event-queue}'

class EventQueueSingleton {
  private static instance: Bull.Queue

  static getInstance(): Bull.Queue {
    if (!EventQueueSingleton.instance) {
      EventQueueSingleton.instance = new Bull(
        EVENT_QUEUE_NAME,
        process.env.REDIS_URL,
        {
          defaultJobOptions: {
            removeOnComplete: true,
          },
        }
      )
    }
    return EventQueueSingleton.instance
  }
}

export const getEventQueue = () => EventQueueSingleton.getInstance()
