export const printCsv = <T>(
  data: T[],
  transforms: Partial<Record<keyof T, (value: any) => any>>
) => {
  const keys = Object.keys(transforms)
  let output = keys.map(toString).join(',') + '\n'

  for (const row of data) {
    for (const key of keys) {
      output += transforms[key](row[key])

      if (key !== keys[keys.length - 1]) {
        output += ','
      }
    }
    output += '\n'
  }

  return output
}

export const toString = (value: any) => `"${value}"`
export const toDate = (value: any) => new Date(value).toISOString()
export const toFloatCurrency = (value: any) => (value / 100).toFixed(2)
