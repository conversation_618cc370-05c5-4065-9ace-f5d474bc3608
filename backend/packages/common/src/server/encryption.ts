/**
 * This module encrypts and decrypts data using AES-256-CBC.
 * IVs are stored as hex strings because of fixed byte length
 * For cipher text we use base64 encoding as it's more compact
 *
 * - Encryption key defaults to `process.env.AES_ENCRYPTION_KEY`
 * - Keys must be 16/24/32 bytes long
 */

import aesjs from 'aes-js'
import assert from 'assert'
import { createHash } from 'crypto'

const CBC_PREFIX = 'aes'
const DELIMITER = '|'

export const getKeyFromPassword = (password: string): Uint8Array => {
  const sliced = password.slice(0, 32)

  return aesjs.utils.utf8.toBytes(sliced)
}

const defaultKey = getKeyFromPassword(
  process.env.AES_ENCRYPTION_KEY ?? 'ClashPlaceholderPassword'
)

export const encrypt = (text: string, key?: Uint8Array) => {
  if (!key) {
    key = defaultKey
  }

  const paddedText = text.padStart(Math.ceil(text.length / 16) * 16)
  const bytes = aesjs.utils.utf8.toBytes(paddedText)
  const [iv, ivHex] = generateSyntheticIv(text)

  // eslint-disable-next-line new-cap
  const aesCbc = new aesjs.ModeOfOperation.cbc(key, iv)
  const encrypted = aesCbc.encrypt(bytes)
  const encryptedB64 = toBase64(encrypted)

  return [CBC_PREFIX, ivHex, encryptedB64].join(DELIMITER)
}

/**
 * Generates a synthetic initialization vector from plaintext hash
 * It must be 16 bytes
 */
const generateSyntheticIv = (text: string): [Uint8Array, string] => {
  const hexHash = createHash('sha256').update(text).digest('hex')
  const ivHex = hexHash.slice(0, 16)
  const ivBytes = aesjs.utils.utf8.toBytes(ivHex)

  return [ivBytes, ivHex]
}

export const decrypt = (encryptedString: string, key?: Uint8Array) => {
  if (!isEncrypted(encryptedString)) {
    return encryptedString
  }

  if (!key) {
    key = defaultKey
  }

  const [prefix, ivHex, encryptedBase] = encryptedString.split(DELIMITER)
  assert(prefix === CBC_PREFIX, 'invalid_prefix')
  const iv = aesjs.utils.utf8.toBytes(ivHex)
  const encryptedBytes = fromBase64(encryptedBase)

  // eslint-disable-next-line new-cap
  const aesCbc = new aesjs.ModeOfOperation.cbc(key, iv)
  const decryptedBytes = aesCbc.decrypt(encryptedBytes)
  const decrypted = aesjs.utils.utf8.fromBytes(decryptedBytes)

  return decrypted.trimStart()
}

export const isEncrypted = (str: string): boolean => {
  return str.startsWith(CBC_PREFIX + DELIMITER)
}

const toBase64 = (text: Uint8Array): string => {
  return Buffer.from(text).toString('base64')
}

const fromBase64 = (text: string): Buffer => {
  return Buffer.from(text, 'base64')
}
