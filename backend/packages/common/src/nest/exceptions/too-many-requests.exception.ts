import { HttpException, HttpExceptionOptions, HttpStatus } from '@nestjs/common'

export class TooManyRequestsException extends HttpException {
  constructor(
    objectOrError?: any,
    descriptionOrOptions: string | HttpExceptionOptions = 'Too Many Requests'
  ) {
    const { description, httpExceptionOptions } =
      HttpException.extractDescriptionAndOptionsFrom(descriptionOrOptions)

    super(
      HttpException.createBody(
        objectOrError,
        description!,
        HttpStatus.TOO_MANY_REQUESTS
      ),
      HttpStatus.TOO_MANY_REQUESTS,
      httpExceptionOptions
    )
  }
}
