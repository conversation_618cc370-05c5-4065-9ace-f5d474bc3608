import { Injectable, OnModuleInit } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { PrismaClient } from '@prisma/client'
import { DEBUG_LOG_TYPES } from './prisma.constants'
import { withOptimize } from '@prisma/extension-optimize'

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit {
  private _main: PrismaClient
  private _direct: PrismaClient
  private _read: PrismaClient
  private _readSlow: PrismaClient

  constructor(private readonly config: ConfigService) {
    super({
      log: config.get('debugLogging') ? DEBUG_LOG_TYPES : [],
    })
    this.#init()
  }

  private createClientInstance(url: string) {
    const logTypes = this.config.get('debugLogging') ? DEBUG_LOG_TYPES : []
    const optimizeKey = process.env.PRISMA_OPTIMIZE_API_KEY
    const prismaOptions = {
      log: logTypes,
      datasources: {
        db: { url },
      },
    }

    if (optimizeKey && process.env.NODE_ENV === 'development') {
      return new PrismaClient(prismaOptions).$extends(
        withOptimize({ apiKey: optimizeKey })
      ) as PrismaClient
    }

    return new PrismaClient(prismaOptions)
  }

  private getMainInstance() {
    if (!this._main) {
      this._main = this.createClientInstance(process.env.DATABASE_URL)
      this._main.$connect()
    }
    return this._main
  }

  private getClient(client: PrismaClient) {
    if (!client) {
      return this.getMainInstance()
    }
    return client
  }

  /**
   * Exports a special instance of PrismaClient connected directly
   * to a database, instead of a connection proxy (like PGBouncer)
   *
   * Used for speed-critical operations or long transactions
   */
  get direct() {
    return this.getClient(this._direct)
  }

  /**
   * Exports a special instance of PrismaClient connected to a read replica,
   * where data may be delayed by up to 40 seconds.
   *
   * Used for non-critical queries that can tolerate stale data and taking up to 30s,
   * reducing load on the primary database and improving read performance.
   */
  get read() {
    return this.getClient(this._read)
  }

  /**
   * Exports a special instance of PrismaClient,
   * where data may be delayed by up to 15 minutes.
   *
   * Used for long running queries that can tolerate stale data
   */
  get readSlow() {
    return this.getClient(this._readSlow)
  }

  #init() {
    if (process.env.DATABASE_URL_READ) {
      this._read = this.createClientInstance(process.env.DATABASE_URL_READ)
    }

    if (process.env.DATABASE_URL_READ_SLOW) {
      this._readSlow = this.createClientInstance(
        process.env.DATABASE_URL_READ_SLOW
      )
    }
  }

  async onModuleInit() {
    await this.$connect()
    if (this._direct) {
      await this._direct.$connect()
    }
    if (this._read) {
      await this._read.$connect()
    }
    if (this._readSlow) {
      await this._readSlow.$connect()
    }
  }

  async getMetrics() {
    const getNormal = this.$metrics.json()
    const getDirect = this.direct.$metrics.json()
    const getRead = this.read.$metrics.json()
    const getReadSlow = this.readSlow.$metrics.json()

    const [normal, direct, read, readSlow] = await Promise.all([
      getNormal,
      getDirect,
      getRead,
      getReadSlow,
    ])

    return { normal, direct, read, readSlow }
  }

  async getPrometheusMetrics() {
    const getNormal = this.$metrics.prometheus({
      globalLabels: {
        type: 'prisma',
        prisma_connection_name: 'normal',
      },
    })
    const getDirect = this.direct.$metrics.prometheus({
      globalLabels: {
        type: 'prisma',
        prisma_connection_name: 'direct',
      },
    })
    const getRead = this.read.$metrics.prometheus({
      globalLabels: {
        type: 'prisma',
        prisma_connection_name: 'read',
      },
    })
    const getReadSlow = this.readSlow.$metrics.prometheus({
      globalLabels: {
        type: 'prisma',
        prisma_connection_name: 'readSlow',
      },
    })

    const [normal, direct, read, readSlow] = await Promise.all([
      getNormal,
      getDirect,
      getRead,
      getReadSlow,
    ])

    return normal + direct + read + readSlow
  }
}
