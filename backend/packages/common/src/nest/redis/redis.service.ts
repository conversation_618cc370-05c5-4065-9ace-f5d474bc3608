import { HOUR_S, TooManyRequestsException, isRedisReplica } from '../../index'
import { Int } from '@crashgg/types/dist'
import { Injectable, Logger, ServiceUnavailableException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { Redlock } from '@sesamecare-oss/redlock'
import { warn } from 'console'
import { Redis, RedisOptions } from 'ioredis'
import { Counter, Gauge, Histogram } from 'prom-client'
import { EventEmitter } from 'stream'

export enum LockMode {
  Default = 'default',
  Fast = 'fast',
  LongPoll = 'long',
}

@Injectable()
export class RedisService extends EventEmitter {
  client: Redis
  clientSub: Redis
  clientLock: Redis
  clientCache: Redis
  private redlock: Record<LockMode, Redlock>
  private psubscribes: Record<string, number> = {}
  private logger = new Logger(RedisService.name)
  static subOptions: RedisOptions = {
    maxRetriesPerRequest: null,
    enableReadyCheck: false,
  }

  private namedClients: Record<string, Redis> = {}

  private readonly prometheus = {
    cacheRequests: new Counter({
      name: 'redis_cache_requests_total',
      help: 'Total Redis cache JSON requests',
    }),
    cacheHits: new Counter({
      name: 'redis_cache_hits_total',
      help: 'Total Redis cache hits',
    }),
    lockWaitDuration: new Histogram({
      name: 'redis_lock_acquire_duration_seconds',
      help: 'Histogram of Redlock acquire durations',
      buckets: [0.01, 0.05, 0.1, 0.25, 0.5, 1, 2],
    }),
    lockPending: new Gauge({
      name: 'redis_pending_locks',
      help: 'Current number of pending Redlock acquire calls',
    }),
  }

  constructor(private config: ConfigService) {
    super()

    const redisUrl = config.get('REDIS_URL')
    const redisLockUrl = config.get('REDIS_URL_LOCK') ?? redisUrl
    this.client = new Redis(redisUrl)
    this.clientSub = new Redis(redisLockUrl, RedisService.subOptions)
    this.clientLock = new Redis(redisLockUrl)
    this.clientCache = new Redis(redisLockUrl)
    this._handleError(this.client)
    this._handleError(this.clientSub)
    this._listenToMessages()

    const _default = new Redlock([this.clientLock as any], {
      retryCount: 20,
      retryDelay: 200,
      retryJitter: 100,
      // 20 * (200 + 100/2) = 5_000
    })
    const fast = new Redlock([this.clientLock as any], {
      retryCount: 33,
      retryDelay: 20,
      retryJitter: 20,
      // 33 * (20 + 20/2) = 990
    })
    const long = new Redlock([this.clientLock as any], {
      retryCount: 30,
      retryDelay: 200,
      retryJitter: 200,
      // 40 * (200 + 200/2) = 12_000
    })

    this.redlock = {
      default: _default,
      fast,
      long,
    }
  }

  _handleError(client: Redis) {
    client.on('connect', async () => {
      const isReplica = await isRedisReplica(client)
      if (isReplica) {
        this.handleRedisReplica()
      }
    })
  }

  handleRedisReplica() {
    this.logger.error('Redis is in replica mode. Exiting')
    process.exit(1)
  }

  getClient(name: string, options: RedisOptions = {}) {
    if (!this.namedClients[name]) {
      const url = this.config.get('REDIS_URL')
      this.namedClients[name] = new Redis(url, options)
      this.namedClients[name].setMaxListeners(100)
    }

    return this.namedClients[name]
  }

  async get(key: string): Promise<string | null> {
    return this.client.get(key)
  }

  async getJSON<T>(key: string, client?: Redis): Promise<T | null> {
    const redis = client || this.client
    const start = Date.now()
    const value: string = await redis.get(key)
    this.logger.log(
      '[timing] get key=%s took=%dms hit=%d',
      key,
      Date.now() - start,
      Number(Boolean(value))
    )
    if (typeof value === 'string' && !['{', '['].includes(value.charAt(0))) {
      return value as T
    }

    if (!value) return null
    return JSON.parse(value)
  }

  async set(
    key: string,
    value: unknown,
    ttlSeconds?: number,
    client?: Redis
  ): Promise<'OK'> {
    const redis = client || this.client
    const encodedValue: any = ['string', 'number'].includes(typeof value)
      ? value
      : JSON.stringify(value)

    try {
      if (ttlSeconds) {
        return await redis.set(key, encodedValue, 'EX', ttlSeconds)
      } else {
        return await redis.set(key, encodedValue)
      }
    } catch (err) {
      this.logger.error('Failed to set', err)
      throw err
    }
  }

  async del(key: string): Promise<number> {
    return this.client.del(key)
  }

  /**
   * @example
   * const lock = await this.redis.lock(['trade:user:123'])
   * await lock.unlock()
   */
  async lock(
    keys: string[],
    mode: LockMode = LockMode.Default,
    ttl = 60_000,
    hashSlot = 'lock'
  ) {
    this.prometheus.lockPending.inc()
    const start = Date.now()

    keys = keys.map((key) => `{${hashSlot}}:${key}`)

    const redlock = this.redlock[mode]
    const lock = await redlock.acquire(keys, ttl).catch((err) => {
      this.logger.warn(
        'Failed to acquire locks=%o message=%s',
        keys,
        err.message
      )
      throw new ServiceUnavailableException('please_try_again_later')
    })

    const lockSetted = Date.now()
    this.prometheus.lockPending.dec()
    this.prometheus.lockWaitDuration.observe((lockSetted - start) / 1000)

    const release = () => {
      if (lockSetted + ttl < Date.now()) {
        this.logger.log('Lock expired, not releasing locks=%o', keys)
        return
      }

      lock.release().catch((err) => {
        this.logger.warn(
          'Failed to release locks=%o message=%s',
          keys,
          err.message
        )
      })
    }

    return { lock, release }
  }

  async blockLock(key: string) {
    await this.set(`lock:b:${key}`, 1, 30).catch(warn)
  }

  async canLock(key: string) {
    const isLocked = await this.get(`lock:b:${key}`)
    return !isLocked
  }

  async getCachedJSON<T>(
    key: string,
    ttlSeconds: Int,
    fetchData: () => Promise<T>,
    skipLock = true
  ): Promise<T> {
    this.prometheus.cacheRequests.inc()

    const cached = await this.getJSON<T>(key, this.clientCache)
    if (cached) {
      this.prometheus.cacheHits.inc()
      return cached
    }

    if (skipLock) {
      const data = await fetchData()
      await this.set(key, data, ttlSeconds, this.clientCache)

      return data
    }

    const cachePrefix = this.config.get('cachePrefix') ?? 'cache'
    const start = Date.now()
    const lock = await this.lock(
      [`${cachePrefix}:${key}`],
      LockMode.Fast,
      10_000,
      'cachelock'
    )
    this.logger.log('[timing] Lock took=%dms', Date.now() - start)
    try {
      const start = Date.now()
      const cached = await this.getJSON<T>(key, this.clientCache)
      this.logger.log('[timing] Cache get took=%dms', Date.now() - start)
      if (cached) {
        return cached
      }

      const data = await fetchData()
      await this.set(key, data, ttlSeconds, this.clientCache)

      return data
    } finally {
      lock.release()
    }
  }

  async throttle(key: string, ttlSeconds: Int) {
    const isThrottled = await this.client.get(key)
    if (isThrottled) return true

    await this.set(key, '1', ttlSeconds)
    return false
  }

  async throttleThrow(key: string, ttlSeconds: Int) {
    const isThrottled = await this.throttle(key, ttlSeconds)
    if (isThrottled) {
      throw new TooManyRequestsException('please_slow_down')
    }
  }

  async psubscribe(channel: string) {
    this.psubscribes[channel] = (this.psubscribes[channel] || 0) + 1

    if (this.psubscribes[channel] === 1) {
      return this.clientSub.psubscribe(channel)
    }
  }

  async punsubscribe(channel: string) {
    this.psubscribes[channel] = (this.psubscribes[channel] || 0) - 1

    if (this.psubscribes[channel] === 0) {
      return this.clientSub.punsubscribe(channel)
    }
  }

  async publish(channel: string, message: any) {
    const encodedMessage: any = ['string', 'number'].includes(typeof message)
      ? message
      : JSON.stringify(message)

    return this.clientCache.publish(channel, encodedMessage)
  }

  async publishPublic(channel: string, message: any) {
    return this.publish(`public:${channel}`, message).catch((err) => {
      this.logger.warn({ err }, `Failed to publish public event=%o`, {
        channel,
        message,
      })
    })
  }

  async wsBroadcast(channel: string, message: any) {
    return this.publish(`ws-broadcast`, { channel, message }).catch(warn)
  }

  async userEvent(userId: Int, event: string, data: any) {
    await this.publish(`user:${userId}:${event}`, data)
  }

  /**
   * This increments a counter, starting at 1. First call sets a TTL,
   * after which the counter expires and is reset to 0.
   */
  async increx(key: string, ttl = HOUR_S) {
    const count = await this.get(key)

    if (!count) await this.set(key, 0, ttl)
    await this.client.incr(key)

    return parseInt(count) || 1
  }

  _listenToMessages() {
    this.clientSub.on('pmessage', (channel, pattern, message) => {
      try {
        message = JSON.parse(message)
      } catch (e) {
        // noop
      }

      this.emit(channel, { channel: pattern, message })
    })
  }
}
