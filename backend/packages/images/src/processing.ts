import sharp from 'sharp'
import FormData from 'form-data'
import fetch from 'node-fetch-commonjs'
import { Float, Int } from '@crashgg/common/types'
import config from './config'

export const getBufferFromUrl = async (url: string) => {
  const response = await fetch(url)
  const buffer = await response.arrayBuffer()
  return buffer
}

export const trimBorderPipe =
  (trimSizePx: Int) => async (inputBuffer: ArrayBuffer) => {
    const { width = 0, height = 0 } = await sharp(inputBuffer).metadata()
    const outputBuffer = await sharp(inputBuffer)
      .extract({
        top: trimSizePx,
        left: trimSizePx,
        width: width - 2 * trimSizePx,
        height: height - 2 * trimSizePx,
      })
      .toBuffer()

    return outputBuffer
  }

export const trimTransparencyPipe = () => async (inputBuffer: ArrayBuffer) => {
  return await sharp(inputBuffer).trim().toBuffer()
}

export const resizePipe =
  (options: sharp.ResizeOptions) => async (inputBuffer: ArrayBuffer) => {
    return await sharp(inputBuffer).resize(options).toBuffer()
  }

export const trimPipe = () => async (inputBuffer: ArrayBuffer) => {
  return await sharp(inputBuffer).trim().toBuffer()
}

export const webpPipe =
  (options: sharp.WebpOptions) => async (inputBuffer: ArrayBuffer) => {
    return await sharp(inputBuffer).webp(options).toBuffer()
  }

interface CropProps {
  top: Float
  left: Float
  right: Float
  bottom: Float
}

export const cropPercentPipe =
  (crop: CropProps) => async (inputBuffer: ArrayBuffer) => {
    const { width = 0, height = 0 } = await sharp(inputBuffer).metadata()

    const newWidth = Math.floor(width * (1 - crop.left - crop.right))
    const newHeight = Math.floor(height * (1 - crop.top - crop.bottom))
    const left = Math.floor(width * crop.left)
    const top = Math.floor(height * crop.top)

    return sharp(inputBuffer)
      .extract({
        left,
        top,
        width: newWidth,
        height: newHeight,
      })
      .toBuffer()
  }

export const removeBgPipe = () => async (inputBuffer: ArrayBuffer) => {
  const form = new FormData()
  form.append('image_file', inputBuffer, { filename: 'input.png' })

  const response = await fetch('https://clipdrop-api.co/remove-background/v1', {
    method: 'POST',
    headers: {
      'x-api-key': config.clipdropKey,
    },
    body: form,
  })

  const outputBuffer = await response.arrayBuffer()
  return outputBuffer
}
