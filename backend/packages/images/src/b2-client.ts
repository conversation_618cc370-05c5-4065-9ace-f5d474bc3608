import { GetObjectCommand, S3Client } from '@aws-sdk/client-s3'

const { B2_REGION, B2_ENDPOINT, B2_APP_KEY_ID, B2_APP_KEY, B2_BUCKET_NAME } =
  process.env

const b2Client = new S3Client({
  region: B2_REGION,
  endpoint: B2_ENDPOINT,
  credentials: {
    accessKeyId: B2_APP_KEY_ID,
    secretAccessKey: B2_APP_KEY,
  },
})

// remove x-amz-checksum-mode for b2 api
const removeChecksumMiddleware =
  () => (next: any, _context: any) => async (args: any) => {
    if (args?.request?.headers?.['x-amz-checksum-mode']) {
      delete args.request.headers['x-amz-checksum-mode']
    }
    return next(args)
  }

b2Client.middlewareStack.addRelativeTo(removeChecksumMiddleware(), {
  relation: 'before',
  toMiddleware: 'awsAuthMiddleware',
  name: 'removeChecksumHeader',
})

export const getB2FileBuffer = async (fileKey: string) => {
  const command = new GetObjectCommand({
    Bucket: B2_BUCKET_NAME,
    Key: fileKey,
  })
  const response = await b2Client.send(command)

  const chunks = []
  for await (const chunk of response.Body as any) {
    chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk))
  }

  return Buffer.concat(chunks)
}

export const getB2FileBufferArray = async (fileKey: string) => {
  const buffer = await getB2FileBuffer(fileKey)
  return buffer.buffer.slice(
    buffer.byteOffset,
    buffer.byteOffset + buffer.byteLength
  )
}

export default b2Client
