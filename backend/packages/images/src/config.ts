import { getB2FileBuffer, getB2FileBufferArray } from './b2-client'
import {
  cropPercentPipe,
  getBufferFromUrl,
  removeBgPipe,
  resizePipe,
  trimBorderPipe,
  trimPipe,
  trimTransparencyPipe,
  webpPipe,
} from './processing'
const { B2_IDX_FILE_KEY } = process.env

export type Pipe = (buffer: ArrayBuffer) => Promise<ArrayBuffer>

export interface Mode {
  tag: string
  getInputBuffer?: (url: string) => Promise<ArrayBuffer>
  validateQuery?: (url: string) => boolean
  pipes: Pipe[]
  extension?: 'webp' | 'png'
}

const getSteamEconomyUrl = (hash: string, size = 360) => {
  return `https://community.cloudflare.steamstatic.com/economy/image/${hash}/${size}fx${size}f`
}

const getStashPathKey = async (name: string) => {
  const buffer = await getB2FileBuffer(B2_IDX_FILE_KEY)
  try {
    const json = JSON.parse(buffer.toString('utf-8'))

    const pathKey = json[name]
    if (!pathKey) {
      throw new Error(`Path key not found for name: ${name}`)
    }

    return pathKey
  } catch (error) {
    throw new Error(`Failed to parse JSON index file: ${error}`)
  }
}

const isTrustedDomain = (query: string) => {
  const hostname = new URL(query).hostname
  try {
    return [
      'media.discordapp.net',
      'cdn.discordapp.com',
      'clash.gg',
      'rustclash.com',
      'dotaclash.com',
      'cdn.skinsmonkey.com',
      'images.stockx.com',
    ].includes(hostname)
  } catch (err) {
    return false
  }
}

const config = {
  port: Number(process.env.PORT) || 7300,
  clipdropKey: process.env.CLIPDROP_KEY,
  adminKey: process.env.ADMIN_KEY,
  modes: [
    {
      tag: 'steam-raw',
      validateQuery(query) {
        return query.length < 512 && !query.includes('/')
      },
      async getInputBuffer(query) {
        const steamUrl = getSteamEconomyUrl(query)
        return getBufferFromUrl(steamUrl)
      },
      pipes: [],
    },
    {
      tag: 'dota',
      validateQuery(query) {
        return query.length < 512 && !query.includes('/')
      },
      async getInputBuffer(query) {
        const steamUrl = getSteamEconomyUrl(query)
        return getBufferFromUrl(steamUrl)
      },
      pipes: [
        trimTransparencyPipe(),
        trimBorderPipe(5),
        removeBgPipe(),
        trimTransparencyPipe(),
      ],
    },
    {
      tag: 'chests',
      validateQuery: isTrustedDomain,
      pipes: [
        cropPercentPipe({ top: 0.12, left: 0.06, right: 0.06, bottom: 0.15 }),
        resizePipe({ width: 224 }),
      ],
    },
    {
      tag: 'cases',
      validateQuery: isTrustedDomain,
      pipes: [resizePipe({ height: 448 }), webpPipe({ nearLossless: true })],
      extension: 'webp',
    },
    {
      tag: 'standard',
      validateQuery: isTrustedDomain,
      pipes: [webpPipe({ nearLossless: true })],
      extension: 'webp',
    },
    {
      tag: 'avatar',
      validateQuery: () => false,
      pipes: [
        resizePipe({ width: 512, height: 512 }),
        webpPipe({ nearLossless: true }),
      ],
      extension: 'webp',
    },
    {
      tag: 'transparent',
      validateQuery: isTrustedDomain,
      pipes: [
        removeBgPipe(),
        trimTransparencyPipe(),
        webpPipe({ nearLossless: true }),
      ],
      extension: 'webp',
    },
    {
      tag: 'e',
      validateQuery(query) {
        return query.length < 512 && !query.includes('/')
      },
      async getInputBuffer(query) {
        const steamUrl = getSteamEconomyUrl(query, 128)
        return getBufferFromUrl(steamUrl)
      },
      pipes: [
        webpPipe({
          quality: 55,
          nearLossless: true,
          effort: 6,
          preset: 'drawing',
        }),
      ],
      extension: 'webp',
    },
    {
      tag: 'eq',
      validateQuery(query) {
        return query.length < 512 && !query.includes('/')
      },
      async getInputBuffer(query) {
        const steamUrl = getSteamEconomyUrl(query, 512)
        return getBufferFromUrl(steamUrl)
      },
      pipes: [
        webpPipe({
          quality: 100,
          lossless: true,
          effort: 6,
          preset: 'drawing',
        }),
      ],
      extension: 'webp',
    },
    {
      tag: 'p',
      validateQuery(query) {
        return query.length < 512 && !query.includes('.')
      },
      async getInputBuffer(query) {
        const url = `https://raw.githubusercontent.com/ByMykel/counter-strike-image-tracker/main/static/panorama/images/econ/${query}.png`
        return getBufferFromUrl(url)
      },
      pipes: [
        resizePipe({ width: 240 }),
        webpPipe({
          quality: 55,
          nearLossless: true,
          effort: 6,
          preset: 'drawing',
        }),
      ],
      extension: 'webp',
    },
    {
      tag: 'pqc',
      validateQuery(query) {
        return query.length < 512 && !query.includes('.')
      },
      async getInputBuffer(query) {
        const url = `https://raw.githubusercontent.com/ByMykel/counter-strike-image-tracker/main/static/panorama/images/econ/${query}.png`
        return getBufferFromUrl(url)
      },
      pipes: [
        webpPipe({
          quality: 90,
          nearLossless: true,
          effort: 6,
          preset: 'drawing',
        }),
        trimPipe(),
      ],
      extension: 'webp',
    },
    {
      tag: 'stash',
      validateQuery(query) {
        return query.length < 512 && !query.includes('.')
      },
      async getInputBuffer(query) {
        const pathKey = await getStashPathKey(query)
        return getB2FileBufferArray(pathKey)
      },
      pipes: [webpPipe({ nearLossless: true })],
      extension: 'webp',
    },
  ] as Mode[],
} as const

export default config
