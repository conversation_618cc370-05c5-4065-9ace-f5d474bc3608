import { logger } from '@crashgg/common'
import config, { Mode } from './config'
import express, { Request, Response } from 'express'
import cors from 'cors'
import helmet from 'helmet'
import assert from 'assert'
import sharp from 'sharp'
import { createHash } from 'crypto'
import { ensureDirExists, existsAsync, getPath } from './filesystem'
import asyncHandler from 'express-async-handler'
import { join } from 'path'
import { unlink } from 'fs/promises'
import { getBufferFromUrl } from './processing'
import fileUpload from 'express-fileupload'

export class ProxyServer {
  private logger = logger.child({ context: 'ProxyServer' })
  private app: ReturnType<typeof express>

  constructor() {
    const app = express()

    app.use(helmet({ crossOriginResourcePolicy: false }))
    app.use(cors())
    app.use(express.json())
    app.use(express.urlencoded({ extended: true }))

    this.app = app
    this.#initializeRoutes()
    this.logger.info('App initialized')
  }

  #initializeRoutes() {
    const handleImageRequest = asyncHandler(this.#handleImageRequest.bind(this))
    this.app.get('/robots.txt', (_req, res) => {
      res.type('text/plain').send('User-agent: *\nAllow: /')
    })
    this.app.get('/favicon.ico', (_req, res) => {
      res.send('')
    })
    this.app.post(
      '/upload',
      this.#adminGuardMiddleware.bind(this),
      fileUpload({ limits: { fileSize: 50 * 1024 ** 2 } }),
      asyncHandler(this.#handleUpload.bind(this))
    )
    this.app.get('/:tag/:query', handleImageRequest)
    this.app.get('/:tag', handleImageRequest)

    const handleImageDelete = asyncHandler(this.#handleImageDelete.bind(this))
    this.app.delete(
      '/:tag/:query',
      this.#adminGuardMiddleware.bind(this),
      handleImageDelete
    )
    this.app.delete(
      '/:tag',
      this.#adminGuardMiddleware.bind(this),
      handleImageDelete
    )

    this.app.use((err, _res, res, next) => {
      this.logger.error(`Error: %s`, err.message)
      console.log(err.stack)
      res.status(500).send('Something went wrong')
      next()
    })
  }

  #adminGuardMiddleware(req: Request, res: Response, next: () => void) {
    const header = req.header('authorization')
    const bodyToken = req.body?.token
    const isValid =
      (header && header === `Bearer ${config.adminKey}`) ||
      bodyToken === config.adminKey
    if (!isValid) {
      res.status(401).send('Unauthorized')
      return
    }

    next()
  }

  async #handleUpload(req: Request, res: Response) {
    const imageRaw = req.files?.image
    const image = Array.isArray(imageRaw) ? imageRaw[0] : imageRaw
    this.logger.info(`Received files ${JSON.stringify(req.files)}`)
    assert(image, 'no_image')
    const mode = this.#getMode(req.body.mode)

    const extension = image.name.split('.').pop()
    const queryKey = `${image.md5}.${extension}`
    const storeKey = createHash('md5').update(queryKey).digest('hex')
    const path = getPath(mode.tag, storeKey, mode.extension)

    const processedImage = await this.#processImage(mode, image.data)
    await ensureDirExists(path)
    await sharp(processedImage).toFile(path)

    const uri = `/${mode.tag}/${queryKey}`
    return res.json({ success: true, uri })
  }

  async #handleImageRequest(req: Request, res: Response) {
    const tag = req.params.tag
    const query = req.params.query || req.query.q
    const modifier = req.query.m ?? ''
    const mode = this.#getMode(tag)
    this.#validateQuery(query, mode)
    const isHash = this.#isHash(query)

    const key = createHash('md5')
      .update(query + modifier)
      .digest('hex')
    const extension = mode.extension ?? 'png'
    const path = getPath(mode.tag, key, extension)
    this.logger.debug('Looking for q+m %s %s', query, modifier)

    if (await existsAsync(path)) {
      this.logger.debug('Found q+m')
      res.sendFile(path, { root: join(__dirname, '..') })
      return
    }

    if (modifier === 'crop') {
      this.logger.info('Cropping q %s', query)
      const ogKey = createHash('md5').update(query).digest('hex')
      const extension = mode.extension ?? 'png'
      const ogPath = getPath(mode.tag, ogKey, extension)
      if (await existsAsync(ogPath)) {
        const image = sharp(ogPath).trim()
        await ensureDirExists(path)
        await image.toFile(path)

        const responseBuffer = await image.toBuffer()
        return res.type(extension).send(responseBuffer)
      }
    }

    if (isHash) {
      return res.status(404).end('Not found')
    }

    // TODO: add mutex mechanism
    this.logger.info(`Generating ${tag} ${query} ${path}`)
    const image = await this.#generateImage(mode, query)
    await ensureDirExists(path)
    await sharp(image).toFile(path)

    const responseBuffer = await sharp(image).toBuffer()
    return res.type(extension).send(responseBuffer)
  }

  async #handleImageDelete(req: Request, res: Response) {
    const { tag, url } = req.params
    this.logger.info(`Remove: ${tag} ${url}`)
    const mode = this.#getMode(tag)
    this.#validateQuery(url, mode)
    const key = createHash('md5').update(url).digest('hex')
    const extension = mode.extension ?? 'png'
    const path = getPath(mode.tag, key, extension)

    if (!(await existsAsync(path))) {
      return res.json({ success: false })
    }

    await unlink(path)
    return res.json({ success: true })
  }

  #isHash(query: string) {
    const parts = query.split('.')
    return parts.length === 2 && parts[0].length === 32 && parts[1].length <= 5
  }

  #validateQuery(query: unknown, mode: Mode): asserts query is string {
    const isValid =
      typeof query === 'string' &&
      query.length < 2048 &&
      query.length > 3 &&
      (this.#isHash(query) ||
        (mode.validateQuery ? mode.validateQuery(query) : true))
    assert(isValid, 'invalid_url')
  }

  #getMode(tag: string): Mode {
    const mode = config.modes.find((m) => m.tag === tag)
    assert(mode, 'mode_not_found')

    return mode
  }

  async #generateImage(mode: Mode, url: string) {
    const buffer = await (mode.getInputBuffer ?? getBufferFromUrl)(url)
    return this.#processImage(mode, buffer)
  }

  async #processImage(mode: Mode, buffer: ArrayBuffer) {
    for (const pipe of mode.pipes) {
      buffer = await pipe(buffer)
    }

    return buffer
  }

  async listen(): Promise<void> {
    return new Promise((resolve) => {
      this.app.listen(config.port, () => {
        this.logger.info(`Listening on port ${config.port}`)
        resolve()
      })
    })
  }
}
