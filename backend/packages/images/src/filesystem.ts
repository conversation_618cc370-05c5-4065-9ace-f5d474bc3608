import { stat, writeFile, mkdir } from 'fs/promises'

export const getPath = (tag: string, key: string, extension: string) => {
  return `./data/${tag}/${key.substring(0, 1)}/${key}.${extension}`
}

export const existsAsync = async (path: string) => {
  try {
    const stats = await stat(path)
    return stats.isFile
  } catch (err) {
    return false
  }
}

export const ensureDirExists = async (filePath: string) => {
  const path = filePath.split('/').slice(0, -1).join('/')
  await mkdir(path, { recursive: true })
}

export const saveAsync = async (path: string, buffer: any) => {
  await writeFile(path, buffer, { encoding: 'binary' })
}
