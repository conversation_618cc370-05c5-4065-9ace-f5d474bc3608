const WebSocket = require('ws')
const jwt = require('jsonwebtoken')

const log = (...args) => {
  console.log(new Date().toLocaleTimeString(), ...args)
}

const createBettor = (userId) => {
  const ws = new WebSocket('ws://localhost:7200')

  const send = (event, data) => {
    log('\x1b[32m', '>>', event, data, '\x1b[0m')
    ws.send(JSON.stringify([event, data]))
  }

  ws.onopen = () => {
    log('open')
    send(
      'auth',
      jwt.sign({ type: 'access', userId, role: 'admin' }, 'moms-spaghetti')
    )
  }

  ws.onerror = (err) => log('error', err)

  ws.onmessage = (msg) => {
    const [event, data] = JSON.parse(msg.data)

    if (event !== 'tick') log(event, data)

    if (event === 'status' && data.state === 'in-progress') {
      setTimeout(() => {
        send('exampleProducts', null)
      }, 200)
    }

    if (event === 'status' && data.state === 'betting') {
      for (let i = 0; i < 2; i++) {
        setTimeout(() => {
          send('placeBet', {
            amount: 100 + i,
            autoCashoutAt: 2 + i * 0.9,
            currency: 'REAL',
          })
        }, i * 200)
      }
    }
  }
}

createBettor(1)
