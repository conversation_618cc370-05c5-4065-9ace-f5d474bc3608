import {
  MessageCtx,
  W<PERSON><PERSON>,
  W<PERSON><PERSON>r,
  logger,
  getRedis,
  C<PERSON><PERSON><PERSON><PERSON><PERSON>,
  sleep,
  overwriteBigint<PERSON>o<PERSON><PERSON>,
  getRedisLock,
} from '@crashgg/common'
import config from './config'
import crash from './crash'
import { <PERSON><PERSON>ashout, CrashPlaceBet, <PERSON>is, Int, Currency } from '@crashgg/types'
import { CrashWatchdog } from './watchdog'

const wss = new WSServer({
  server: {
    port: config.port,
  },
  jwtSecret: config.jwtSecret,
})

overwriteBigintToJson()
const redis = getRedis()
const redisLock = getRedisLock()
redis.setCustomErrorHandler((error) => {
  logger.error('Redis error', error.message)
})

const watchdog = new CrashWatchdog(crash)
watchdog.listen()

const userConnections = {}

wss.on('status', ({ reply }) => {
  reply(crash.status)
})

wss.on('init', ({ reply }) => {
  reply(crash.init)
})

wss.on('myBets', ({ ws, reply }) => {
  if (ws.user) {
    reply(['myBets', crash.getUserBets(ws.user.userId)])
  }
})

wss.on('exampleProducts', ({ reply }) => {
  if (config.isItemBased || config.hasItemAnimation) {
    reply(['exampleProducts', crash.getExampleProductsPayload()])
  }
})

wss.on('ping:serverTime', ({ reply }) => {
  reply(Date.now())
})

wss.on('ping:pong', ({ payload, reply }): MessageCtx<Millis> => {
  if (isNaN(payload)) return

  reply(Date.now() - payload)
})

wss.on(
  'placeBet',
  async ({ payload, ws, reply }: MessageCtx<CrashPlaceBet>) => {
    const user = ws.getUserData().user
    if (!user) return reply('Unauthorized')
    if (!payload.amount) return reply('Missing amount')
    if (payload.amount < 1) return reply('Bet amount must be at least 0.01')
    if (payload.amount > config.maxBet)
      return reply(`Max bet is ${(config.maxBet / 100).toFixed(2)}`)

    if (!CURRENCIES.includes(payload.currency)) return reply('Invalid currency')

    if (typeof payload.autoCashoutAt !== 'undefined') {
      payload.autoCashoutAt = ~~(payload.autoCashoutAt * 100) / 100
      if (payload.autoCashoutAt < 1.01)
        return reply('Auto cashout must be at least 1.01')
      if (payload.autoCashoutAt > 1_000_000)
        return reply('Auto cashout must be at most 1 000 000')
    }

    let lock
    try {
      logger.info(`${user.userId} acquiring lock`)
      lock = await redisLock.lock([`charge:${user.userId}`], 3000)
      logger.info(`${user.userId} lock acquired`)

      const bet = await crash.placeBet(payload, user.userId)
      logger.info(`${user.userId} bet placed!`)
      incrementRain(payload.amount, payload.currency)

      logger.info(`${user.userId} replying`)
      reply({ success: true, bet })
    } catch (err) {
      logger.info(`${user.userId} bet failed ${err.message} ${err}`)
      reply(err.message)
    } finally {
      logger.info(`${user.userId} releasing lock`)
      lock?.release()
    }
  }
)

wss.on('cashout', ({ payload, ws, reply }: MessageCtx<DoCashout>) => {
  const user = ws.getUserData().user
  if (!user) return reply('Unauthorized')
  if (!payload?.betId) return reply('Missing bet ID')
  if (payload.betId !== ~~payload.betId) return reply('Missing bet ID')

  crash.cashout(payload.betId, user.userId).catch(() => {
    reply('Failed to cashout')
  })
})

wss.on('authorized', (ws: WSClient) => {
  const id = ws.getUserData().user.userId
  userConnections[id] = (userConnections[id] || 0) + 1
})

wss.on('disconnect', (ws: WSClient) => {
  const user = ws.getUserData().user
  if (user) {
    const id = user.userId

    userConnections[id] -= 1
    if (userConnections[id] === 0) {
      delete userConnections[id]
      crash.cashoutAllUserBets(id)
    }
  }
})

wss.on('admin-pause', async ({ ws, reply }: MessageCtx) => {
  const user = ws.getUserData().user
  if (!user || user.role !== 'admin')
    return reply(`Unauthorized (${user.role})`)

  let isPaused = false
  while (!isPaused) {
    isPaused = crash.pause()

    if (!isPaused) {
      reply('Waiting to pause...')
      await sleep(1000)
    }
  }
  reply('Paused')
})

wss.on('admin-resume', ({ ws, reply }: MessageCtx) => {
  const user = ws.getUserData().user
  if (!user || user.role !== 'admin')
    return reply(`Unauthorized (${user.role})`)

  crash.resume().catch(() => {
    reply('Failed to resume')
  })
})

crash.on('broadcast', (wsEvent) => {
  wss.broadcast(wsEvent)
})

crash.on('userEvent', (userId, wsEvent) => {
  wss.toUser(userId, wsEvent)
})

const incrementRain = async (betAmount: Int, betCurrency: Currency) => {
  if (betCurrency !== Currency.REAL) return
  try {
    {
      const increment = betAmount * config.rainMultiplier
      const newPot = await redis.client.incrbyfloat('rain:amount', increment)
      redis.publishPublic('rain', { pot: +newPot })
    }
    {
      const increment = betAmount * config.lotteryMultiplier
      const newPot = await redis.client.incrbyfloat('lottery:pot', increment)
      redis.publishPublic('lottery:pot', { pot: +newPot })
    }
  } catch (err) {
    logger.warn({ err }, 'failed to increment rain')
  }
}
