import assert from 'assert'
import { EventEmitter } from 'events'
import {
  getNewGameSeed,
  handleBetFinish,
  handleCrashWin,
  handleCrashItemWin,
  insertGame,
  placeCrashBet,
  setGameProfit,
  handleBetFeed,
  getSortedItemsWithRetry,
} from './database'
import {
  CrashBet,
  BetState,
  GameState,
  GameStatus,
  Cashout,
  CrashPlaceBet,
  Tick,
  Float,
  Int,
  Millis,
  CrashHistory,
  Currency,
  CrashProduct,
} from '@crashgg/types'
import { getCrashPoint, getGameDuration, growthFn } from './utils'
import config from './config'
import {
  sleep,
  logger,
  registerMissionEvent,
  isFeature,
  Feature,
  ensureFeatureDependenciesMet,
  REAL_CURRENCIES,
} from '@crashgg/common'
import { User } from '@prisma/client'
import retry, { type Options } from 'async-retry'

const LONG_RETRY: Options = {
  factor: 1.5,
  retries: 50,
  minTimeout: 100,
}

export class Crash extends EventEmitter {
  private logger = logger
  private state = GameState.Ended
  private gameId: Int = null
  private seed: string = null
  private crashAt: Float = null
  private startedAt: Millis = null
  private duration: Int = null
  private pendingBetsCount: Int = 0
  private bets: CrashBet[] = []
  // An array of manual cashouts that will be emitted in the next tick
  private pendingCashouts: Cashout[] = []
  private newGameTimeout: NodeJS.Timeout
  private houseProfit: Int = 0
  private maxTotalWin: Int = config.maxTotalWin
  private totalWon: Int = 0
  private openBet: Int = 0
  private forceCashoutAt: Float = Infinity
  private gameHistory: CrashHistory[] = []

  private inventoryProducts: CrashProduct[]
  private balanceProduct: CrashProduct

  constructor() {
    super()
    ensureFeatureDependenciesMet()
    this.#ensureRequiredFeatures()

    this.#newGame()
  }

  /**
   * Public game status, can be requested anytime
   */
  get status(): GameStatus {
    return {
      gameId: this.gameId,
      state: this.state,
      bets: this.bets.map(({ possibleProducts: _, ...bet }) => bet),
      startedAt: this.startedAt,
      now: Date.now(),
      ...(this.state === GameState.Ended && { at: this.crashAt }),
    }
  }

  /**
   * Get all details needed to initialize a client
   */
  get init() {
    return {
      gameHistory: this.gameHistory,
      maxTotalWin: this.maxTotalWin,
      maxBet: config.maxBet,
      clientSeed: config.clientSeed,
      isInventoryBased: config.isItemBased,
      hasItemAnimation: config.hasItemAnimation,
    }
  }

  get #at() {
    if (this.state !== GameState.InProgress) return -1

    const elapsed = Date.now() - this.startedAt
    return Math.min(growthFn(elapsed), this.crashAt)
  }

  #ensureRequiredFeatures() {
    if (config.isItemBased) {
      if (!isFeature(Feature.Inventory)) {
        throw new Error('Enable Inventory feature for item based crash game')
      }
    }
  }

  getUserBets(userId: Int) {
    return this.bets.filter((bet) => bet.user.id === userId)
  }

  async #newGame() {
    assert([GameState.Ended, GameState.Paused].includes(this.state))

    const { gameId, seed } = await retry(
      async () => await getNewGameSeed(this.gameId),
      LONG_RETRY
    )
    const crashAt = getCrashPoint(seed)
    this.duration = getGameDuration(crashAt)
    this.gameId = gameId
    this.bets = []
    this.pendingCashouts = []
    this.maxTotalWin = config.maxTotalWin
    this.totalWon = 0
    this.openBet = 0
    this.houseProfit = 0
    this.forceCashoutAt = Infinity

    await retry(
      async () => await insertGame({ id: gameId, seed, crashAt }),
      LONG_RETRY
    )

    this.logger = logger.child({ module: 'Crash', gameId })
    this.logger.info('Starting game crashAt=%d', this.crashAt)

    const products = await getSortedItemsWithRetry()
    this.balanceProduct = products.balance
    this.inventoryProducts = products.rest

    this.startedAt = Date.now()
    this.#setState(GameState.Betting)
    // Seed and crash point must be set while in betting phase.
    // While game state is ended, it leaks the crash point.
    this.seed = seed
    this.crashAt = crashAt

    setTimeout(this.#blockGame.bind(this), config.betTime)
  }

  async #blockGame() {
    assert(this.state === GameState.Betting)

    this.#setState(GameState.Blocked)

    let i = 0
    while (this.pendingBetsCount > 0) {
      this.logger.info(`Processing bets remaining=%d`, this.pendingBetsCount)
      await sleep(100)
      if (i++ > 300) {
        this.logger.warn(
          'Pending bets count is still high, something is wrong. pendingBetsCount=%d',
          this.pendingBetsCount
        )
        this.pendingBetsCount -= 1
        break
      }
    }

    this.#startGame()
  }

  getExampleProductsPayload() {
    // get highest bet else fallback to 100_00 bet
    const EXAMPLE_BET_AMOUNT = 100_00
    let payload = {
      user: null,
      betId: null,
      amount: EXAMPLE_BET_AMOUNT,
      possibleProducts: this.#generatePossibleProducts(EXAMPLE_BET_AMOUNT),
    }

    const realBets = this.bets.filter((bet) => bet.currency === Currency.REAL)
    if (realBets.length > 0) {
      const { user, betId, amount, possibleProducts } = realBets.reduce(
        (max, bet) => (bet.amount > max.amount ? bet : max)
      )
      payload = { user, betId, amount, possibleProducts }
    }

    return payload
  }

  #generatePossibleProducts(amount: Int) {
    // after 1500 gemcents we want to filter randomly 50% of items
    const priceThreshold = 15_00
    const products = this.inventoryProducts.filter(
      (product) =>
        (product.price > priceThreshold && Math.random() >= 0.5) ||
        (product.price <= priceThreshold && Math.random() >= 0.15)
    )

    const betProducts: CrashProduct[] = []

    for (let i = 0; ; i++) {
      const at = growthFn(i * 2900 + Math.random() * 200)
      if (at < 0) break

      const winnings = Math.floor(amount * at)

      let bestProduct: CrashProduct
      let prevDifference = Infinity

      for (const product of products) {
        const difference = Math.abs(winnings - product.price)
        if (difference > prevDifference) break
        bestProduct = product
        prevDifference = difference
      }

      if (!betProducts.find((product) => product.id === bestProduct.id))
        betProducts.push(bestProduct)
      // reached most expensive
      if (bestProduct?.price === products[0].price) break
    }

    return betProducts
  }

  #startGame() {
    assert(this.state === GameState.Blocked)

    this.startedAt = Date.now()
    this.#setState(GameState.InProgress)
    this.#updateForceCashout()

    this.#scheduleTick(0)
  }

  #scheduleTick(elapsed: Int) {
    const left = this.duration - elapsed
    const tickIn = Math.max(0, Math.min(left, config.tickRate))

    setTimeout(this.#handleTick.bind(this), tickIn)
  }

  #handleTick() {
    const elapsed = Date.now() - this.startedAt
    const at = this.#at
    this.logger.info('Tick elapsed=%d at=%d', elapsed, at)

    const cashouts = this.#runAutoCashouts(at)

    const tick: Tick = {
      elapsed,
      at,
      cashouts,
    }
    this.emit('broadcast', ['tick', tick])

    if (at >= this.crashAt) {
      this.#endGame()
      return
    }

    this.#scheduleTick(elapsed)
  }

  #selectProductsForWinnings(bet: CrashBet, at: Float) {
    const products: CrashProduct[] = []
    let winnings = Math.floor(bet.amount * at)

    if (config.isItemBased) {
      for (let i = bet.possibleProducts?.length - 1; i >= 0; i--) {
        const product = bet.possibleProducts[i]
        if (winnings >= product.price) {
          products.push(product)
          winnings -= product.price
          break
        }
      }
    }

    if (winnings > 0) {
      products.push({
        ...this.balanceProduct,
        price: winnings,
      })
      winnings = 0
    }

    return products
  }

  #runAutoCashouts(at: Float) {
    const isForce = at >= this.forceCashoutAt
    const cashoutFilter = isForce
      ? (bet: CrashBet) => bet.state === BetState.Active
      : (bet: CrashBet) =>
          bet.state === BetState.Active && bet.autoCashoutAt <= at

    const betsToCashout = this.bets.filter(cashoutFilter)

    betsToCashout.forEach((bet) => {
      const cashoutAt = bet.autoCashoutAt
        ? Math.min(bet.autoCashoutAt, this.forceCashoutAt)
        : this.forceCashoutAt

      this.#doCashout(bet, cashoutAt, !isForce)
    })

    const cashouts = this.pendingCashouts
    this.pendingCashouts = []

    return cashouts
  }

  async #endGame() {
    assert(this.state === GameState.InProgress)

    this.bets.map((bet) => {
      if (bet.state === BetState.Active) {
        bet.state = BetState.Lost
        this.#handleFinish(bet)
      }
      return bet
    })

    this.#setState(GameState.Ended)
    setGameProfit(this.gameId, this.houseProfit)

    this.#addHistoryEntry()
    this.newGameTimeout = setTimeout(this.#newGame.bind(this), config.endTime)
  }

  #setState(state: GameState) {
    this.logger.debug('State changed to %s', state)
    this.state = state
    if (state !== GameState.Blocked) {
      this.emit('broadcast', ['status', this.status])
    }
  }

  #addHistoryEntry() {
    const historyEntry = {
      crashedAt: this.crashAt,
      id: this.gameId,
      seed: this.seed,
    }

    setTimeout(() => {
      this.gameHistory.push(historyEntry)
      if (this.gameHistory.length > config.maxHistory) this.gameHistory.shift()

      this.emit('broadcast', ['historyEntry', historyEntry])
    }, config.endTime)
  }

  async placeBet(
    { amount, currency, autoCashoutAt }: CrashPlaceBet,
    userId: Int
  ) {
    this.pendingBetsCount++
    try {
      assert(this.state === GameState.Betting, 'game_in_progress')

      assert(
        this.#getUserBets(userId).length < config.maxPlayerBets,
        'max_bets_reached'
      )

      this.logger.info(
        'New bet attempt userId=%d amount=%d currency=%s autoCashout=%d',
        userId,
        amount,
        currency,
        autoCashoutAt
      )

      const dbBet = await placeCrashBet(amount, currency, userId, this.gameId)
      const bet: CrashBet = { ...dbBet, autoCashoutAt }
      this.emit('broadcast', ['bet', bet])

      // even when bet is currency play we generate products for fe ui
      if (config.isItemBased || config.hasItemAnimation) {
        bet.possibleProducts = this.#generatePossibleProducts(amount)
      }

      if (bet.currency === Currency.REAL) {
        this.openBet += amount
        this.houseProfit += amount
      }
      this.bets.push(bet)

      return bet
    } catch (err) {
      if (
        err.message === 'insufficient_balance' ||
        err.message === 'feature_temporarily_disabled'
      ) {
        throw err
      }

      this.logger.error({ err }, `Failed to place bet message=${err.message}`)
      throw new Error('failed_to_place_bet')
    } finally {
      this.logger.info('Processed bet userId=%d', userId)
      this.pendingBetsCount--
    }
  }

  #isProductBet(bet: CrashBet) {
    return config.isItemBased && bet.currency === Currency.REAL
  }

  #getUserBets(userId: Int) {
    return this.bets.filter((bet) => {
      return bet.state === BetState.Active && bet.user.id === userId
    })
  }

  #updateForceCashout() {
    if (this.openBet === 0) {
      this.forceCashoutAt = Infinity
    } else {
      const leftToWin = this.maxTotalWin - this.totalWon + this.openBet
      const at = leftToWin / this.openBet
      this.forceCashoutAt = Math.floor(Math.max(at, 1) * 100) / 100
    }

    this.logger.debug('New forceCashoutAt=%d', this.forceCashoutAt)
  }

  async #doCashout(bet: CrashBet, at: Float, updateCashoutAt = true) {
    assert(at > 1, 'invalid_at')
    assert(at <= this.crashAt, 'invalid_at2')
    this.logger.info('Cashing out bet=%o at=%d', bet, at)

    const idx = this.bets.findIndex((_bet) => _bet.betId === bet.betId)
    this.bets[idx] = {
      ...this.bets[idx],
      state: BetState.Cashout,
      cashedOutAt: at,
    }

    const winnings = Math.floor(bet.amount * at)

    if (bet.currency === Currency.REAL) {
      this.openBet -= bet.amount
      this.totalWon += winnings - bet.amount
      this.houseProfit -= winnings
    }

    if (updateCashoutAt) this.#updateForceCashout()

    const cashoutResult = await this.#handleWin(bet, winnings, at)
    this.pendingCashouts.push(cashoutResult)

    await this.#handleFinish(bet, cashoutResult?.productsWon, at)
    this.#handleMissionEvents(bet, at)
  }

  async cashout(betId: Int, userId?: Int) {
    assert(this.state === GameState.InProgress)

    const bet = this.bets.find((bet) => {
      return bet.betId === betId && bet.state === BetState.Active
    })
    assert(bet)
    assert(!userId || bet.user.id === userId)

    return this.#doCashout(bet, this.#at)
  }

  async #handleWin(bet: CrashBet, winnings: Int, at: Float) {
    let result: any
    let productsWon: CrashProduct[] = []
    if (this.#isProductBet(bet)) {
      productsWon = this.#selectProductsForWinnings(bet, at)
      result = await handleCrashItemWin({
        gameId: this.gameId,
        userId: bet.user.id,
        betId: bet.betId,
        winAmount: winnings,
        products: productsWon,
        cashoutAt: at,
      })
    } else {
      if (!REAL_CURRENCIES.includes(bet.currency)) {
        winnings = Math.min(winnings, 10_000_000_00)
      }
      result = await handleCrashWin(bet.betId, bet.currency, winnings, at)
    }

    if (result) {
      const user = result.user as User
      const cashoutResult = {
        betId: bet.betId,
        user: {
          id: user.id,
          name: user.name,
          avatar: user.avatar,
        },
        at,
        winnings,
        currency: bet.currency,
        ...(productsWon.length > 0 && { productsWon }),
      }
      return cashoutResult
    }
  }

  async #handleFinish(bet: CrashBet, productsWon?: CrashProduct[], at?: Float) {
    const winAmount = Math.floor(bet.amount * (at || 0))
    const balanceIncrement = at && !this.#isProductBet(bet) ? winAmount : 0

    const userChanges = await handleBetFinish(
      bet.user.id,
      bet.betId,
      bet.currency,
      bet.amount,
      at
    )

    handleBetFeed({
      userId: bet.user.id,
      betAmount: bet.amount,
      winAmount,
      currency: bet.currency,
      multiplier: at,
      context: {
        productsWon,
      },
    })

    const betOutcome = {
      ...userChanges,
      currency: bet.currency,
      balanceIncrement,
      productsWon,
    }

    this.emit('userEvent', bet.user.id, ['betOutcome', betOutcome])
  }

  #handleMissionEvents(bet: CrashBet, at: Float) {
    if (at >= 10 && bet.amount >= 500 && bet.currency === Currency.REAL) {
      registerMissionEvent(bet.user.id, 'crash:10x-5gems')
    }
  }

  cashoutAllUserBets(userId: Int) {
    if (this.state !== GameState.InProgress) return

    this.logger.debug('Cashing out all user bets userId=%d', userId)

    const bets = this.#getUserBets(userId)

    bets.forEach((bet) =>
      this.#doCashout(bet, this.#at).catch((err) => {
        this.logger.warn('Failed to cashoutAllUserBets %s', err.message)
      })
    )
  }

  pause() {
    if (this.state !== GameState.Ended) return false

    clearTimeout(this.newGameTimeout)
    this.#setState(GameState.Paused)

    return true
  }

  async resume() {
    assert(this.state === GameState.Paused)
    await this.#newGame()
  }
}

export default new Crash()
