import { Mill<PERSON>, logger, sleep } from '@crashgg/common'
import { Crash } from './crash'

export class CrashWatchdog {
  private lastEvent: any = null
  private lastEventAt: Millis = 0
  private CHECK_INTERVAL = 180_000
  private DEAD_TIMEOUT = 180_000

  constructor(private crash: Crash) {}

  listen() {
    this.crash.on('broadcast', (event) => {
      this.lastEvent = event
      this.lastEventAt = Date.now()
    })
    setInterval(this.check.bind(this), this.CHECK_INTERVAL)
  }

  private async check() {
    const isDead =
      this.lastEvent && Date.now() - this.lastEventAt > this.DEAD_TIMEOUT

    if (isDead) {
      logger.error(
        'Watchdog detected dead game, lastEvent=%o at=%d',
        this.lastEvent,
        this.lastEventAt
      )
      await sleep(5_000)
      process.exit(1)
    }
  }
}
