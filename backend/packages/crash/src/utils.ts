import { CrashProduct, Float, <PERSON>is } from '@crashgg/types'
import { createHmac } from 'crypto'
import config from './config'
import { BALANCE_PRODUCT_ID } from '@crashgg/common'
import assert from 'assert'

export const growthFn = (ms: <PERSON>is) => {
  const r = 0.00006
  return ~~(100 * Math.pow(Math.E, r * ms)) / 100
}

export const getGameDuration = (crashAt: Float): Millis => {
  const c = 16666.666667
  const duration = c * Math.log(0.01 * (crashAt * 100 + 1))

  return Math.floor(duration)
}

/**
 * Combines game seed with client seed to determine the crash point
 * Returns a double decimal number over 1, or 0 for insta-crash
 */
export const getCrashPoint = (gameSeed: string) => {
  const saltedSeed = createHmac('sha256', config.clientSeed)
    .update(gameSeed)
    .digest('hex')

  if (isDivisible(saltedSeed, config.crashEvery)) return 1

  const h = parseInt(saltedSeed.slice(0, 52 / 4), 16)
  const e = Math.pow(2, 52)

  return Math.floor((100 * e - h) / (e - h)) / 100
}

const isDivisible = (hash: string, divisor: number) => {
  return BigInt(`0x${hash}`) % BigInt(divisor) === BigInt(0)
}

export const extractBalanceProduct = (products: CrashProduct[]) => {
  const balance = products.find((product) => product.id === BALANCE_PRODUCT_ID)
  const rest = products.filter((product) => product.id !== BALANCE_PRODUCT_ID)

  assert(balance, 'Balance product not found')
  return { balance, rest }
}
