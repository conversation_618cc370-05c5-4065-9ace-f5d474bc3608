import { PrismaClient, InventoryStatus, Prisma } from '@prisma/client'
import {
  BetState,
  BuildItem,
  CrashBet,
  CrashProduct,
  Currency,
  Float,
  HandleCrashItemWin,
  Int,
} from '@crashgg/types'
import {
  placeBet,
  handleWin,
  runAsTx,
  logger,
  decrypt,
  getAllProducts,
  handleItemWin,
  onBetFinished,
  getRedis,
  BALANCE_PRODUCT_ID,
  getEventQueue,
  PLACE_BET_EVENT,
  PlaceBetEvent,
  handleWagerChanges,
  bigintConversion,
  BET_OUTCOME_EVENT,
  BetOutcomeEvent,
  sleep,
} from '@crashgg/common'
import config from './config'
import { extractBalanceProduct } from './utils'
import assert from 'assert'

const redis = getRedis()
const eventQueue = getEventQueue()
const prisma = new PrismaClient({
  transactionOptions: {
    maxWait: 30_000,
    timeout: 30_000,
  },
})

export const getNewGameSeed = async (prevGameId?: Int) => {
  if (!prevGameId) {
    prevGameId = await prisma.crashGame.count()
  }

  const gameId = prevGameId + 1
  const { seed } = await prisma.crashSeed.findUnique({ where: { gameId } })

  return {
    gameId,
    seed: decrypt(seed),
  }
}

export const insertGame = async (data: Prisma.CrashGameCreateInput) => {
  return prisma.crashGame.create({ data })
}

export const placeCrashBet = async (
  amount: Int,
  currency: Currency,
  userId: Int,
  gameId: Int
): Promise<CrashBet> => {
  logger.info('placeCrashBet userId=%d gameId=%d', userId, gameId)
  const { user, bet } = await runAsTx(prisma, placeBet, {
    currency,
    amount,
    userId,
    game: 'crash',
    transactionData: { message: `Crash bet gameId: ${gameId}` },
    ...(config.instantWager && {
      affiliateEarnings: amount * config.affiliateRate,
    }),
    doWagerChanges: config.instantWager,
    betData: {
      crashGame: {
        connect: { id: gameId },
      },
    },
    isAsync: true,
  })

  await eventQueue.add(
    PLACE_BET_EVENT,
    new PlaceBetEvent({
      userId,
      amount,
      game: 'crash',
      balanceBefore: bigintConversion(user.balance) + amount,
      balanceAfter: bigintConversion(user.balance),
      currency,
      userAfter: user,
      isAsync: true,
    }),
    {
      removeOnComplete: true,
    }
  )

  return {
    amount,
    currency,
    user: { id: user.id, avatar: user.avatar, name: user.name, level: 0 },
    betId: bet.id,
    state: BetState.Active,
  }
}

export const handleCrashWin = async (
  betId: Int,
  currency: Currency,
  winningAmount: Int,
  cashoutAt: Float
) => {
  return await runAsTx(prisma, handleWin, {
    betId,
    currency,
    winningAmount,
    transactionData: { message: 'Crash win' },
    betData: {
      data: { cashoutAt },
    },
    isAsync: true,
  }).catch((err) => {
    logger.error(
      { err },
      'Failed to handle win betId=%d winningAmount=%d',
      betId,
      winningAmount
    )
  })
}

export const handleCrashItemWin = async (details: HandleCrashItemWin) => {
  const { gameId, betId, userId, products, winAmount, cashoutAt } = details
  const items = products.map((product) => ({
    userId,
    productId: product.id,
    price: product.price,
    status: InventoryStatus.AVAILABLE,
    details: {
      crashGameId: gameId,
      betId,
    },
  }))

  return await runAsTx(prisma, handleItemWin, {
    betId,
    userId,
    items,
    currency: Currency.REAL,
    transactionData: { message: 'Crash bet' },
    betData: {
      data: { cashoutAt },
    },
    winAmount,
    isAsync: true,
  }).catch((err) => {
    logger.error(
      { err },
      'Failed to handle item win betId=%d items=%d',
      betId,
      items
    )
  })
}

export const handleBetFinish = async (
  userId: Int,
  betId: Int,
  currency: Currency,
  betAmount: Int,
  cashoutAt?: Float
) => {
  if (!cashoutAt) {
    await onBetFinished(prisma, userId, 0, true, currency)
  }
  if (currency !== Currency.REAL)
    return { wagerIncrement: 0, ticketIncrement: 0 }

  const multiplier = !cashoutAt || cashoutAt > 1.49 ? 1 : cashoutAt - 1
  const wager = Math.round(betAmount * multiplier)

  if (!config.instantWager) {
    const { affiliateUpdates } = await handleWagerChanges({
      userId,
      betId,
      wager,
      prisma,
      affiliateEarnings: betAmount * config.affiliateRate,
    })
    await Promise.all(affiliateUpdates)
  }

  return { wagerIncrement: wager, ticketIncrement: wager }
}

export const setGameProfit = async (gameId: Int, houseProfit: Int) => {
  await prisma.crashGame
    .update({
      where: { id: gameId },
      data: { houseProfit },
    })
    .catch((err) => {
      logger.error(
        { err },
        'Failed to set profit gameId=%d profit=%d',
        gameId,
        houseProfit
      )
    })
}

export const getSortedItemsWithRetry = async () => {
  while (true) {
    try {
      const items =
        !config.isItemBased && config.hasItemAnimation
          ? await getSortedCreatorItems()
          : await getSortedProducts()

      return items
    } catch (err) {
      logger.warn({ err }, 'Failed to get sorted items, retrying in 5s')
      await sleep(5000)
    }
  }
}

const getSortedProducts = async () => {
  const unsorted = await getAllProducts(prisma)
  const sorted = unsorted.sort((a, b) => b.price - a.price)

  const products: CrashProduct[] = sorted.map((product) => ({
    id: product.id,
    name: product.name,
    category: product.category,
    price: product.price,
    imageUrl: product.imageUrl,
  }))

  return extractBalanceProduct(products)
}

const fixCreatorImage = (url: string): string => {
  url = url
    .replace('/e/', '/eq/')
    .replace('/e?', '/eq?')
    .replace('/p/', '/pqc/')
    .replace('/p?', '/pqc?')
  if (url.includes('?q=')) {
    url += '&m=crop'
  } else {
    url += '?m=crop'
  }

  return url
}

const getSortedCreatorItems = async () => {
  const unsorted = await redis.getJSON<BuildItem[]>(
    'cases:available-items:last'
  )
  assert(unsorted, 'Failed to get available items')
  const sorted = unsorted.sort((a, b) => b.price - a.price)

  const products: CrashProduct[] = sorted.map((product, idx) => ({
    id: idx,
    name: product.name,
    category: product.category,
    price: product.price,
    imageUrl: fixCreatorImage(product.image),
  }))

  const balance = await prisma.inventoryProduct.findUnique({
    where: { id: BALANCE_PRODUCT_ID },
    select: {
      id: true,
      name: true,
      category: true,
      price: true,
      imageUrl: true,
    },
  })
  assert(balance, 'Balance product not found')

  return {
    balance,
    rest: products,
  }
}

export const handleBetFeed = async (outcome: Omit<BetOutcomeEvent, 'game'>) => {
  await eventQueue.add(
    BET_OUTCOME_EVENT,
    new BetOutcomeEvent({
      ...outcome,
      game: 'crash',
    }),
    { removeOnComplete: true }
  )
}
