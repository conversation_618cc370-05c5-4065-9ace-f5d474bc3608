import { strBool } from '@crashgg/common'

const config = {
  port: Number(process.env.PORT) || 7200,
  jwtSecret: process.env.JWT_SECRET || 'moms-spaghetti',
  clientSeed:
    process.env.CLIENT_SEED ||
    '00000000000000000009e2a08a78ba29b737a4d5209aa48f7993d17229b16ef7',
  crashEvery: Number(process.env.CRASH_EVERY) || 20,
  betTime: Number(process.env.TIME_BET) || 15000,
  endTime: Number(process.env.TIME_END) || 5000,
  tickRate: Number(process.env.TICK_RATE) || 150,
  maxBet: Number(process.env.MAX_BET) || 500_00,
  ticketRate: Number(process.env.TICKET_RATE) || 0.1,
  affiliateRate: Number(process.env.AFFILIATE_RATE) || 0.005,
  rainMultiplier: Number(process.env.RAIN_BET_MULTIPLIER) || 0.0033,
  lotteryMultiplier: Number(process.env.LOTTERY_MULTIPLIER) || 0.005,
  maxHistory: Number(process.env.MAX_HISTORY) || 20,
  maxTotalWin: Number(process.env.MAX_TOTAL_WIN) || 2500_00,
  maxPlayerBets: Number(process.env.MAX_PLAYER_BETS) || 3,
  instantWager: strBool(process.env.INSTANT_WAGER),
  isItemBased: strBool(process.env.IS_ITEM_BASED, true),
  hasItemAnimation: strBool(process.env.HAS_ITEM_ANIMATION, true),
  itemProposalInterval: Number(process.env.ITEM_PROPOSAL_INTERVAL) || 500,
}

export default config
