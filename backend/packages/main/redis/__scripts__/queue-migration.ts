import Queue from 'bull'
import { Queue as QueueMq, JobsOptions } from 'bullmq'
import Redis from 'ioredis'
import fsPromise from 'fs/promises'
import path from 'path'

const redisUrl = process.env.REDIS_URL
const LOG_FILE = 'queue-migration-log.json'

type QueueMigration = {
  old: string
  new: string
}

const redis = new Redis(redisUrl)

const queues: QueueMigration[] = [
  { old: 'send-tradeoffer', new: '{send-tradeoffer}' },
  { old: 'tradeoffer-update/v2', new: '{tradeoffer-update}' },
  { old: 'inventory-resync', new: '{inventory-resync}' },
  { old: 'global-event-queue', new: '{global-event-queue}' },
  { old: 'admin', new: '{admin}' },
  { old: 'affiliates', new: '{affiliates}' },
  { old: 'battle/rain', new: '{battle-rain}' },
  { old: 'battles/v2', new: '{battles}' },
  { old: 'double-down', new: '{double-down}' },
  { old: 'cases', new: '{cases}' },
  { old: 'coinflipv2', new: '{coinflip}' },
  { old: 'conversion/abandoned-cart', new: '{conversion}' },
  { old: 'duels', new: '{duels}' },
  { old: 'email', new: '{email}' },
  { old: 'item-coinflip', new: '{item-coinflip}' },
  { old: 'roulette/v2', new: '{roulette}' },
  { old: 'jackpot', new: '{jackpot}' },
  { old: 'leaderboard', new: '{leaderboard}' },
  { old: 'lottery', new: '{lottery}' },
  { old: 'checkout', new: '{checkout}' },
  { old: 'currency', new: '{currency}' },
  { old: 'fireblocks', new: '{fireblocks}' },
  { old: 'skinsback', new: '{skinsback}' },
  { old: 'waxpeer', new: '{waxpeer}' },
  { old: 'rain', new: '{rain}' },
  { old: 'calendar', new: '{calendar}' },
  { old: 'raffles', new: '{raffles}' },
  { old: 'rakeback', new: '{rakeback}' },
  { old: 'seasonal', new: '{seasonal}' },
  { old: 'steam-p2p', new: '{steam-p2p}' },
  { old: 'steamapis', new: '{steamapis}' },
  { old: 'upgrader-auto-claim', new: '{upgrader-auto-claim}' },
  { old: 'user/level-loans', new: '{level-loans}' },
  { old: 'user', new: '{user}' },
]

const file = {
  start: async () => {
    fsPromise.writeFile(path.join(__dirname, '/', LOG_FILE), '{\n')
  },
  addData: async (key: string, data: object) => {
    fsPromise.appendFile(
      path.join(__dirname, '/', LOG_FILE),
      `"${key}": ${JSON.stringify(data, null, 2)},\n`
    )
  },
  end: async () => {
    fsPromise.appendFile(path.join(__dirname, '/', LOG_FILE), '}')
  },
}

async function clearQueue(queue: Queue.Queue) {
  const jobs = await queue.getJobs([
    'completed',
    'waiting',
    'active',
    'delayed',
    'failed',
    'paused',
  ])

  if (jobs.length > 0) {
    console.log(`Queue ${queue.name} has jobs:`, jobs.length)
    return
  }
  await queue.obliterate()
}

async function migrateQueue(queue: QueueMigration) {
  const oldQueue = new Queue(queue.old, { createClient: () => redis })
  const newQueue = new QueueMq(queue.new, {
    connection: redis as any,
  })

  await oldQueue.pause()

  const oldJobs = await oldQueue.getJobs(['waiting', 'delayed', 'paused'])
  await file.addData(queue.old, oldJobs)

  for (const job of oldJobs) {
    const remainingDelay = Math.max(
      0,
      job.opts.delay - (Date.now() - job.timestamp)
    )

    await newQueue.add(job.name, job.data, {
      ...bullJobOptsToBullMqJobOpts(job.opts),
      delay: remainingDelay,
    })

    await job.remove()
  }

  await clearQueue(oldQueue)

  await oldQueue.close()
  await newQueue.close()
}

async function migrateAllQueues() {
  await file.start()

  for (const queue of queues) {
    console.log(`Migrating queue ${queue.old} to ${queue.new}`)
    await migrateQueue(queue)
  }

  await file.end()
  redis.quit()
}

function bullJobOptsToBullMqJobOpts(bullJobOpt: Queue.JobOptions): JobsOptions {
  const { jobId, repeat, backoff: oldBackoff, ...restOpts } = bullJobOpt
  const backoff =
    typeof oldBackoff === 'object' &&
    oldBackoff.type === 'configurable-backoff-strat'
      ? null
      : oldBackoff

  return {
    ...restOpts,
    jobId: jobId?.toString(),
    backoff,
    ...(repeat && 'cron' in repeat && { repeat: { pattern: repeat.cron } }),
  }
}

migrateAllQueues()
