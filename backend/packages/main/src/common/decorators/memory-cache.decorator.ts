import { SetMetadata, UseInterceptors, applyDecorators } from '@nestjs/common'
import { CacheTTL } from '@nestjs/cache-manager'
import { MemoryCacheInterceptor } from '../interceptors/memory-cache.interceptor'
import { seconds } from '@nestjs/throttler'

export const MEMORY_CACHE_OPTIONS = 'memory-cache-options'

export type MemoryCacheOptions = { ttl?: number } & (
  | {
      perUser?: true
      guestOnly?: false
    }
  | {
      perUser?: false
      guestOnly?: boolean
    }
)

export function MemoryCache(options?: MemoryCacheOptions) {
  const defaultOptions: MemoryCacheOptions = {
    ttl: seconds(3),
    perUser: false,
    guestOnly: false,
  }

  const finalOptions = { ...defaultOptions, ...options }

  return applyDecorators(
    CacheTTL(finalOptions.ttl),
    SetMetadata(MEMORY_CACHE_OPTIONS, finalOptions),
    UseInterceptors(MemoryCacheInterceptor)
  )
}
