import {
  ValidatorConstraint,
  ValidatorConstraintInterface,
  ValidationArguments,
  registerDecorator,
  ValidationOptions,
} from 'class-validator'

@ValidatorConstraint({ name: 'IsNotEmptyDto', async: false })
export class IsNotEmptyDtoConstraint implements ValidatorConstraintInterface {
  validate(value: any, args: ValidationArguments) {
    const keys = Object.keys(args.object)
    return keys.length > 0
  }

  defaultMessage(_args: ValidationArguments) {
    return `At least one field must be provided`
  }
}

export function IsNotEmptyDto(validationOptions?: ValidationOptions) {
  return function (object: any) {
    registerDecorator({
      target: object,
      propertyName: '',
      validator: IsNotEmptyDtoConstraint,
      options: validationOptions,
      constraints: [],
    })
  }
}
