import { WorkerOptions } from 'bullmq'
import 'reflect-metadata'

export const BULLMQ_QUEUE = 'BULLMQ_QUEUE'
export const BULLMQ_JOB = 'BULLMQ_JOB'

interface Job {
  jobName: string
  method: string | symbol
}

export function ProcessorMq(
  queueName: string,
  opts?: Omit<WorkerOptions, 'connection'>
): ClassDecorator {
  return (target) => {
    Reflect.defineMetadata(BULLMQ_QUEUE, { queueName, opts }, target)
  }
}

export function ProcessMq(jobName = ''): MethodDecorator {
  return (target, key) => {
    const existing: Job[] =
      (Reflect.getMetadata(BULLMQ_JOB, target.constructor) as Job[]) || []
    existing.push({ jobName, method: key })
    Reflect.defineMetadata(BULLMQ_JOB, existing, target.constructor)
  }
}
