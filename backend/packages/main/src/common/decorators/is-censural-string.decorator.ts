import { containsBadWords, FilterContext } from '@crashgg/common/dist'
import {
  registerDecorator,
  ValidationArguments,
  ValidationOptions,
} from 'class-validator'

export function IsCensuralString(
  context: FilterContext,
  validationOptions?: ValidationOptions
) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'IsCensuralString',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      constraints: [context],
      validator: {
        validate(value: unknown, args: ValidationArguments) {
          const [ctx] = args.constraints
          return typeof value === 'string' && !containsBadWords(value, ctx)
        },
        defaultMessage() {
          return 'bad_word_usage'
        },
      },
    })
  }
}
