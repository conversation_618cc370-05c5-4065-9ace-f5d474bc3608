import { DAY_MS } from '@crashgg/common/dist'
import { MinimalJob } from 'bullmq'

interface BackoffStrategyOptions {
  initialDelay?: Int
  multiplier?: Float
  maxDelay?: Int
}

export enum BullBackoffStrategy {
  SLOW = 'slow',
  LOWER_MULTIPLIER = 'lower-multiplier',
  MULTI_1_POINT_1 = 'multi-1-1',
}

const backoffStrategies = {
  [BullBackoffStrategy.SLOW]: {
    multiplier: 3,
    initialDelay: 5000,
  },
  [BullBackoffStrategy.LOWER_MULTIPLIER]: {
    multiplier: 1.2,
  },
  [BullBackoffStrategy.MULTI_1_POINT_1]: {
    multiplier: 1.1,
  },
}

export const getBackoffStrategy = (
  attemptsMade: number,
  options?: BackoffStrategyOptions
) => {
  const initialDelay = options?.initialDelay || 500
  const multiplier = options?.multiplier || 1.5
  const maxDelay = options?.maxDelay || DAY_MS

  return Math.min(initialDelay * Math.pow(multiplier, attemptsMade), maxDelay)
}

export const configurableBackoffStrategy = (
  attemptsMade: number,
  type?: string,
  _err?: Error,
  _job?: MinimalJob
) => {
  return getBackoffStrategy(attemptsMade, backoffStrategies[type])
}
