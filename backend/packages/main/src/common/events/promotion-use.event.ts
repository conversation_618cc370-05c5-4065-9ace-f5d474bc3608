export const PROMOTION_USE = 'promotion.use'

export enum PromotionUseFailureReason {
  ALREADY_USED,
  INVALID_CODE,
  NOT_APPLICABLE,
  EXPIRED,
}

export class PromotionUseEvent {
  userId: Int
  amount: Int
  isSuccessful: boolean
  failureReason?: PromotionUseFailureReason
  promotionId: string
  promotionDescription: string
  isUseSpike?: boolean

  constructor(data: PromotionUseEvent) {
    Object.assign(this, data)
  }
}
