import { Logger } from '@nestjs/common'

const LOG_THRESHOLD = 10

export const performanceLogger = (logger: Logger, featureName: string) => {
  const tag = `[${featureName}:${Math.random().toString(36).substring(7)}]`

  const start = Date.now()
  let lastCheckpoint = Date.now()

  return (chekkpoint: string, isFinish?: boolean) => {
    const took = Date.now() - lastCheckpoint
    if (took > LOG_THRESHOLD || isFinish) {
      const total = Date.now() - start
      logger.log(`${tag} ${chekkpoint} - took=${took}ms (total=${total}ms)`)
    }
    lastCheckpoint = Date.now()
  }
}
