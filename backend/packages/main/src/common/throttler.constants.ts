import { ExecutionContext } from '@nestjs/common'
import { ThrottlerOptions, minutes, seconds } from '@nestjs/throttler'
import { Request } from 'express'
import { getIp } from './utilities'

enum ThrottlerName {
  Default = 'default',
  Burst = 'burst',
  Guest = 'guest',
}

export const HIGH_THROUGHPUT: Record<string, ThrottlerOptions> = {
  [ThrottlerName.Default]: {
    ttl: minutes(1),
    limit: 10 * 60,
  },
  [ThrottlerName.Burst]: {
    ttl: seconds(1),
    limit: 70,
  },
} as const

export const SKIP_ALL: Record<ThrottlerName, false> = {
  [ThrottlerName.Default]: false,
  [ThrottlerName.Burst]: false,
  [ThrottlerName.Guest]: false,
}

export const isSensitiveUrl = (req: Request) => {
  return [
    '/health',
    '/payment',
    '/webhook',
    '/user/me',
    '/auth/access-token',
    '/fairness',
  ].some((sensitive) => req?.url?.includes(sensitive))
}

export const isAuthenticated = (req: Request) => {
  return (
    Boolean(req.headers.authorization) ||
    req.cookies.refresh_token ||
    req.cookies.token
  )
}

export const defaultSkipRules = (req: Request) => {
  const ip = getIp(req)
  if (ip.includes('172.1')) return true

  return isSensitiveUrl(req)
}

export const defaultSkip = (ctx: ExecutionContext) => {
  const req: Request = ctx.switchToHttp().getRequest()
  return defaultSkipRules(req)
}

export const DEFAULT_THROTTLERS: ThrottlerOptions[] = [
  {
    name: ThrottlerName.Default,
    ttl: minutes(1),
    limit: 200, // This is insane
    skipIf: (ctx) => {
      const req: Request = ctx.switchToHttp().getRequest()
      return defaultSkipRules(req) || isAuthenticated(req)
    },
  },
  {
    name: ThrottlerName.Burst,
    ttl: seconds(1),
    limit: 50, // This is insane
    skipIf: (ctx) => {
      const req: Request = ctx.switchToHttp().getRequest()
      return defaultSkipRules(req) || isAuthenticated(req)
    },
  },
  // {
  //   name: ThrottlerName.Guest,
  //   ttl: minutes(1),
  //   limit: 100 * TEST_LIBERTY,
  //   skipIf: (ctx) => {
  //     const req: Request = ctx.switchToHttp().getRequest()
  //     return defaultSkipRules(req) || isAuthenticated(req)
  //   }
  // }
]
