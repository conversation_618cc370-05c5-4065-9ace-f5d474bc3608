import { PaginatedDto } from './dto/paginated.dto'
import { TimeCursorDto } from './dto/time-cursor.dto'

export const paginate = (
  { page = 1, pageSize }: PaginatedDto,
  defaultPageSize = 30
) => {
  pageSize = pageSize || defaultPageSize

  return {
    skip: (page - 1) * pageSize,
    take: pageSize,
  }
}

export const paginated = <T>({
  data,
  count,
  pageSize,
}: {
  data: T
  count: Int
  pageSize: Int
}) => {
  const pageCount = getPageCount(count, pageSize)

  return {
    pages: pageCount,
    data,
  }
}

export const getPageCount = (count: number, pageSize: number) => {
  return Math.ceil(count / pageSize)
}

/**
 * @deprecated
 * Use PaginatedDto instead
 */
export const isPageNumber = (page: unknown) => {
  if (isNaN(page as any)) return false

  const pageInt = parseInt(page as string)
  return Number.isInteger(pageInt) && pageInt > 0
}

export const getTimeCursorBounds = (cursor: TimeCursorDto) => {
  if (cursor.before) {
    return { lt: cursor.before }
  }

  if (cursor.after) {
    return { gt: cursor.after }
  }

  return {}
}

export const getTimeCursorOrder = (cursor: TimeCursorDto): 'asc' | 'desc' => {
  if (cursor.after) return 'asc'
  return 'desc'
}

interface CursorFnArgs<T> {
  data: T[]
  cursor: TimeCursorDto
  field: keyof T
}

export const getNextCursor = <T>({
  data,
  cursor,
  field,
}: CursorFnArgs<T>): TimeCursorDto => {
  if (data.length === 0) return null

  if (cursor.after) {
    return { after: data[0][field] as unknown as Date }
  }

  return { before: data[data.length - 1][field] as unknown as Date }
}

export const getCursors = <T>({
  data,
  cursor,
  field,
}: CursorFnArgs<T>): {
  prevCursor: TimeCursorDto
  nextCursor: TimeCursorDto
} => {
  if (data.length === 0) return null

  const first = data[0][field] as unknown as Date
  const last = data[data.length - 1][field] as unknown as Date

  if (cursor.after) {
    // return { after: data[0][field] as unknown as Date }
    return {
      nextCursor: { after: first },
      prevCursor: { before: last },
    }
  }

  // return { before: data[data.length - 1][field] as unknown as Date }
  return {
    nextCursor: { before: last },
    prevCursor: { after: first },
  }
}

export const toCursoredResponse = <T>(args: CursorFnArgs<T>) => {
  return {
    data: args.data,
    ...getCursors(args),
  }
}
