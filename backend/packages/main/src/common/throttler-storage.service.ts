import { Injectable } from '@nestjs/common'
import { ThrottlerStorageRecord } from '@nestjs/throttler/dist/throttler-storage-record.interface'
import * as Redis from 'ioredis'

// Based on:
// https://github.com/kkoomen/nestjs-throttler-storage-redis/blob/master/src/throttler-storage-redis.service.ts

@Injectable()
export class ThrottlerStorageRedisService {
  redis: Redis.Redis
  disconnectRequired?: boolean
  scanCount: number
  scriptSrc: string

  constructor(redis: Redis.Redis, scanCount?: number) {
    this.scanCount = typeof scanCount === 'undefined' ? 1000 : scanCount

    this.redis = redis
    this.scriptSrc = this.getScriptSrc()
  }

  private getScriptSrc() {
    return `
      local totalHits = redis.call("INCR", KEYS[1])
      local timeToExpire = redis.call("PTTL", KEYS[1])
      if timeToExpire <= 0
        then
          redis.call("PEXPIRE", KEYS[1], tonumber(ARGV[1]))
          timeToExpire = tonumber(ARGV[1])
        end
      return { totalHits, timeToExpire }
    `
      .replace(/^\s+/gm, '')
      .trim()
  }

  async increment(key: string, ttl: number): Promise<ThrottlerStorageRecord> {
    const results: number[] = (await this.redis.eval(
      this.scriptSrc,
      1,
      'rl:' + key,
      ttl * 1000
    )) as number[]

    if (!Array.isArray(results)) {
      throw new TypeError(
        `Expected result to be array of values, got ${results}`
      )
    }

    if (results.length !== 2) {
      throw new Error(`Expected 2 values, got ${results.length}`)
    }

    const [totalHits, timeToExpire] = results

    if (typeof totalHits !== 'number') {
      throw new TypeError('Expected totalHits to be a number')
    }

    if (typeof timeToExpire !== 'number') {
      throw new TypeError('Expected timeToExpire to be a number')
    }

    return { totalHits, timeToExpire }
  }
}
