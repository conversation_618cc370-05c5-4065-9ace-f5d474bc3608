import { Int } from '@crashgg/types/dist'
import { createParamDecorator, ExecutionContext } from '@nestjs/common'
import { assert } from 'src/utils/assert'

export const getUserId = (req: any) => req.user?.userId || req.user?.id
export const getRole = (req: any) => req.user?.role ?? 'guest'

export const UserId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): Int => {
    const req = ctx.switchToHttp().getRequest()

    return getUserId(req)
  }
)

export const SteamId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string => {
    const req = ctx.switchToHttp().getRequest()
    const { steamId } = req.user
    assert(steamId, 'steam_not_linked')

    return steamId
  }
)

export type Role =
  | 'admin'
  | 'management'
  | 'staff'
  | 'vip-manager'
  | 'mod'
  | 'user'
  | 'trader'
  | 'helper'

export const UserRole = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): Int => {
    const req = ctx.switchToHttp().getRequest()

    return getRole(req)
  }
)
