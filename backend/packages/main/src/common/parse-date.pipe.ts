import { BadRequestException, Injectable, PipeTransform } from '@nestjs/common'

@Injectable()
export class ParseDatePipe implements PipeTransform {
  transform(value: string | Date | undefined | null) {
    if (!value) return null
    const transformedValue = new Date(value)
    if (isNaN(transformedValue.getTime())) {
      throw new BadRequestException('invalid_date')
    }
    return transformedValue
  }
}
