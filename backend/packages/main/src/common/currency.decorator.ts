import { Currency } from '@crashgg/types/dist'
import {
  applyDecorators,
  BadRequestException,
  CallHandler,
  createParamDecorator,
  ExecutionContext,
  Header,
  NestInterceptor,
  UseInterceptors,
} from '@nestjs/common'
import { Request } from 'express'
import { Observable, tap } from 'rxjs'
import { assert } from 'src/utils/assert'
import { CURRENCIES } from '@crashgg/common'

export const getCurrency = (req: Request): Currency =>
  (req.get('X-Currency') as Currency) || Currency.REAL

export const getCurrencies = (req: Request): Currency[] => {
  const xCurrency = req.get('X-Currency')
  if (!xCurrency) return [Currency.REAL]

  const currencies = xCurrency.split(/,\s?/) as Currency[]
  assert(
    currencies.length > 0 &&
      currencies.every((currency) => CURRENCIES.includes(currency)),
    'invalid_currency'
  )

  return Array.from(new Set(currencies))
}

export const CurrencyUsed = createParamDecorator(
  (allowedCurrencies: Currency[] | undefined, ctx: ExecutionContext) => {
    const req = ctx.switchToHttp().getRequest()

    const currency = getCurrency(req)
    assert(CURRENCIES.includes(currency), 'invalid_currency')

    assert(
      !allowedCurrencies || allowedCurrencies.includes(currency),
      'blocked_currency'
    )

    return currency
  }
)

export const CurrenciesUsed = createParamDecorator(
  (allowedCurrencies: Currency[] | undefined, ctx: ExecutionContext) => {
    const req = ctx.switchToHttp().getRequest()
    const currencies = getCurrencies(req)

    assert(
      !allowedCurrencies ||
        currencies.every((currency) => allowedCurrencies.includes(currency)),
      'blocked_currency'
    )

    return currencies
  }
)

// used mainly in game-history endpoints
export const QueryCurrency = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest()
    const currency: undefined | Array<string> | string = request.query.currency

    if (!currency) return undefined
    if (CURRENCIES.includes(currency as Currency)) return currency

    throw new BadRequestException('invalid_currency')
  }
)

class CurrencyMirrorInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const req = context.switchToHttp().getRequest()
    const currency: Currency = getCurrency(req)

    return next.handle().pipe(
      tap(() => {
        const res = context.switchToHttp().getResponse()
        res.header('X-Currency', currency)
      })
    )
  }
}

/**
 * Use this decorator if the GET response varies based on the currency.
 * Only for CurrencyUsed decorator.
 */
export const VaryCurrency = () =>
  applyDecorators(
    Header('Vary', 'X-Currency'),
    UseInterceptors(CurrencyMirrorInterceptor)
  )
