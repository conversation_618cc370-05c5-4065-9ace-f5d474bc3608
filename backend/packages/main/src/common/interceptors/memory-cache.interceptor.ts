import { ExecutionContext, Injectable } from '@nestjs/common'
import { CacheInterceptor } from '@nestjs/cache-manager'
import {
  MEMORY_CACHE_OPTIONS,
  MemoryCacheOptions,
} from '../decorators/memory-cache.decorator'
import { getRole, getUserId } from '../user-id.decorator'
import { getCurrencies } from '../currency.decorator'
import { STAFF_ROLES } from '../constants'

const NO_CACHE: undefined = undefined

@Injectable()
export class MemoryCacheInterceptor extends CacheInterceptor {
  override isRequestCacheable(context: ExecutionContext): boolean {
    // checks if it is GET method
    return super.isRequestCacheable(context)
  }

  override trackBy(context: ExecutionContext): string | undefined {
    const isCacheable = this.isRequestCacheable(context)
    if (!isCacheable) return NO_CACHE

    const options = this.reflector.get<MemoryCacheOptions>(
      MEMORY_CACHE_OPTIONS,
      context.getHandler()
    )

    const urlKey = super.trackBy(context)
    const request = context.switchToHttp().getRequest()

    const currencies = getCurrencies(request)

    const userId = getUserId(request)
    const isLogged = !!userId
    const role = getRole(request)

    if (STAFF_ROLES.includes(role)) return NO_CACHE

    if (options.guestOnly) {
      return isLogged ? NO_CACHE : this._cacheKey(urlKey, currencies)
    }

    if (options.perUser) {
      if (isLogged) return this._cacheKey(urlKey, currencies, userId)
    }

    // for everyone (logged, not logged), when options are false
    return this._cacheKey(urlKey, currencies)
  }

  private _cacheKey(urlKey: string, currencies: Currency[], userId?: string) {
    if (userId) return `${userId}:${urlKey}:${currencies.join(',')}`
    return `${urlKey}:${currencies.join(',')}`
  }
}
