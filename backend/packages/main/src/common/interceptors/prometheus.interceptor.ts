import {
  CallHandler,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common'
import { Observable } from 'rxjs'
import { tap } from 'rxjs/operators'
import * as promClient from 'prom-client'

const httpRequestCount = new promClient.Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status'],
})

const httpRequestDuration = new promClient.Histogram({
  name: 'http_request_duration_seconds',
  help: 'HTTP request duration in seconds',
  labelNames: ['method', 'route', 'status'],
  buckets: [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1, 2.5, 5, 10],
})

@Injectable()
export class PrometheusInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const req = context.switchToHttp().getRequest()
    const method = req.method
    const route = req.route?.path || req.url

    const end = httpRequestDuration.startTimer({ method, route })

    const generalizeStatusCode = (statusCode: Int) =>
      String(statusCode).at(0) + 'xx'

    return next.handle().pipe(
      tap({
        next: (_data) => {
          const statusCode = context.switchToHttp().getResponse().statusCode
          const status = generalizeStatusCode(statusCode)
          httpRequestCount.inc({ method, route, status })
          end({ status })
        },
        error: (err) => {
          const statusCode = err.status || 500
          const status = generalizeStatusCode(statusCode)
          httpRequestCount.inc({ method, route, status })
          end({ status })
        },
      })
    )
  }
}
