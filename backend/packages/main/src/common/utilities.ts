import { logger, sleep } from '@crashgg/common/dist'
import { AxiosError } from 'axios'
import { TransformFnParams } from 'class-transformer'
import { createHash } from 'crypto'
import { Request } from 'express'
import slugify from 'slugify'

export const getIp = (req: Request) =>
  (req.headers['cf-connecting-ip'] as string) ||
  (req.headers['x-forwarded-for'] as string) ||
  req.connection?.remoteAddress

export const tsAfter = (delay: Millis) => {
  return new Date(Date.now() + delay)
}

export const startOfDay = (date: Date) => {
  date.setUTCHours(0, 0, 0, 0)
  return date
}

export const startOfWeek = (date: Date) => {
  const diff = date.getDate() - date.getDay() + (date.getDay() === 0 ? -6 : 1)
  date.setDate(diff)

  return startOfDay(date)
}

export const tsBefore = (delay: Millis) => tsAfter(-delay)

export const isIpV4 = (ip: string) => {
  return ip.split('.').length === 4
}

export const md5 = (str: string) => {
  return createHash('md5').update(str).digest('hex')
}

export const chunk = <T>(arr: T[], size: number): T[][] => {
  const chunks: T[][] = []
  for (let i = 0; i < arr.length; i += size) {
    chunks.push(arr.slice(i, i + size))
  }

  return chunks
}

export const filterAsync = async <T>(
  arr: T[],
  filterFn: (v: T) => Promise<boolean>
): Promise<T[]> => {
  const promises = arr.map(async (v) => ({
    value: v,
    include: await filterFn(v),
  }))
  const withIncludes = await Promise.all(promises)

  return withIncludes.filter((v) => v.include).map((data) => data.value)
}

export const mapAsync = async <TIn, TOut>(
  arr: TIn[],
  mapFn: (v: TIn, idx: Int, arr: TIn[]) => Promise<TOut>
): Promise<TOut[]> => {
  const promises = arr.map(mapFn)
  return await Promise.all(promises)
}

export const mapAsyncSome = async <TIn, TOut>(
  arr: TIn[],
  mapFn: (v: TIn, idx: Int, arr: TIn[]) => Promise<TOut>
): Promise<TOut[]> => {
  const promises = arr.map(mapFn)
  const settled = await Promise.allSettled(promises)

  return settled
    .filter((v) => v.status === 'fulfilled')
    .map((v: PromiseFulfilledResult<Awaited<TOut>>) => v.value)
}

export const transformInt = ({ value }: TransformFnParams) =>
  parseInt(value, 10)

export const transformIntArray = ({ value }: TransformFnParams) => {
  if (typeof value !== 'string') {
    if (Array.isArray(value) && value.every((v) => typeof v === 'number')) {
      return value
    }

    return []
  }

  return value.split(',').map((v) => parseInt(v, 10))
}

// combine two arrays into one array of tuples
export const zip = <T, U>(a: T[], b: U[]): [T, U][] => {
  const minLength = Math.min(a.length, b.length)
  const result: [T, U][] = []
  for (let i = 0; i < minLength; i++) {
    result.push([a[i], b[i]])
  }
  return result
}

export const transformTrim = ({ value }: TransformFnParams) => value.trim()

export function warn(err: Error) {
  logger.warn('Recoverable error: %s %s', err.message, err.stack)
}

export const slugifySafe = (str: string) => {
  return slugify(str, { lower: true, remove: /[.]/g }).replace(/\\/g, '')
}

export const prettyJson = (obj: any) => {
  return JSON.stringify(obj ?? null, null, 2)
}

export const prandom = <T>(arr: T[]): T => {
  return arr[Math.floor(Math.random() * arr.length)]
}

export const prandomInt = (min: Int, max: Int) => {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

export const shuffle = <T>(arr: T[]) => {
  return arr
    .map((value) => ({ value, sort: Math.random() }))
    .sort((a, b) => a.sort - b.sort)
    .map(({ value }) => value)
}

export const isTruthy = <T>(
  value?: T | undefined | null | false
): value is T => {
  return !!value
}

export const groupSettlements = (settlements: PromiseSettledResult<any>[]) => {
  const response = { rejected: 0, fulfilled: 0 }

  for (const settlement of settlements) {
    response[settlement.status] += 1
  }

  return response
}

export const isAxiosNetworkIssue = (err: AxiosError) => {
  return !err.response?.status
}

export const avg = (arr: number[]) => {
  return arr.reduce((a, b) => a + b, 0) / arr.length
}

export const parseFloatToFixed = (value: number, fixed: number) => {
  return Math.floor(value * 10 ** fixed) / 10 ** fixed
}

type RetryableAllSettledOptions = {
  retries?: Int
  initialDelay?: Int
  factor?: Float
}

export const retryableAllSettled = async <T>(
  promiseFactories: (() => Promise<T>)[],
  options?: RetryableAllSettledOptions
) => {
  const { retries = 5, initialDelay = 500, factor = 2 } = options || {}
  let results: PromiseSettledResult<any>[] = []

  let delay = initialDelay
  for (let retry = 0; retry < retries; retry++) {
    let promiseArray: Promise<T>[] = []

    if (results.length > 0) {
      promiseArray = results.map((result, index) =>
        result.status === 'fulfilled' ? result.value : promiseFactories[index]()
      )
    } else {
      promiseArray = promiseFactories.map((fn) => fn())
    }

    results = await Promise.allSettled(promiseArray)
    if (results.every((x) => x.status === 'fulfilled')) {
      return results
    }

    await sleep(delay)
    delay *= factor
  }

  return results
}

export const censor = (uncensored: string): string => {
  const segmenter = new Intl.Segmenter(undefined, { granularity: 'grapheme' })
  const graphemes = Array.from(
    segmenter.segment(uncensored),
    (seg) => seg.segment
  )

  let charsToShow: number
  if (graphemes.length < 7) {
    charsToShow = 1
  } else if (graphemes.length < 9) {
    charsToShow = 2
  } else {
    charsToShow = 3
  }

  return (
    graphemes.slice(0, charsToShow).join('') +
    '***' +
    graphemes.slice(-charsToShow).join('')
  )
}
