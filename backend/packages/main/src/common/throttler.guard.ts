import { ThrottlerGuard } from '@nestjs/throttler'
import { ExecutionContext, Injectable, Logger } from '@nestjs/common'
import { getIp, md5 } from './utilities'
import { Request } from 'express'
import { ThrottlerLimitDetail } from '@nestjs/throttler/dist/throttler.guard.interface'
import { MINUTE_MS } from 'src/utils/constants'

@Injectable()
export class ProxiedThrottlerGuard extends ThrottlerGuard {
  private readonly logger = new Logger(ProxiedThrottlerGuard.name)

  protected override getTracker(req: Request): Promise<string> {
    if (process.env.NODE_ENV === 'development') return Promise.resolve('1')

    // TODO: Support per-user rate limiting
    // const authHeader = req.headers.authorization
    // return Promise.resolve(authHeader ? authHeader.slice(-16) : getIp(req))

    return Promise.resolve(getIp(req))
  }

  protected override getErrorMessage(
    _context: ExecutionContext,
    _throttlerLimitDetail: ThrottlerLimitDetail
  ): Promise<string> {
    const minutes = Math.ceil(_throttlerLimitDetail.ttl / MINUTE_MS)
    return Promise.resolve(`Please slow down. Try again in ${minutes} minutes.`)
  }

  protected override generateKey(
    context: ExecutionContext,
    suffix: string,
    name: string
  ): string {
    const className = context.getClass().name
    const handlerName = context.getHandler().name
    const prefix = `${className}-${handlerName}-${name}`
    this.logger.log('Key prefix=%s suffix=%s', prefix, suffix)

    return md5(`${prefix}-${suffix}`)
  }
}
