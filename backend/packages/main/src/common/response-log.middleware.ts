import { Injectable, Logger, NestMiddleware } from '@nestjs/common'
import { Request, NextFunction } from 'express'
import { PassThrough } from 'stream'

@Injectable()
export class ResponseLogMiddleware implements NestMiddleware {
  private logger = new Logger(ResponseLogMiddleware.name)

  use(req: Request, res: any, next: NextFunction) {
    const defaultWrite = res.write.bind(res)
    const defaultEnd = res.end.bind(res)
    const ps: any = new PassThrough()
    const chunks = []

    ps.on('data', (data) => chunks.push(data))

    res.write = (...args) => {
      ps.write(...args)
      defaultWrite(...args)
    }

    res.end = (...args) => {
      ps.end(...args)
      defaultEnd(...args)
    }

    res.on('finish', () => {
      const body = Buffer.concat(chunks).toString()
      this.logger.log(
        'Request path=%s code=%d req=%o res=%o',
        req.path,
        res.statusCode,
        req.body,
        body
      )
    })

    next()
  }
}
