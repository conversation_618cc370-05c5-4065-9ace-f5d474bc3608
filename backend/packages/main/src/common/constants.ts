import { DAY_<PERSON> } from 'src/utils/constants'
import { Role } from './user-id.decorator'

export const PUBLIC_USER_FIELDS = {
  id: true,
  role: true,
  name: true,
  avatar: true,
  xp: true,
  badge: true,
  isPrivate: true,
  premiumUntil: true,
}

export const YEAR = 365.25 * DAY_MS

export const SUCCESS = { success: true }
export const FAILURE = { success: false }

export const STAFF_ROLES = [
  'admin',
  'management',
  'staff',
  'vip-manager',
  'mod',
  'helper',
] as Role[]

export const ALL_ROLES = [...STAFF_ROLES, 'user', 'trader'] as Role[]

// Groups for class-transformer
export const STAFF = 'staff'
export const OWNER = 'owner'
export const PUBLIC = 'public'

export const PLAY_MULTIPLIER = 10000

export const SITE_CODES = ['CL', 'RC', 'CA', 'SS'] as const

export const SHARED_BULL_CONFIG_KEY = 'shared'
