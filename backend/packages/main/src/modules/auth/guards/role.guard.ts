import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
} from '@nestjs/common'

@Injectable()
export class RoleGuard implements CanActivate {
  constructor(
    private roles: string[],
    private customMessage?: string
  ) {}

  canActivate(context: ExecutionContext): boolean {
    const req = context.switchToHttp().getRequest()

    const canActivate = this.roles.includes(req.user.role)
    if (!canActivate && this.customMessage) {
      throw new ForbiddenException(this.customMessage)
    }

    return canActivate
  }
}

@Injectable()
export class NotRoleGuard implements CanActivate {
  constructor(
    private roles: string[],
    private customMessage?: string
  ) {}

  canActivate(context: ExecutionContext): boolean {
    const req = context.switchToHttp().getRequest()

    const canActivate = this.roles.every((role) => role !== req.user.role)
    if (!canActivate && this.customMessage) {
      throw new ForbiddenException(this.customMessage)
    }

    return canActivate
  }
}

@Injectable()
export class AdminGuard extends RoleGuard {
  static ROLES = ['admin' as const]

  constructor() {
    super(AdminGuard.ROLES)
  }
}

@Injectable()
export class ManagementGuard extends RoleGuard {
  static ROLES = ['management' as const, ...AdminGuard.ROLES]

  constructor() {
    super(ManagementGuard.ROLES)
  }
}

@Injectable()
export class StaffGuard extends RoleGuard {
  static ROLES = ['staff' as const, ...ManagementGuard.ROLES]

  constructor() {
    super(StaffGuard.ROLES)
  }
}
@Injectable()
export class VipManagerGuard extends RoleGuard {
  static ROLES = ['vip-manager' as const, ...StaffGuard.ROLES]

  constructor() {
    super(VipManagerGuard.ROLES)
  }
}

@Injectable()
export class ModGuard extends RoleGuard {
  static ROLES = ['vip-manager' as const, 'mod' as const, ...StaffGuard.ROLES]

  constructor() {
    super(ModGuard.ROLES)
  }
}

@Injectable()
export class HelperGuard extends RoleGuard {
  static ROLES = ['helper' as const, ...ModGuard.ROLES]

  constructor() {
    super(HelperGuard.ROLES)
  }
}

@Injectable()
export class NotTraderGuard extends NotRoleGuard {
  static ROLES = ['trader' as const]

  constructor() {
    super(NotTraderGuard.ROLES, 'is_trader')
  }
}
