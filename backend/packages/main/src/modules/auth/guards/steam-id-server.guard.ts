import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
  Logger,
} from '@nestjs/common'
import { Request } from 'express'

@Injectable()
export class SteamIDServerGuard implements CanActivate {
  logger = new Logger(SteamIDServerGuard.name)

  canActivate(context: ExecutionContext): boolean {
    const req: Request = context.switchToHttp().getRequest()

    const isCallback = Object.keys(req.query).length > 1
    if (!isCallback) {
      return true
    }

    const allowedParams = [
      'openid.ns',
      'openid.mode',
      'openid.op_endpoint',
      'openid.claimed_id',
      'openid.identity',
      'openid.return_to',
      'openid.response_nonce',
      'openid.assoc_handle',
      'openid.signed',
      'openid.sig',
      'withReferer',
    ]

    const unknownQueryParams = Object.keys(req.query).some(
      (property) => !allowedParams.includes(property)
    )

    const nsVersionMatch =
      req.query['openid.ns'] === 'http://specs.openid.net/auth/2.0'

    const isValid =
      nsVersionMatch &&
      this.isCommunityUrl(req.query['openid.claimed_id']) &&
      this.isCommunityUrl(req.query['openid.identity']) &&
      this.isCommunityUrl(req.query['openid.op_endpoint']) &&
      !unknownQueryParams

    if (!isValid) {
      throw new ForbiddenException('Go fuck yourself!')
    }

    return true
  }

  private isCommunityUrl(url?: unknown) {
    return (
      typeof url === 'string' &&
      url.startsWith('https://steamcommunity.com/openid/')
    )
  }
}
