import { User } from '@prisma/client'
import {
  BadRequestException,
  ExecutionContext,
  Injectable,
  mixin,
  UnauthorizedException,
} from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { AuthGuard, IAuthGuard } from '@nestjs/passport'
import { Request, Response } from 'express'
import { AuthService } from '../auth.service'
import { assert } from 'src/utils/assert'
import { RETURN_COOKIE } from '../middleware/keep-referer.middleware'

export interface Guard {
  new (): IAuthGuard
}

/**
 * Guard used only during OAuth authentication
 * Catches all errors and redirects to the appropriate URL
 * Saves refresh token as a secure cookie
 */
export const OAuthGuard = (type: string): Guard => {
  @Injectable()
  class MixinOauthGuard extends AuthGuard(type) {
    constructor(
      private auth: AuthService,
      private config: ConfigService
    ) {
      super()
    }

    handleRequest(err, user: User, _info, context: ExecutionContext): any {
      const req: Request = context.switchToHttp().getRequest()
      const res: Response = context.switchToHttp().getResponse()

      const customReturnCookie = req.cookies[RETURN_COOKIE]
      assert(
        typeof customReturnCookie === 'string' ||
          customReturnCookie === undefined
      )
      const customReturn = customReturnCookie || this.config.get('clientUrl')
      const returnUrl = new URL(customReturn)

      if (err) {
        const isSafeError = [UnauthorizedException, BadRequestException].some(
          (exception) => err instanceof exception
        )

        const message = isSafeError ? err.message : 'internal_error'
        if (!isSafeError) {
          this.auth.logger.error(
            { err },
            'failed to handle auth err=%s',
            err.message
          )
        }

        returnUrl.searchParams.set('failed', '1')
        returnUrl.searchParams.set('reason', message)
        res.redirect(returnUrl.toString())
        return
      }

      const jwt = this.auth.signRefreshToken(user.id)
      this.auth.setRefreshTokenCookie(res, jwt, user.role)

      res.redirect(returnUrl.toString())
      return user
    }
  }

  return mixin(MixinOauthGuard)
}
