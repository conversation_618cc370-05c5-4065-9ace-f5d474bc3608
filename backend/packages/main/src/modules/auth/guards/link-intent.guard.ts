import {
  CanActivate,
  ExecutionContext,
  ForbiddenException,
  Injectable,
  Logger,
} from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { Request, Response } from 'express'
import { MINUTE_MS } from 'src/utils/constants'

/**
 * Verifies that the link callback was actually initiated by the user
 */
@Injectable()
export class LinkIntentGuard implements CanActivate {
  logger = new Logger(LinkIntentGuard.name)
  static INTENT_COOKIE = 'auth-link-intent'
  static INTENT_VALUE = 'true'

  constructor(private config: ConfigService) {}

  canActivate(context: ExecutionContext): boolean {
    const req: Request = context.switchToHttp().getRequest()

    const isCallback = Object.keys(req.query).length
    if (!isCallback) {
      const res: Response = context.switchToHttp().getResponse()
      this.#setCookie(res)
      return true
    }

    if (!this.#checkCookie(req)) {
      throw new ForbiddenException('intent_expired')
    }

    return true
  }

  #setCookie(res: Response) {
    res.cookie(LinkIntentGuard.INTENT_COOKIE, LinkIntentGuard.INTENT_VALUE, {
      maxAge: 2 * MINUTE_MS,
      httpOnly: true,
      secure: !this.config.get('isStaging'),
    })
  }

  #checkCookie(req: Request): boolean {
    return (
      req.cookies[LinkIntentGuard.INTENT_COOKIE] ===
      LinkIntentGuard.INTENT_VALUE
    )
  }
}
