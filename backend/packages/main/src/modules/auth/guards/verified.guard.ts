import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common'
import { UserBadge } from '@prisma/client'
import { UserService } from 'src/modules/user/user.service'

@Injectable()
export class VerifiedGuard implements CanActivate {
  constructor(private userService: UserService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest()
    const user = await this.userService.byId(req.user.userId)

    return user.badge === UserBadge.VERIFIED
  }
}
