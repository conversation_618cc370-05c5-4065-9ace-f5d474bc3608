import {
  BadRequestException,
  CanActivate,
  ExecutionContext,
  Injectable,
} from '@nestjs/common'
import { UserService } from 'src/modules/user/user.service'

@Injectable()
export class DateGuard implements CanActivate {
  constructor(
    private readonly fields: string[],
    readonly userService?: UserService
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest()
    const user = this.userService
      ? await this.userService.byId(req.user.userId)
      : req.user

    const banField = this.fields.find(
      (field) => (user[field] || 0) > Date.now()
    )

    if (banField) {
      const type = { bannedUntil: 'Banned', mutedUntil: 'Muted' }[banField]
      const until = new Date(user[banField]).toLocaleString()

      throw new BadRequestException(`${type} until ${until} UTC`)
    }

    return true
  }
}

@Injectable()
export class BanGuard extends DateGuard {
  constructor() {
    super(['bannedUntil'])
  }
}

@Injectable()
export class MuteGuard extends DateGuard {
  constructor(readonly userService: UserService) {
    super(['mutedUntil', 'bannedUntil'], userService)
  }
}
