import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common'
import { Reflector } from '@nestjs/core'
import { assert } from 'src/utils/assert'
import { PassService } from '../pass/pass.service'

@Injectable()
export class ApiGuard implements CanActivate {
  constructor(
    private passService: PassService,
    private reflector: Reflector
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const req = context.switchToHttp().getRequest()
    const token = req.headers.authorization
      ? req.headers.authorization.split(' ')[1]
      : req.query.token
    assert(token, 'missing_api_token')

    const payload = await this.passService.verifyPass(token)
    const validator = this.reflector.get('validator', context.getHandler())
    assert(!validator || validator(payload), 'invalid_scope')

    req.api = payload

    return true
  }
}
