import { Injectable, NestMiddleware } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { Request, NextFunction } from 'express'
import { assert } from 'src/utils/assert'
import { MINUTE_MS } from 'src/utils/constants'

export const RETURN_COOKIE = 'oauth_return_url'

@Injectable()
export class KeepRefererMiddleware implements NestMiddleware {
  constructor(private config: ConfigService) {}

  use(req: Request, res: any, next: NextFunction) {
    if (req.query.withReferer === 'false') {
      next()
      return
    }

    const referer = req.headers.referer ?? this.config.get('clientUrl')
    let returnUrl = new URL(referer)

    const customReturnPath = req.query.returnPath
    if (customReturnPath) {
      assert(typeof customReturnPath === 'string')
      returnUrl = new URL(customReturnPath, returnUrl.toString())
    }

    res.cookie(RETURN_COOKIE, returnUrl.toString(), {
      maxAge: 5 * MINUTE_MS,
      domain: '.' + this.config.get('topLevelDomain'),
    })

    next()
  }
}
