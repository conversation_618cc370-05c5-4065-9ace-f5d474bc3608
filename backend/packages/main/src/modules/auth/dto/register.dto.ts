import {
  IsEnum,
  IsOptional,
  Length,
  Validate,
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  isEmail,
  isMobilePhone,
} from 'class-validator'
import { LoginDto } from './login.dto'
import { AuthOption } from '../auth.interface'

@ValidatorConstraint({ name: 'usernameEmailOrPhone', async: false })
class UsernameEmailOrPhoneConstraint implements ValidatorConstraintInterface {
  validate(value: any, args: ValidationArguments) {
    const object = args.object as RegisterDto
    if (object.type === AuthOption.PHONE) {
      return isMobilePhone(value)
    }
    return isEmail(value)
  }

  defaultMessage(_args: ValidationArguments) {
    return 'username must be a valid email or phone number'
  }
}

export class RegisterDto extends LoginDto {
  @IsEnum(AuthOption)
  type: AuthOption

  @Validate(UsernameEmailOrPhoneConstraint)
  username: string

  @IsOptional()
  @Length(2, 32)
  displayName?: string

  // ISO31661APLHA2
  // In the US, it's ISO31661APLHA2;STATE
  @IsOptional()
  @Length(2, 6)
  countryCode?: string
}
