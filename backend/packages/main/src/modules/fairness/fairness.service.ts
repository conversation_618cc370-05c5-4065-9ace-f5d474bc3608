import { Int } from '@crashgg/types/dist'
import { Injectable } from '@nestjs/common'
import { createHash, randomBytes, randomInt } from 'crypto'
import { PrismaService } from '../prisma/prisma.service'
import RandomOrg from 'random-org'
import { assert } from 'src/utils/assert'
import { RandomResponse } from './fairness.interface'
import { RedisService } from '../redis/redis.service'
import { md5, warn } from 'src/common/utilities'
import { DAY_S } from 'src/utils/constants'
import { EosService } from './eos/eos.service'
import { GetEosHashArgs } from './eos/eos.types'
import { RotatingSeedService } from './rotating-seed/rotating-seed.service'
import {
  AttachToSeed,
  RotatingSeedRequest,
  RotatingSeedResponse,
} from './rotating-seed/rotating-seed.interface'
import { ConfigService } from '../config/config.service'
import { Feature } from '@crashgg/common/dist'

@Injectable()
export class FairnessService {
  private random: RandomOrg
  isEos: boolean
  isRotatingSeed: boolean

  constructor(
    private prisma: PrismaService,
    private config: ConfigService,
    private redis: RedisService,
    private eosService: EosService,
    private rotatingSeedService: RotatingSeedService
  ) {
    const randomOrgKey = config.get('RANDOM_ORG_KEY')
    if (randomOrgKey) {
      this.random = new RandomOrg({
        apiKey: randomOrgKey,
        endpoint: process.env.RANDOM_ORG_ENDPOINT,
      } as any)
    }

    const isV2 = config.isFeature(Feature.FairnessV2)
    const isV1 = config.isFeature(Feature.FairnessV1)
    this.isEos = isV2
    this.isRotatingSeed = isV2

    if (isV2 === isV1) {
      throw new Error(
        'FairnessV1 and FairnessV2 cannot be enabled at the same time'
      )
    }
  }

  get eos() {
    return this.eosService
  }

  serverSeedFromSeed(seed: string) {
    const salt = this.config.get('auth.jwt.secret').slice(0, 30)

    return this.hash(salt + seed).substring(0, 32)
  }

  async getSerial(serialId: Int) {
    // TODO
    const serial = await this.prisma.read.randomSerial.findUnique({
      where: { serialId },
    })
    assert(serial, 'not_found')

    return serial
  }

  async #addSerial(random: any) {
    return this.prisma.randomSerial.create({
      data: {
        serialId: random.random.serialNumber,
        random,
      },
    })
  }

  async useRotatingSeed(
    request: RotatingSeedRequest
  ): Promise<RotatingSeedResponse> {
    if (this.isRotatingSeed) {
      return await this.rotatingSeedService.useRotatingSeed(request)
    }

    const { userId, clientSeed } = request
    const serverSeed = await this.getServerSeed(userId)
    const seed = `${serverSeed}:${clientSeed}`

    return {
      publicSeed: seed,
      privateSeed: seed,
      historyId: null,
      nonce: 0,
    }
  }

  async attachToSeed(request: AttachToSeed) {
    if (!this.isRotatingSeed) {
      return
    }
    await this.rotatingSeedService.attachToSeed(request)
  }

  /**
   * @deprecated use takeRotatingSeed instead
   */
  async getServerSeed(userId: Int) {
    const { serverSeed } = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { serverSeed: true },
    })
    if (serverSeed) return serverSeed

    return await this.createServerSeed(userId)
  }

  async getServerHash(userId: Int) {
    const seed = await this.getServerSeed(userId)

    return this.hash(seed)
  }

  async createServerSeed(userId: Int, hashed = false, usePrismaDirect = false) {
    if (this.isRotatingSeed) {
      return undefined
    }

    const serverSeed = randomBytes(12).toString('hex')

    const prisma = usePrismaDirect ? this.prisma.direct : this.prisma
    await prisma.user.update({
      where: { id: userId },
      data: { serverSeed },
    })

    return hashed ? this.hash(serverSeed) : serverSeed
  }

  hash(seed: string) {
    return createHash('sha256').update(seed).digest('hex')
  }

  async getRandomSeed(
    userData: Record<string, unknown>,
    onAwaiting: GetEosHashArgs['onAwaiting']
  ) {
    if (this.isEos) {
      const serverSeedSeed = JSON.stringify(userData)
      const serverSeed = this.serverSeedFromSeed(serverSeedSeed)

      const eos = await this.eosService.getEosHash({ onAwaiting, serverSeed })
      return {
        seed: eos.serverSeed + eos.blockchainSeed,
        proof: eos,
        serialId: null,
      }
    }

    if (!this.random) return this.#getRandomCryptoSeed()

    return await this.#getRandomOrgSeed(userData)
  }

  async #getRandomOrgSeed(userData?: Record<string, unknown>) {
    const res = await this.getRandomSafely<string>('generateSignedStrings', {
      n: 1,
      length: 32,
      characters:
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',
      userData,
    })

    return {
      seed: res.random.data?.[0],
      proof: { random: res.random, signature: res.signature },
      serialId: res.serial.id,
    }
  }

  #getRandomCryptoSeed() {
    const seed = randomBytes(16).toString('hex')

    return {
      seed,
      proof: {
        message: 'Could not reach Random.org, results generated in-house',
      },
      serialId: null,
    }
  }

  async getRandom<T>(
    method: keyof RandomOrg,
    params: any
  ): Promise<RandomResponse<T>> {
    const res: any = await this.random[method](params)
    const serial = await this.#addSerial(res)
    res.serial = serial

    return res
  }

  /**
   * Caches the random result in redis, so it is not requested twice in case of an error
   * The cache key is method and params combined
   */
  async getRandomSafely<T>(
    method: keyof RandomOrg,
    params: any
  ): Promise<RandomResponse<T>> {
    const key = `random-org:${md5(method + JSON.stringify(params))}`
    const cached = await this.redis.getJSON<RandomResponse<T>>(key)
    if (cached) return cached

    const response = await this.getRandom<T>(method, params)
    this.redis.set(key, response, DAY_S).catch(warn)

    return response
  }

  async getDiceRolls(count: Int, userData: Record<string, unknown>) {
    if (!this.random) return this.#getCryptoDiceRolls(count)

    try {
      return await this.#getRandomOrgDiceRolls(count, userData)
    } catch (err) {
      return this.#getCryptoDiceRolls(count)
    }
  }

  async #getRandomOrgDiceRolls(count: Int, userData: Record<string, unknown>) {
    const res = await this.random.generateSignedIntegers({
      n: count,
      min: 0,
      max: 99_99,
      replacement: false,
      userData,
    })

    await this.#addSerial(res)

    return {
      rolls: res.random.data,
      proof: res,
    }
  }

  #getCryptoDiceRolls(count: Int) {
    const rolls = []

    while (rolls.length < count) {
      const roll = randomInt(0, 100_00)
      if (!rolls.includes(roll)) rolls.push(roll)
    }

    return {
      rolls,
      proof: {
        signature: 'Could not reach Random.org, results generated in-house',
      },
    }
  }

  async getLotteryNumbers(lotteryId: Int) {
    const res = await this.random.generateSignedIntegers({
      n: 6,
      min: 0,
      max: 9,
      replacement: true,
      userData: { lotteryId },
    })
    await this.#addSerial(res)

    return {
      numbers: res.random.data.join(''),
      proof: res,
    }
  }
}
