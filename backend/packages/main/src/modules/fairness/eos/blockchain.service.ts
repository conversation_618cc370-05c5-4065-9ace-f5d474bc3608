import { Injectable, Logger } from '@nestjs/common'
import axios, { Method } from 'axios'
import { prandom } from 'src/common/utilities'
import { BlockchainBlockInfo, BlockchainInfo, CurrentBlock } from './eos.types'

@Injectable()
export class BlockchainService {
  private readonly logger = new Logger(BlockchainService.name)
  private readonly API_NODES = ['https://eos.api.eosnation.io']

  async getCurrentBlock(): Promise<CurrentBlock> {
    const res = await this.request<BlockchainInfo>('GET', '/v1/chain/get_info')
    return {
      num: res.head_block_num,
      id: res.head_block_id,
    }
  }

  async getBlock(blockNum: Int): Promise<BlockchainBlockInfo> {
    try {
      const res = await this.request<BlockchainBlockInfo>(
        'GET',
        '/v1/chain/get_block_info',
        { block_num: blockNum }
      )

      return res
    } catch (err) {
      // Block is not found
      if (err.response?.status === 400) {
        return null
      }

      throw err
    }
  }

  private async request<T>(
    method: Method,
    path: string,
    data?: any
  ): Promise<T> {
    const nodeUrl = prandom(this.API_NODES)

    const res = await axios
      .request<T>({
        method,
        url: nodeUrl + path,
        data,
      })
      .catch((err) => {
        const isOurError = err.message.includes(
          'Request failed with status code 4'
        )
        if (!isOurError) {
          this.logger.warn(
            'Failed to request node=%s method=%s %o',
            nodeUrl,
            method,
            err
          )
        }
        throw err
      })

    return res.data
  }
}
