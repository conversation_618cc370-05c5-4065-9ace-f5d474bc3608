import { Injectable, Logger } from '@nestjs/common'
import { RedisService } from 'src/modules/redis/redis.service'
import { BlockchainService } from './blockchain.service'
import { BlockchainBlockInfo, GetEosHashArgs } from './eos.types'
import { sleep } from '@crashgg/common/dist'
import { PrismaService } from 'src/modules/prisma/prisma.service'
import { createHash } from 'crypto'
import { EosStatus } from '../fairness.interface'

@Injectable()
export class EosService {
  private readonly logger = new Logger(EosService.name)

  constructor(
    private readonly blockchainService: BlockchainService,
    private readonly redisService: RedisService,
    private readonly prisma: PrismaService
  ) {}

  static WAIT_BLOCKS = 8
  static MS_PER_BLOCK = 500

  async getEosHash({ onAwaiting, serverSeed }: GetEosHashArgs) {
    const serverHash = createHash('sha256').update(serverSeed).digest('hex')

    const current = await this.blockchainService.getCurrentBlock()
    const awaitingNum = current.num + EosService.WAIT_BLOCKS
    this.logger.log('EOS awaiting num=%d', awaitingNum)
    onAwaiting({
      current,
      awaitingNum,
      serverHash,
      status: EosStatus.Awaiting,
    })

    await sleep(EosService.WAIT_BLOCKS * EosService.MS_PER_BLOCK)

    let block: BlockchainBlockInfo
    while (!block) {
      block = await this.blockchainService.getBlock(awaitingNum)
      if (!block) {
        this.logger.log('EOS block not yet available num=%d', awaitingNum)
        await sleep(EosService.MS_PER_BLOCK)
      }
    }

    return {
      status: EosStatus.Completed,
      startBlockNum: current,
      blockNum: awaitingNum,
      blockchainSeed: block.id,
      serverSeed,
      serverHash,
    }
  }

  async() {}
}
