import { EosStatus } from '../fairness.interface'

export interface BlockchainInfo {
  server_version: string
  chain_id: string
  head_block_num: number
  last_irreversible_block_num: number
  last_irreversible_block_id: string
  head_block_id: string
  head_block_time: string
  head_block_producer: string
  virtual_block_cpu_limit: number
  virtual_block_net_limit: number
  block_cpu_limit: number
  block_net_limit: number
  server_version_string: string
  fork_db_head_block_num: number
  fork_db_head_block_id: string
  server_full_version_string: string
  total_cpu_weight: string
  total_net_weight: string
  earliest_available_block_num: number
  last_irreversible_block_time: string
}

export interface BlockchainBlockInfo {
  id: string | null
}

export interface CurrentBlock {
  num: number
  id: string
}

export interface GetEosHashArgs {
  serverSeed: string
  onAwaiting: (payload: {
    status: EosStatus.Awaiting
    current: CurrentBlock
    awaitingNum: Int
    serverHash: string
  }) => void
}
