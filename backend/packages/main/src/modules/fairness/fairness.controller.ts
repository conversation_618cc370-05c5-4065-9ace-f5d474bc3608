import {
  <PERSON>,
  Get,
  MethodNotAllowedException,
  Param,
  Req,
  UseGuards,
} from '@nestjs/common'
import { AccessGuard } from '../auth/guards/access.guard'
import { RedisService } from '../redis/redis.service'
import { FairnessService } from './fairness.service'
import { Throttle } from '@nestjs/throttler'
import { HIGH_THROUGHPUT } from 'src/common/throttler.constants'
import { FeatureGuard } from '../config/feature.guard'
import { IsFeature } from '../config/is-feature.decorator'
import { Feature } from '@crashgg/common/dist'

@Controller('fairness')
@IsFeature(Feature.FairnessV1)
@UseGuards(FeatureGuard)
export class FairnessController {
  constructor(
    private fairnessService: FairnessService,
    private redis: RedisService
  ) {}

  @Get('serial/:id')
  async getSerial(@Param('id') id: string) {
    if (this.fairnessService.isEos) {
      return new MethodNotAllowedException()
    }

    return this.fairnessService.getSerial(+id)
  }

  @Throttle(HIGH_THROUGHPUT)
  @Get('server-hash')
  @UseGuards(AccessGuard)
  async getServerHash(@Req() req) {
    if (this.fairnessService.isRotatingSeed) {
      return new MethodNotAllowedException()
    }

    const userId = req.user.userId
    const lock = await this.redis.lock([`fairness:seed:${userId}`])
    try {
      const serverHash = await this.fairnessService.getServerHash(userId)

      return { serverHash }
    } finally {
      lock.release()
    }
  }
}
