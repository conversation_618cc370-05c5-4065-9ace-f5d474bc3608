import { Injectable, Logger } from '@nestjs/common'
import { CrapsStatus, RotatingSeed } from '@prisma/client'
import { createHash, randomBytes } from 'crypto'
import { PrismaService } from 'src/modules/prisma/prisma.service'
import {
  AttachToSeed,
  HistoryOutcome,
  RawNonce,
  RotatingSeedRequest,
  RotatingSeedResponse,
} from './rotating-seed.interface'
import { RotatingSeedEntity } from './rotating-seed.entity'
import { instanceToPlain } from 'class-transformer'
import { assert } from 'src/utils/assert'
import { CasesService } from 'src/modules/games/cases/cases.service'
import { getPlinkoOutcomesFromNonce } from 'src/modules/games/plinko/plinko.helpers'
import { getMinesOutcomesFromNonce } from 'src/modules/games/mines/mines.helpers'
import { getDiceOutcomesFromNonce } from 'src/modules/games/dice/dice.helpers'
import { getKenoOutcomesFromNonce } from 'src/modules/games/keno/keno.helpers'
import { getBlackjackOutcomesFromNonce } from 'src/modules/games/blackjack/blackjack.helpers'
import { getRouletteSpOutcomesFromNonce } from 'src/modules/games/roulette-sp/roulette-sp.helpers'
import { getLossbackCaseOutcomesFromNonce } from 'src/modules/rewards/lossback-case/lossback-case.helpers'
import { getCrapsOutcomesFromNonce } from 'src/modules/games/craps/craps.helpers'
import { getBaccaratOutcomesFromNonce } from 'src/modules/games/baccarat/baccarat.helpers'
import { Status as MinesStatus } from 'src/modules/games/mines/mines.interface'
import { warn } from 'src/common/utilities'

@Injectable()
export class RotatingSeedService {
  private logger = new Logger(RotatingSeedService.name)

  constructor(private prisma: PrismaService) {}

  async getPublicRotatingSeed(userId: Int) {
    const rotatingSeed = await this.getRotatingSeed(userId)
    return new RotatingSeedEntity(rotatingSeed)
  }

  async getRotatingSeed(
    userId: Int,
    doIncrement = false
  ): Promise<RotatingSeed> {
    // Cannot use upsert as userId is not unique
    const foundSeed = await this.prisma.rotatingSeed.findFirst({
      where: { userId, isRetired: false },
    })
    if (foundSeed) {
      if (doIncrement) {
        const updated = await this.prisma.rotatingSeed.update({
          where: { id: foundSeed.id },
          data: {
            nonce: { increment: 1 },
          },
          select: { nonce: true },
        })

        // Retire seeds after n results
        if (updated.nonce > 1000) {
          await this.createRotatingSeed(userId).catch(warn)
        }
      }

      return foundSeed
    }

    return await this.createRotatingSeed(userId)
  }

  private async _canRetireRotatingSeed(userId: Int) {
    const pendingGames = await Promise.all([
      this.prisma.bombRun.count({
        where: { userId, status: 'IN_PROGRESS' },
      }),
      this.prisma.mines.count({
        where: { userId, status: MinesStatus.Playing },
      }),
      this.prisma.blackjack.count({
        where: { userId, status: 'in-progress' },
      }),
      this.prisma.craps.count({
        where: { userId, status: CrapsStatus.IN_PROGRESS },
      }),
    ])

    return !pendingGames.some((count) => count > 0)
  }

  async getPublicRotatingSeedHistory(userId: Int) {
    const seeds = await this.prisma.read.rotatingSeed.findMany({
      where: {
        userId,
        isRetired: true,
      },
      take: 50,
      orderBy: { id: 'desc' },
    })

    return seeds.map((seed) => new RotatingSeedEntity(seed))
  }

  async getRotatingSeedHistoryEntry(userId: Int, serverHash: string) {
    const seed = await this.prisma.read.rotatingSeed.findFirst({
      where: { userId, serverHash, isRetired: true },
    })
    assert(seed, 'seed_not_found')
    const publicSeed = instanceToPlain(new RotatingSeedEntity(seed))

    const nonces: RawNonce[] =
      await this.prisma.read.rotatingSeedHistory.findMany({
        where: { seedId: seed.id },
        select: {
          nonce: true,
          clientSeed: true,
          caseDrops: {
            select: {
              seed: true,
              item: true,
            },
          },
          upgrade: {
            select: {
              seed: true,
              result: true,
            },
          },
          Bet: {
            select: {
              data: true,
              game: true,
              Mines: {
                select: {
                  state: true,
                },
              },
              dice: {
                select: { serverSeed: true, clientSeed: true, result: true },
              },
              keno: {
                select: {
                  seed: true,
                  result: true,
                },
              },
              blackjack: {
                select: {
                  state: true,
                },
              },
              crapsRound: {
                select: {
                  results: true,
                  publicSeed: true,
                },
              },
            },
          },
        },
      })

    const outcomes = nonces
      .flatMap((nonce): HistoryOutcome[] => {
        if (nonce.caseDrops?.length) {
          return CasesService.getOutcomesFromNonce(nonce)
        } else if (nonce.Bet?.length) {
          const bet = nonce.Bet[0]
          if (bet.game === 'plinko') {
            return getPlinkoOutcomesFromNonce(nonce)
          } else if (bet.game === 'mines') {
            return getMinesOutcomesFromNonce(nonce)
          } else if (bet.game === 'dice') {
            return getDiceOutcomesFromNonce(nonce)
          } else if (bet.game === 'keno') {
            return getKenoOutcomesFromNonce(nonce)
          } else if (bet.game === 'blackjack') {
            return getBlackjackOutcomesFromNonce(nonce)
          } else if (bet.game === 'roulette-sp') {
            return getRouletteSpOutcomesFromNonce(nonce)
          } else if (bet.game === 'lossback-case') {
            return getLossbackCaseOutcomesFromNonce(nonce)
          } else if (bet.game === 'craps') {
            return getCrapsOutcomesFromNonce(nonce)
          } else if (bet.game === 'baccarat') {
            return getBaccaratOutcomesFromNonce(nonce)
          } else {
            this.logger.warn('Unknown nonce bet type %o', nonce)
            return []
          }
        } else {
          this.logger.warn('Unknown nonce type %o', nonce)
          return []
        }
      })
      .map((outcome): HistoryOutcome => {
        // only replaces first `x`
        outcome.seed = outcome.seed.replace('x', publicSeed.serverSeed)
        return outcome
      })

    return { seed: publicSeed, outcomes }
  }

  async retirePreviousSeed(userId: Int) {
    const canRetire = await this._canRetireRotatingSeed(userId)
    assert(canRetire, 'cant_retire_previous_seed')

    await this.prisma.rotatingSeed.updateMany({
      where: { userId, isRetired: false },
      data: { isRetired: true },
    })
  }

  async createRotatingSeed(userId: Int): Promise<RotatingSeed> {
    await this.retirePreviousSeed(userId)

    const serverSeed = randomBytes(12).toString('hex')
    const serverHash = createHash('sha256').update(serverSeed).digest('hex')

    const seed = await this.prisma.rotatingSeed.create({
      data: {
        user: {
          connect: { id: userId },
        },
        serverSeed,
        serverHash,
        nonce: 1,
      },
    })

    seed.nonce = 0
    return new RotatingSeedEntity(seed)
  }

  async useRotatingSeed(
    request: RotatingSeedRequest
  ): Promise<RotatingSeedResponse> {
    const { userId, clientSeed, attachments = {} } = request

    const rotatingSeed = await this.getRotatingSeed(userId, true)

    const history = await this.prisma.rotatingSeedHistory.create({
      data: {
        seedId: rotatingSeed.id,
        nonce: rotatingSeed.nonce,
        clientSeed,
        ...attachments,
      },
    })

    const publicSeedParts = [clientSeed, history.nonce]

    const response: RotatingSeedResponse = {
      historyId: history.id,
      nonce: history.nonce,
      privateSeed: [rotatingSeed.serverSeed, ...publicSeedParts].join(':'),
      publicSeed: ['x', ...publicSeedParts].join(':'),
    }

    return response
  }

  async attachToSeed(request: AttachToSeed) {
    await (request.prisma ?? this.prisma).rotatingSeedHistory.update({
      where: { id: request.id },
      data: request.attachments,
    })
  }
}
