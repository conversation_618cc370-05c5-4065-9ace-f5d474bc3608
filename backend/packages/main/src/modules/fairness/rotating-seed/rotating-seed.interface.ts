import { PrismaTx } from '@crashgg/common/dist'
import { Prisma } from '@prisma/client'

export interface RotatingSeedRequest {
  userId: Int
  clientSeed: string
  attachments?: Partial<Prisma.RotatingSeedHistoryUncheckedCreateInput>
}

export interface RotatingSeedResponse {
  // Rotating seed: 'x' + public seed + nonce
  // v1: same as `privateSeed`
  publicSeed: string
  // Rotating seed: private seed + public seed + nonce
  // v1: server seed + client seed
  privateSeed: string
  nonce: Int
  historyId: Int | null
}

export interface AttachToSeed {
  id: Int
  attachments: Partial<Prisma.RotatingSeedHistoryUncheckedCreateInput>
  prisma?: PrismaTx
}

export interface RawNonce {
  nonce: number
  clientSeed: string
  upgrade: {
    result: number
    seed: string
  }
  caseDrops: {
    seed: string
    item: Prisma.JsonValue
  }[]
  Bet: {
    data: Prisma.JsonValue
    game: string
    Mines: {
      state: Prisma.JsonValue
    }[]
    dice: {
      serverSeed: string
      clientSeed: string
      result: number
    }
    keno: {
      seed: string
      result: number[]
    }
    blackjack: {
      state: Prisma.JsonValue
    }
    crapsRound: {
      results: Prisma.JsonValue
      publicSeed: string
    }
  }[]
}

export interface HistoryOutcome {
  nonce: Int
  type:
    | 'CASES'
    | 'UPGRADER'
    | 'PLINKO'
    | 'DICE'
    | 'MINES'
    | 'KENO'
    | 'BLACKJACK'
    | 'ROULETTE'
    | 'LOSSBACK_CASE'
    | 'CRAPS'
    | 'BACCARAT'
  seed: string
  outcome: string | number[][] | object
}
