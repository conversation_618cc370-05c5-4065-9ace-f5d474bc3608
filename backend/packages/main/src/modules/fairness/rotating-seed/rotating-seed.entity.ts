import { RotatingSeed } from '@prisma/client'
import { Exclude, Transform } from 'class-transformer'

export class RotatingSeedEntity implements RotatingSeed {
  @Transform((params) => {
    return params.obj.isRetired ? params.value : undefined
  })
  serverSeed: string

  serverHash: string
  nonce: number
  createdAt: Date

  @Exclude() id: number
  @Exclude() userId: number
  @Exclude() isRetired: boolean
  @Exclude() updatedAt: Date

  constructor(partial: Partial<RotatingSeedEntity>) {
    Object.assign(this, partial)
  }
}
