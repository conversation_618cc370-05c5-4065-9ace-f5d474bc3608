import { <PERSON>, <PERSON>, Param, Post, UseGuards } from '@nestjs/common'
import { RotatingSeedService } from './rotating-seed.service'
import { RedisService } from 'src/modules/redis/redis.service'
import { UserId } from 'src/common/user-id.decorator'
import { AccessGuard } from 'src/modules/auth/guards/access.guard'
import { BanGuard } from 'src/modules/auth/guards/ban.guard'
import { assert } from 'src/utils/assert'
import { IsFeature } from 'src/modules/config/is-feature.decorator'
import { Feature } from '@crashgg/common/dist'
import { FeatureGuard } from 'src/modules/config/feature.guard'

@IsFeature(Feature.FairnessV2)
@UseGuards(AccessGuard, BanGuard, FeatureGuard)
@Controller('fairness/rotating-seed')
export class RotatingSeedController {
  constructor(
    private readonly rotatingSeedService: RotatingSeedService,
    private readonly redis: RedisService
  ) {}

  @Get()
  async getSeed(@UserId() userId: Int) {
    const lock = await this.redis.lock([`fairness:seed:${userId}`])

    return await this.rotatingSeedService
      .getPublicRotatingSeed(userId)
      .finally(lock.release)
  }

  @Get('history')
  async getHistory(@UserId() userId: Int) {
    const data =
      await this.rotatingSeedService.getPublicRotatingSeedHistory(userId)
    return { data }
  }

  @Get('history/:hash')
  async getHistoryEntry(@UserId() userId: Int, @Param('hash') hash: string) {
    assert(userId && hash)
    return await this.rotatingSeedService.getRotatingSeedHistoryEntry(
      userId,
      hash
    )
  }

  @Post()
  async createSeed(@UserId() userId: Int) {
    const lock = await this.redis.lock([`fairness:seed:${userId}`])

    return await this.rotatingSeedService
      .createRotatingSeed(userId)
      .finally(lock.release)
  }
}
