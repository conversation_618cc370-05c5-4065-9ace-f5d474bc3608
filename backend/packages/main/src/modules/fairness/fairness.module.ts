import { Module } from '@nestjs/common'
import { FairnessService } from './fairness.service'
import { FairnessController } from './fairness.controller'
import { EosModule } from './eos/eos.module'
import { RotatingSeedModule } from './rotating-seed/rotating-seed.module'

@Module({
  providers: [FairnessService],
  controllers: [FairnessController],
  exports: [FairnessService],
  imports: [EosModule, RotatingSeedModule],
})
export class FairnessModule {}
