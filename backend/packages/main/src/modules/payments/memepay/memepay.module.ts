import { Module, forwardRef } from '@nestjs/common'
import { UserModule } from 'src/modules/user/user.module'
import { PaymentsModule } from '../payments.module'
import { FireblocksModule } from '../fireblocks/fireblocks.module'
import { MemepayService } from './memepay.service'
import { MemepayController } from './memepay.controller'

@Module({
  imports: [forwardRef(() => PaymentsModule), UserModule, FireblocksModule],
  providers: [MemepayService],
  controllers: [MemepayController],
})
export class MemepayModule {}
