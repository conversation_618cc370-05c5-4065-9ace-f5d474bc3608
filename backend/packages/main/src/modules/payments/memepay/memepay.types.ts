export interface MemepayCoin {
  id: string
  symbol: string
  network: string
  name: string
  precision?: number
  contract_address?: string
  extra_id?: string
  address_regex?: string
  extra_id_regex?: string
  icon_url?: string
  liquidity?: number
  market_cap?: number
  market_price?: number
  change_last_24_hours?: number
  usd_last_24_hours?: number
  updated_at: string | null
}

export interface GetTokensResponse {
  data: MemepayCoin[]
  meta: {
    current_page: number
    from: number
    last_page: number
    links: {
      url: string
      label: string
      active: boolean
    }
    path: string
    per_page: number
    to: number
    total: number
  }
  links: {
    first: string
    last: string
    prev: string
    next: string
  }
}
