import { strBool } from '@crashgg/common/dist'
import { registerAs } from '@nestjs/config'

export default registerAs('numipay', () => ({
  apiKey: process.env.NUMIPAY_API_KEY,
  signaturePassword: process.env.NUMIPAY_SIGNATURE_PASS,
  depositRate: parseFloat(process.env.NUMIPAY_DEPOSIT_RATE) || 1.4,
  isSandbox: strBool(process.env.NUMIPAY_IS_SANDBOX, false),
  levelRequired: parseInt(process.env.NUMIPAY_LEVEL_REQUIRED) || 5,
}))
