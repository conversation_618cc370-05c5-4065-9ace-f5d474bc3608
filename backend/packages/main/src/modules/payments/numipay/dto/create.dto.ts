import {
  <PERSON><PERSON><PERSON>,
  IsInt,
  IsString,
  IsISO31661<PERSON><PERSON>pha2,
  Is<PERSON><PERSON>berString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Matches,
  IsIn,
  IsOptional,
} from 'class-validator'
import { PROVIDERS_ID, VALID_TRANSACTION_AMOUNTS } from '../numipay.constants'
import { Method } from '../numipay.types'

export class CreateDto {
  @IsIn(Object.keys(PROVIDERS_ID))
  @IsOptional()
  method?: Method

  @IsInt()
  @IsIn(VALID_TRANSACTION_AMOUNTS)
  amount: Int

  @IsString()
  firstName: string

  @IsString()
  lastName: string

  @IsEmail()
  email: string

  @IsString()
  zipCode: string

  @IsString()
  address: string

  @IsString()
  city: string

  @IsString()
  @Matches(/^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/\d{4}$/, {
    message: 'DOB must be in DD/MM/YYYY format',
  })
  dob: string

  @IsISO31661Alpha2()
  phoneCountryCode: string

  @IsNumberString()
  phoneNumber: string // without prefix

  @IsNumberString()
  @MinLength(1)
  @MaxLength(3)
  phoneNumberPrefix: string
}
