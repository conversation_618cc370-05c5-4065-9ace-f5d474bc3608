import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { AccessGuard } from 'src/modules/auth/guards/access.guard'
import { BanGuard } from 'src/modules/auth/guards/ban.guard'
import { NumipayService } from './numipay.service'
import { UserId } from 'src/common/user-id.decorator'
import { CreateDto } from './dto/create.dto'
import { WebhookGuard } from './webhook.guard'
import { SkipThrottle } from '@nestjs/throttler'
import { SKIP_ALL } from 'src/common/throttler.constants'
import { WebhookResponse } from './numipay-sdk/numipay-sdk.types'
import { SUCCESS } from 'src/common/constants'
import { BASE_PATH } from './numipay.constants'
import { RedisService } from '@crashgg/common/dist'

@Controller(BASE_PATH)
export class NumipayController {
  constructor(
    private numipayService: NumipayService,
    private redis: RedisService
  ) {}

  @Post()
  @UseGuards(AccessGuard, BanGuard)
  async create(@UserId() userId: Int, @Body() createDto: CreateDto) {
    createDto.method ??= 'PAYPAL'
    return await this.numipayService.create(userId, createDto)
  }

  @Post('/webhook')
  @UseGuards(WebhookGuard)
  @SkipThrottle(SKIP_ALL)
  async webhook(@Body() body: WebhookResponse) {
    await this.numipayService.handleTransactionStatusChange(body)

    return SUCCESS
  }
}
