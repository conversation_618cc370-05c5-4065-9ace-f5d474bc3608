import { BadRequestException, Injectable } from '@nestjs/common'
import { CreateDto } from './dto/create.dto'
import { NumipaySdkAccess } from './numipay-sdk/numipay-sdk.access'
import { UserService } from 'src/modules/user/user.service'
import { IpLockAccess } from 'src/modules/ip-lock/ip-lock.access'
import { ConfigService } from '@nestjs/config'
import { assert } from 'src/utils/assert'
import {
  NUMIPAY_BANK_PROVIDER,
  NUMIPAY_PAYPAL_PROVIDER,
  BANK_ALLOWED_COUNTRIES,
  PAYPAL_BLOCKED_COUNTRIES,
  PROVIDERS_ID,
  TRANSACTION_CURRENCY,
  WEBHOOK_PATH,
} from './numipay.constants'
import { PayinCustomer, WebhookResponse } from './numipay-sdk/numipay-sdk.types'
import { PaymentsService } from '../payments.service'
import { Currency } from '@crashgg/types/dist'
import { User } from '@prisma/client'
import { DAY_MS, levelToXp } from '@crashgg/common/dist'
import { PaymentsAccess } from '../payments.access'
import { Method } from './numipay.types'

@Injectable()
export class NumipayService {
  constructor(
    private config: ConfigService,
    private sdk: NumipaySdkAccess,
    private userService: UserService,
    private ipLockAccess: IpLockAccess,
    private paymentsService: PaymentsService,
    private paymentsAccess: PaymentsAccess
  ) {}

  async create(userId: Int, data: CreateDto) {
    // TODO: remove when FE catches up
    data.method ??= 'PAYPAL'

    const [user, userIp] = await Promise.all([
      this.userService.byId(userId),
      this.ipLockAccess.getMostRecentUserIp(userId),
    ])

    const userCountries = [user.countryCode, userIp.countryCode].filter(Boolean)
    this._checkGeoRestrictions(data.method, userCountries)
    if (data.method === 'PAYPAL') {
      this._checkLevelRequirement(user)
      await this._checkDepositLimits(user, data.method, data.amount)
    }

    const amount = data.amount / 100
    const userKey = this._userKey(userId)
    const transactionId = `${userKey}:${data.method}:${Date.now()}`

    user.countryCode ??= userIp.countryCode
    const customer = this._createCustomerData(user, data)

    try {
      const response = await this.sdk.payin({
        provider_id: PROVIDERS_ID[data.method],
        currency: TRANSACTION_CURRENCY,
        user_id: userKey,
        tracking_id: transactionId,
        ip: userIp.address,
        amount,
        settings: {
          status_url: this.config.get('apiUrlInternal') + WEBHOOK_PATH,
          return_url: `${this.config.get('clientUrl')}/?redirect-from=numipay`,
        },
        customer,
      })

      return { url: response.url }
    } catch {
      throw new BadRequestException('failed_to_create_transaction')
    }
  }

  async handleTransactionStatusChange(data: WebhookResponse) {
    if (data.status === 'successful') {
      assert(data.currency === TRANSACTION_CURRENCY)

      const amountUsd = parseFloat(data.amount) * 100
      const userId = parseInt(data.user_id.split('-')[1])
      const method = data.tracking_id.split(':')[1] as Method
      const provider =
        method === 'PAYPAL' ? NUMIPAY_PAYPAL_PROVIDER : NUMIPAY_BANK_PROVIDER

      const balanceChange = Math.floor(
        amountUsd * this.config.get('numipay.depositRate')
      )
      await this.paymentsService.handleDeposit({
        userId,
        amountUsd,
        balanceChange,
        balanceCurrency: Currency.REAL,
        isFiat: true,
        externalId: `numipay:${data.transaction_id}`,
        provider,
      })
    }
  }

  private _checkLevelRequirement(user: User) {
    const levelRequired = this.config.get('numipay.levelRequired')
    const xpRequired = levelToXp(levelRequired)
    assert(user.xp >= xpRequired, 'level_too_low')
  }

  private async _checkDepositLimits(user: User, method: Method, amount: Int) {
    if (method === 'BANK') return

    const restrictions =
      await this.userService.restrictions.getUserRestrictions(user.id)

    const [perUser, perBrand] = await Promise.all([
      this.paymentsAccess.checkPaymentLimit({
        amountUsd: amount,
        limit: restrictions.numipayPaypalDepositLimit,
        msAgo: DAY_MS,
        type: 'deposit',
        provider: NUMIPAY_PAYPAL_PROVIDER,
        userId: user.id,
      }),
      this.paymentsAccess.checkPaymentLimit({
        amountUsd: amount,
        limit: 50000_00,
        msAgo: DAY_MS,
        type: 'deposit',
        provider: NUMIPAY_PAYPAL_PROVIDER,
      }),
    ])

    assert(!perUser.isExceeded, 'user_limit_exceeded')
    assert(!perBrand.isExceeded, 'brand_limit_exceeded')
  }

  private _checkGeoRestrictions(method: Method, userCountries: string[]) {
    const restrictions: Record<Method, () => boolean> = {
      PAYPAL: () =>
        userCountries.some((cc) => PAYPAL_BLOCKED_COUNTRIES.includes(cc)),
      BANK: () =>
        !userCountries.every((cc) => BANK_ALLOWED_COUNTRIES.includes(cc)),
    }
    const isGeoRestricted = restrictions[method]()
    assert(!isGeoRestricted, 'unsupported_country')
  }

  private _createCustomerData(user: User, data: CreateDto): PayinCustomer {
    return {
      max_deposit_limit: false,
      kyc_status: 'completed',
      first_name: data.firstName,
      last_name: data.lastName,
      address: data.address,
      city: data.city,
      country_code: user.countryCode,
      dob: data.dob,
      email: data.email,
      zip_code: data.zipCode,
      phone: {
        country: data.phoneCountryCode,
        prefix: data.phoneNumberPrefix,
        number: data.phoneNumber,
      },
    }
  }

  private _userKey(userId: Int) {
    const siteCode = this.config.get('siteCode')
    return `${siteCode}-${userId}`
  }
}
