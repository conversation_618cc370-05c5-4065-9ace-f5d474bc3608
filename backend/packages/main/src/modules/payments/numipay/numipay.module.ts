import { Module, forwardRef } from '@nestjs/common'
import { NumipayController } from './numipay.controller'
import { NumipayService } from './numipay.service'
import { UserModule } from 'src/modules/user/user.module'
import { NumipaySdkAccess } from './numipay-sdk/numipay-sdk.access'
import { IpLockModule } from 'src/modules/ip-lock/ip-lock.module'
import { PaymentsModule } from '../payments.module'

@Module({
  imports: [UserModule, forwardRef(() => PaymentsModule), IpLockModule],
  controllers: [NumipayController],
  providers: [NumipayService, NumipaySdkAccess],
})
export class NumipayModule {}
