import { EUROPEAN_UNION_COUNTRY_CODES } from 'src/common/country-codes'

export const NUMIPAY_BANK_PROVIDER = 'numipay:bank'
export const NUMIPAY_PAYPAL_PROVIDER = 'numipay:paypal'

export const BASE_PATH = '/payments/numipay'
export const WEBHOOK_PATH = `${BASE_PATH}/webhook`
export const TRANSACTION_CURRENCY = 'USD'

export const VALID_TRANSACTION_AMOUNTS = [
  10_00, 20_00, 50_00, 100_00, 200_00, 250_00, 500_00,
]

export const PROVIDERS_ID = {
  PAYPAL: 2,
  BANK: 3,
} as const

export const BANK_ALLOWED_COUNTRIES = EUROPEAN_UNION_COUNTRY_CODES.filter(
  (cc) => cc !== 'DE'
)
export const PAYPAL_BLOCKED_COUNTRIES = ['US']
