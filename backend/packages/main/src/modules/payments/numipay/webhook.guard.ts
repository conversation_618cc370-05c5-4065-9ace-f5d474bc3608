import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
} from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { createHash } from 'crypto'
import { assert } from 'src/utils/assert'

@Injectable()
export class WebhookGuard implements CanActivate {
  private logger = new Logger('NumipayGuard')

  constructor(private config: ConfigService) {}

  canActivate(context: ExecutionContext): boolean | Promise<boolean> {
    const req = context.switchToHttp().getRequest()

    const theirSignature = req.get('x-signature')
    const data = req.body
    this.logger.log(`Numipay request data=%o`, data)
    const signaturePassword = this.config.get('numipay.signaturePassword')
    assert(signaturePassword)

    const payloadForHash = JSON.stringify(data) + signaturePassword
    const localSignature = createHash('sha1')
      .update(payloadForHash)
      .digest('hex')
      .toUpperCase()

    if (!localSignature === theirSignature) {
      this.logger.warn(
        'Numipay signature mismatch local=%s their=%s',
        localSignature,
        theirSignature
      )
      return false
    }

    return true
  }
}
