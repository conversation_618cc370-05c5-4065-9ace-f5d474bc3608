import { Injectable, Logger } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import axios, { AxiosRequestConfig, Method } from 'axios'
import { PayinRequest } from './numipay-sdk.types'

@Injectable()
export class NumipaySdkAccess {
  private logger = new Logger(NumipaySdkAccess.name)
  constructor(private readonly config: ConfigService) {}

  async payin(payinRequest: PayinRequest) {
    const mode = this.config.get('numipay.isSandbox') ? 'test' : 'prod'
    return await this._request<any>('post', '/payment/direct-integration', {
      request: { mode, ...payinRequest },
    })
  }

  private async _request<T>(
    method: Method,
    path: string,
    data: any = {},
    log = true
  ): Promise<T> {
    const apiKey = this.config.get('numipay.apiKey')

    const requestConfig: AxiosRequestConfig = {
      method,
      url: `https://gateway-api.numipay.co/main/v1${path}`,
      headers: {
        'x-api-key': apiKey,
      },
    }

    if (method.toUpperCase() === 'GET') {
      requestConfig.params = data
    } else {
      requestConfig.data = data
    }

    const res = await axios.request(requestConfig).catch((err) => {
      if (log)
        this.logger.warn(
          'Numipay request failed path=%s err=%o data=%o req=%o',
          path,
          err.message,
          err.response?.data,
          data
        )
      throw err
    })

    if (log)
      this.logger.log(
        'Numipay request sucessful path=%s data=%o res=%o',
        path,
        data,
        res.data
      )

    return res.data
  }
}
