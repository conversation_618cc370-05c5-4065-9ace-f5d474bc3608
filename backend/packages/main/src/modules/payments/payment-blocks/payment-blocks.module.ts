import { Module } from '@nestjs/common'
import { PaymentBlocksController } from './payment-blocks.controller'
import { PaymentBlocksService } from './payment-blocks.service'
import { UserModule } from 'src/modules/user/user.module'

@Module({
  imports: [UserModule],
  providers: [PaymentBlocksService],
  controllers: [PaymentBlocksController],
  exports: [PaymentBlocksService],
})
export class PaymentBlocksModule {}
