import { Injectable, Logger } from '@nestjs/common'
import { PAYMENTS_AVAILABILITY_KEY } from './payment-blocks.constants'
import { PaymentAvailability } from './payment-blocks.interface'
import { RedisService } from 'src/modules/redis/redis.service'
import { UpdateDto } from './dto/update.dto'
import { ConfigService } from '@nestjs/config'
import { assert } from 'src/utils/assert'
import { UserService } from 'src/modules/user/user.service'
import { PAYMENTS_PROVIDERS } from '../payment.guard'
import { PaymentType } from '../payments.interface'

@Injectable()
export class PaymentBlocksService {
  private readonly logger = new Logger(PaymentBlocksService.name)
  private allPayments: Record<string, PaymentAvailability>

  constructor(
    private readonly redis: RedisService,
    private readonly config: ConfigService,
    private readonly userService: UserService
  ) {
    this.populatePaymentProviders()
  }

  private populatePaymentProviders() {
    try {
      this.allPayments = Object.fromEntries(
        PAYMENTS_PROVIDERS.map((provider) => {
          const { provider: name, type, isCrypto } = provider

          const currencies = isCrypto
            ? this.config.get(`${name}.supportedCoins`) ?? ['USD']
            : ['USD']

          return currencies.map((currency: string) => [
            `${type}/${name}/${currency}`,
            PaymentAvailability.ENABLED,
          ])
        }).flat()
      )
    } catch (error) {
      this.logger.error('Cannot read allowable currencies of payment provider')
      throw error
    }
  }

  async getAll(): Promise<Record<string, PaymentAvailability>> {
    const availability = await this.redis.getJSON<
      Record<string, PaymentAvailability> | undefined
    >(PAYMENTS_AVAILABILITY_KEY)

    return {
      ...this.allPayments,
      ...(availability ?? {}),
    }
  }

  async setStatus(userId: Int, { method, status }: UpdateDto): Promise<void> {
    const all = await this.getAll()
    assert(method in all, 'invalid_method')

    const user = await this.userService.byId(userId)
    assert(
      user.role !== 'staff' ||
        (status === PaymentAvailability.MAINTENANCE &&
          all[method] === PaymentAvailability.ENABLED),
      'action_not_allowed'
    )

    all[method] = status
    await this.redis.set(PAYMENTS_AVAILABILITY_KEY, all)
  }

  async getStatus(method: string): Promise<PaymentAvailability> {
    const all = await this.getAll()
    return all[method] ?? PaymentAvailability.ENABLED
  }

  getPaymentString(
    type: PaymentType,
    provider: string,
    coinCode?: string
  ): string {
    const currency = coinCode || 'USD'
    return `${type}/${provider}/${currency}`
  }

  async getInactive(): Promise<Record<string, PaymentAvailability>> {
    const all = await this.getAll()
    return Object.fromEntries(
      Object.entries(all).filter(([_, v]) => v !== PaymentAvailability.ENABLED)
    )
  }
}
