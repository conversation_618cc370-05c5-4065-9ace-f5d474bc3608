import { Body, Controller, Get, Patch, UseGuards } from '@nestjs/common'
import { PaymentBlocksService } from './payment-blocks.service'
import { UpdateDto } from './dto/update.dto'
import { AccessGuard } from 'src/modules/auth/guards/access.guard'
import { ModGuard, StaffGuard } from 'src/modules/auth/guards/role.guard'
import { UserId } from 'src/common/user-id.decorator'
import { SUCCESS } from 'src/common/constants'

@Controller('payments/blocks')
export class PaymentBlocksController {
  constructor(private readonly paymentBlocks: PaymentBlocksService) {}

  @Get('all')
  @UseGuards(AccessGuard, ModGuard)
  async getAll() {
    return await this.paymentBlocks.getAll()
  }

  @Get('inactive')
  async getInactive() {
    return await this.paymentBlocks.getInactive()
  }

  @Patch()
  @UseGuards(AccessGuard, StaffGuard)
  async setStatus(@UserId() userId: Int, @Body() body: UpdateDto) {
    await this.paymentBlocks.setStatus(userId, body)
    return SUCCESS
  }
}
