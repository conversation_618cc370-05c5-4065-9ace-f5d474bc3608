import { Body, Controller, Get, Patch, Post, UseGuards } from '@nestjs/common'
import { MasspayService } from './masspay.service'
import { PaymentGuard, PaymentProvider } from '../payment.guard'
import { AccessGuard } from 'src/modules/auth/guards/access.guard'
import { BanGuard } from 'src/modules/auth/guards/ban.guard'
import { WithdrawLockGuard } from 'src/modules/user/withdraw-lock.guard'
import { TwoFactorGuard } from 'src/modules/auth/guards/two-factor.guard'
import { UserId } from 'src/common/user-id.decorator'
import { CurrencyUsed } from 'src/common/currency.decorator'
import { Currency } from '@crashgg/types/dist'
import { CashoutDto } from './dto/cashout.dto'
import { RedisService } from 'src/modules/redis/redis.service'
import { PaymentHandlingService } from '../payment-handling/payment-handling.service'
import { UpdateMasspayUserDto } from './dto/update-masspay-user.dto'
import { CreateMasspayUserDto } from './dto/create-masspay-user.dto'
import { IpLockGuard } from 'src/modules/ip-lock/ip-lock.guard'
import { RestrictionType } from 'src/modules/ip-lock/ip-lock.types'

@Controller('payments/masspay')
export class MasspayController {
  constructor(
    private masspayService: MasspayService,
    private redis: RedisService,
    private paymentHandlingService: PaymentHandlingService
  ) {}

  @Get('/user')
  @UseGuards(AccessGuard)
  async getUser(@UserId() userId: Int) {
    return await this.masspayService.getPublicUser(userId)
  }

  @Post('/user')
  @UseGuards(AccessGuard)
  async createUser(@UserId() userId: Int, @Body() data: CreateMasspayUserDto) {
    return await this.masspayService.createUser(userId, data)
  }

  @Patch('/user')
  @UseGuards(AccessGuard)
  async updateUser(@UserId() userId: Int, @Body() data: UpdateMasspayUserDto) {
    return await this.masspayService.updateUser(userId, data)
  }

  @Post('/cashout')
  @PaymentProvider({
    provider: 'masspay',
    type: 'withdraw',
    isCrypto: false,
  })
  @UseGuards(
    AccessGuard,
    BanGuard,
    PaymentGuard,
    WithdrawLockGuard({ enforceKyc: true }),
    TwoFactorGuard,
    IpLockGuard(RestrictionType.FIAT_CASHOUTS_DISABLED)
  )
  async cashout(
    @UserId() userId: Int,
    @Body() data: CashoutDto,
    @CurrencyUsed([Currency.REAL]) currency: Currency
  ) {
    const lock = await this.redis.lock([`charge:${userId}`])

    try {
      return await this.masspayService.cashout(userId, data, currency)
    } finally {
      lock.release()
    }
  }
}
