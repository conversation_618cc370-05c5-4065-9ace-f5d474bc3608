import { Currency } from '@crashgg/types/dist'
import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  Logger,
  ServiceUnavailableException,
} from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { CashoutDto } from './dto/cashout.dto'
import {
  ApiError,
  AttrValue,
  MasspayJsSdk,
  PayoutTxn,
  PayoutTxnCommitResp,
  StoredUser,
  UpdateUser,
} from 'masspay-js-sdk'
import { assert } from 'src/utils/assert'
import { USD } from 'src/utils/conversion'
import { PaymentHandlingService } from '../payment-handling/payment-handling.service'
import { UserService } from 'src/modules/user/user.service'
import { SUCCESS } from 'src/common/constants'
import { PaymentsService } from '../payments.service'
import { DESTINATIONS, MASSPAY_PROVIDER } from './masspay.constants'
import { PrismaService } from 'src/modules/prisma/prisma.service'
import { DAY_MS } from 'src/utils/constants'
import { RestrictionsService } from 'src/modules/user/restrictions/restrictions.service'
import { UpdateMasspayUserDto } from './dto/update-masspay-user.dto'
import { CreateMasspayUserDto } from './dto/create-masspay-user.dto'
import { KycService } from '../antifraud/kyc/kyc.service'
import { noop } from '@crashgg/common/dist'
import { fuzzyMatchNames, getFullName } from './masspay.helpers'
import { MasspayMethod } from './masspay.types'
import { WithdrawHandler } from '../withdraw-handler.class'
import { WithdrawJobPayload } from '../payment-handling/withdraw-handling.types'
import { Wrapped } from 'src/common/types/wrapped'

@Injectable()
export class MasspayService extends WithdrawHandler {
  protected logger = new Logger(MasspayService.name)
  private sdk: MasspayJsSdk

  constructor(
    private config: ConfigService,
    private prisma: PrismaService,
    protected userService: UserService,
    private paymentsService: PaymentsService,
    @Inject(forwardRef(() => PaymentHandlingService))
    protected paymentHandlingService: Wrapped<PaymentHandlingService>,
    private restrictionsService: RestrictionsService,
    private kycService: KycService
  ) {
    super(new Logger(MasspayService.name), userService, paymentHandlingService)
    const { apiKey, isSandbox } = this.config.get('masspay')
    this.sdk = new MasspayJsSdk({
      AUTHORIZER_NAME_API_KEY: 'Bearer ' + apiKey,
      BASE: isSandbox
        ? 'https://staging-api.masspay.io/v1.0.0'
        : 'https://api.masspay.io/v1.0.0',
    })
  }

  private _userKey(userId: Int) {
    const siteCode = this.config.get('siteCode')
    return `${siteCode}-${userId}`
  }

  async _getUser(userId: Int) {
    const userToken = await this._getUserToken(userId)
    return await this.sdk.UserService.getUserByToken(userToken)
  }

  async getPublicUser(userId: Int) {
    const user = await this._getUser(userId).catch(noop)
    if (!user) return { isRegistered: false }

    return {
      isRegistered: true,
      user: this._publicUser(user),
    }
  }

  async _getAttributeTokens(userToken: string, method: MasspayMethod) {
    const { token: destinationToken, attributeTypes } = DESTINATIONS[method]

    const attributes = await this.sdk.AttributeService.getAttrs(
      userToken,
      destinationToken,
      'USD'
    )

    // slow
    return Object.keys(attributeTypes).reduce(
      (attributeTokens, attr) => {
        const theirType = attributeTypes[attr]
        const matchingAttribute = attributes.find(
          (attribute) => attribute.type === theirType
        )

        if (matchingAttribute) {
          attributeTokens[attr] = matchingAttribute.token
        }

        return attributeTokens
      },
      {} as Record<string, string>
    )
  }

  private async _setTxAttributes(userToken: string, data: CashoutDto) {
    const destination = DESTINATIONS[data.method]
    const attributeTokens = await this._getAttributeTokens(
      userToken,
      data.method
    )
    this.logger.log('Got attibuteTokens=%o', attributeTokens)
    const attributeNames = Object.keys(attributeTokens)

    const siteName = this.config.get('siteName')

    ;(data.attributes as any).note = `${siteName} Redemption`

    const values: Array<AttrValue> = attributeNames.map((attr) => ({
      token: attributeTokens[attr],
      value: data.attributes[attr],
    }))

    try {
      await this.sdk.AttributeService.storeAttrs(
        userToken,
        destination.token,
        'USD',
        { values }
      )
    } catch (err: any) {
      this.logger.warn('Failed setting transaction attrs err=%o', err)
      throw new BadRequestException('wrong_transaction_attributes')
    }
  }

  private async _getAccount() {
    try {
      // dunno if there will be ever more than one account
      const account = await this.sdk.AccountService.getAccountBalance()
      return account[0]
    } catch (err: any) {
      this.logger.error('Cannot get masspay account err=%o', err)
      throw new ServiceUnavailableException('cannot_send_payout')
    }
  }

  async createUser(userId: number, data: CreateMasspayUserDto) {
    const userToken = await this._getUserToken(userId).catch(noop)
    assert(!userToken, 'masspay_user_already_exists')

    try {
      const user = await this.sdk.UserService.createUser(
        this._prepareUserPayload(userId, data)
      )
      return this._publicUser(user)
    } catch (err: any) {
      this.logger.warn('Failed to create masspay user err=%o', err)
      throw new ServiceUnavailableException('failed_to_create_masspay_user')
    }
  }

  async updateUser(userId: Int, data: UpdateMasspayUserDto) {
    const userToken = await this._getUserToken(userId)

    try {
      const user = await this.sdk.UserService.updateUser(userToken, {
        status: UpdateUser.status.ACTIVE,
        ...this._prepareUserPayload(userId, data),
      })
      return this._publicUser(user)
    } catch (err: any) {
      this.logger.warn('Failed to update masspay user err=%o', err)
      throw new ServiceUnavailableException('failed_to_update_masspay_user')
    }
  }

  private async _getUserToken(userId: Int) {
    try {
      const { user_token: userToken } = await this.sdk.UserService.userLookup(
        undefined,
        undefined,
        this._userKey(userId)
      )
      return userToken
    } catch (err: any) {
      throw new BadRequestException('masspay_user_not_found')
    }
  }

  private async _sendPayout(
    withdrawalId: string,
    sourceAmount: Int,
    userToken: string,
    sourceToken: string,
    destinationToken: string
  ) {
    const payload: PayoutTxn = {
      client_transfer_id: withdrawalId,
      source_amount: USD.toFloat(sourceAmount),
      source_token: sourceToken,
      source_currency_code: 'USD',
      destination_currency_code: 'USD',
      destination_token: destinationToken,
      auto_commit: true,
    }
    this.logger.log('Attempting payout data=%o', payload)

    const payoutTxnResp = (await this.sdk.PayoutService.initiatePayout(
      userToken,
      payload
    )) as PayoutTxnCommitResp

    assert(payoutTxnResp.status === 'success', payoutTxnResp.errors)

    return payoutTxnResp.payout_token
  }

  private async _checkFiatLimit(
    userId: number,
    amountUsd: number,
    msAgo: number,
    limit: number
  ) {
    const date = new Date(Date.now() - msAgo)
    const aggregated = await this.prisma.payment.aggregate({
      where: {
        provider: MASSPAY_PROVIDER,
        type: 'withdraw',
        userId,
        createdAt: {
          gte: date.toISOString(),
        },
      },
      _sum: { amountUsd: true },
    })

    const current = aggregated._sum.amountUsd || 0
    const isExceeded = current + amountUsd > limit

    return { limit, current, isExceeded }
  }

  private async _checkFiatLimits(userId: Int, amountUsd: Int) {
    const restrictions =
      await this.restrictionsService.getUserRestrictions(userId)

    const [daily, monthly] = await Promise.all([
      this._checkFiatLimit(
        userId,
        amountUsd,
        DAY_MS,
        restrictions.fiatWithdrawPerDay || 9_500_00
      ),
      this._checkFiatLimit(
        userId,
        amountUsd,
        30 * DAY_MS,
        restrictions.fiatWithdrawPerMonth || 100_000_00
      ),
    ])

    assert(!daily.isExceeded, 'exceeded_daily_fiat_limit')
    assert(!monthly.isExceeded, 'exceeded_monthly_fiat_limit')
  }

  private async _checkKycRestrictions(userId: Int) {
    const { kycDocumentId, kycName } = await this.kycService.getKycInfo(userId)
    assert(kycDocumentId || kycName, 'kyc_required')

    // withpersona.com kyc
    if (kycDocumentId && !kycDocumentId.startsWith('sumsub-')) return true

    const user = await this._getUser(userId)
    const masspayName = getFullName([user.first_name, user.last_name])

    // if we don't have documentId that means manual kyc
    if (!kycDocumentId) {
      const user = await this.userService.byId(userId)
      assert(user.countryCode === 'US', 'non_usa_citizen')
      assert(fuzzyMatchNames(masspayName, kycName), 'kyc_name_mismatch')
      return true
    }

    // subsum
    await this._checkSubSumKycRestrictions(kycDocumentId, masspayName)

    return true
  }

  async _checkSubSumKycRestrictions(documentId: string, masspayName: string) {
    const applicantId = documentId.replace('sumsub-', '')
    const applicant = await this.kycService.getSumSubApplicant(applicantId)

    const isUsaCitizen = applicant.info?.idDocs?.some(
      (document) => document.country === 'USA'
    )
    assert(isUsaCitizen, 'non_usa_citizen')

    const subSumName = getFullName([
      applicant.info.firstName,
      applicant.info.lastName,
    ])

    const matched = fuzzyMatchNames(masspayName, subSumName)
    assert(matched, 'kyc_name_mismatch')
  }

  async cashout(userId: Int, data: CashoutDto, currency: Currency) {
    const cashoutRate = this.config.get('masspay.cashoutRate')
    const destination = DESTINATIONS[data.method]
    const amountUsd = Math.floor(data.amount * cashoutRate)

    const [account, userToken] = await Promise.all([
      this._getAccount(),
      this._getUserToken(userId),
      this._checkFiatLimits(userId, amountUsd),
      this._checkKycRestrictions(userId),
    ])

    assert(
      account.balance >= USD.toFloat(amountUsd),
      'cannot_send_payment',
      ServiceUnavailableException
    )
    await this._setTxAttributes(userToken, data)

    const withdrawalId = this.paymentHandlingService.generatePaymentId()

    await this.userService.charge({
      userId,
      amount: data.amount,
      transaction: {
        category: 'cashier',
        message: 'Masspay cashout attempt',
      },
      currency,
    })

    const sourceAmount = Math.floor(
      amountUsd - amountUsd * destination.precentageFee - destination.fixedFee
    )

    await this.paymentHandlingService.initiateWithdrawal({
      provider: MASSPAY_PROVIDER,
      withdrawalId,
      amountUsd,
      amountBalance: data.amount,
      userId,
      currency,
      additionalPayload: {
        sourceAmount,
        userToken,
        accountToken: account.token,
        destinationToken: destination.token,
        attributes: data.attributes,
        method: data.method,
      },
    })

    return SUCCESS
  }

  async withdrawPayout(payload: WithdrawJobPayload) {
    try {
      const externalId = await this._sendPayout(
        payload.withdrawalId,
        payload.additionalPayload.sourceAmount,
        payload.additionalPayload.userToken,
        payload.additionalPayload.accountToken,
        payload.additionalPayload.destinationToken
      )

      await this.paymentsService
        .handleWithdraw(payload.userId, {
          amountBalance: payload.amountBalance,
          amountUsd: payload.amountUsd,
          currency: 'USD',
          provider: MASSPAY_PROVIDER,
          externalId,
          withdrawalId: payload.withdrawalId,
          context: {
            ...payload.additionalPayload.attributes,
            masspayMethod: payload.additionalPayload.method,
            additionalText: `Instant transfer may take time to be processed by \
            your receiving bank. Expect funds to appear within 60 minutes.`,
          },
        })
        .catch((err) => {
          this.logger.warn(
            `Failed to handleWithdraw %d %o %o`,
            payload.userId,
            {
              attributes: payload.additionalPayload.attributes,
              method: payload.additionalPayload.method,
              amountBalance: payload.amountBalance,
            },
            err
          )
        })

      // TODO: check others providers for this too
      // balanceAmoutFiat is decremented inside userSvc.charge()
      // this.paymentHandlingService
      //   .registerCashoutFiat(userId, amountUsd, isUsingFiat)
      //   .catch(warn)
    } catch (err: any) {
      this.logger.warn(`Failed to send payment err=%o`, err)
      if (this._isMasspayNetworkError(err)) {
        this.userService.addLog({
          userId: payload.userId,
          category: 'cashier',
          message: 'Masspay network issue, not refunding automatically',
        })
        return
      }

      throw new ServiceUnavailableException('cannot_send_payout')
    }
  }

  async withdrawRefund(payload: WithdrawJobPayload): Promise<void> {
    await this.userService.credit(
      payload.userId,
      payload.amountBalance,
      {
        category: 'cashier',
        message: 'Masspay cashout failed',
      },
      payload.currency
    )

    await this.userService.update(payload.userId, {
      isFiat: true,
      balanceAmoutFiat: { increment: payload.amountBalance },
    })
  }

  private _isMasspayNetworkError(err: any) {
    // masspay-sdk returns ApiError when response exists
    // we assume there is network error when fetch throwed or failed
    // to return any response, we also check BadRequestException
    // cause it is what assert returns when status !== success
    return !(err instanceof ApiError || err instanceof BadRequestException)
  }

  private _prepareUserPayload(
    userId: number,
    data: UpdateMasspayUserDto | CreateMasspayUserDto
  ) {
    return {
      internal_user_id: this._userKey(userId),
      address1: data.address1,
      address2: data.address2,
      city: data.city,
      country: data.country,
      email: data.email,
      language: data.language,
      state_province: data.stateProvince,
      postal_code: data.postalCode,
      date_of_birth: data.dateOfBirth,
      middle_name: data.middleName,
      business_name: data.businessName,
      first_name: data.firstName,
      mobile_number: data.mobileNumber,
      last_name: data.lastName,
    }
  }

  private _publicUser(user: StoredUser) {
    return {
      address1: user.address1,
      address2: user.address2,
      city: user.city,
      status: user.status,
      country: user.country,
      email: user.email,
      firstName: user.first_name,
      middleName: user.middle_name,
      lastName: user.last_name,
      bussinessName: user.business_name,
      dateOfBirth: user.date_of_birth,
      postalCode: user.postal_code,
      mobileNumber: user.mobile_number,
    }
  }
}
