import { Module, forwardRef } from '@nestjs/common'
import { MasspayService } from './masspay.service'
import { MasspayController } from './masspay.controller'
import { UserModule } from 'src/modules/user/user.module'
import { PaymentHandlingModule } from '../payment-handling/payment-handling.module'
import { PaymentsModule } from '../payments.module'
import { RestrictionsModule } from 'src/modules/user/restrictions/restrictions.module'
import { KycModule } from '../antifraud/kyc/kyc.module'
import { PaymentBlocksModule } from '../payment-blocks/payment-blocks.module'
import { IpLockModule } from 'src/modules/ip-lock/ip-lock.module'

@Module({
  imports: [
    forwardRef(() => PaymentsModule),
    UserModule,
    forwardRef(() => PaymentHandlingModule),
    RestrictionsModule,
    KycModule,
    PaymentBlocksModule,
    IpLockModule,
  ],
  providers: [MasspayService],
  controllers: [MasspayController],
})
export class MasspayModule {}
