import {
  Min,
  Max,
  IsInt,
  IsIn,
  IsObject,
  ValidateNested,
} from 'class-validator'
import { DESTINATIONS } from '../masspay.constants'
import { BankDto } from './bank.dto'
import { Type, TypeHelpOptions } from 'class-transformer'
import { <PERSON>elleDto } from './zelle.dto'
import { MasspayMethod } from '../masspay.types'

export class CashoutDto {
  @IsInt()
  @Min(25_00)
  @Max(50_000_00)
  amount: Int

  @IsIn(Object.keys(DESTINATIONS))
  method: MasspayMethod

  @IsObject()
  @ValidateNested()
  @Type((type: TypeHelpOptions | undefined) => {
    if (type?.object) {
      const cashoutDto = type.object as CashoutDto
      switch (cashoutDto.method) {
        case 'ZELLE':
          return ZelleDto
        default:
          return BankDto
      }
    }
    return BankDto
  })
  attributes: BankDto | ZelleDto
}
