import {
  IsIn,
  IsNumberString,
  IsString,
  Length,
  Matches,
} from 'class-validator'

export class BankDto {
  @IsNumberString()
  @Length(9)
  ssn: string

  @IsString()
  @IsIn(['Checking', 'Savings'])
  bankAccountType: 'Checking' | 'Savings'

  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'dateOfBirth must be in the format yyyy-mm-dd',
  })
  dateOfBirth: string

  @IsNumberString()
  @Length(9)
  bankRoutingNumber: string

  @IsString()
  bankAccountNumber: string
}
