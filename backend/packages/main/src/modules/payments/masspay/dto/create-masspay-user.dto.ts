import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>eng<PERSON>,
  IsMobilePhone,
  IsISO31661Alpha3,
  Matches,
  IsEmail,
} from 'class-validator'

export class CreateMasspayUserDto {
  @IsString()
  address1: string

  @IsOptional()
  @IsString()
  address2?: string

  @IsString()
  city: string

  @IsOptional()
  @IsString()
  stateProvince: string

  @IsString()
  postalCode: string

  @IsString()
  @IsISO31661Alpha3()
  country: string

  @IsString()
  firstName: string

  @IsOptional()
  @IsString()
  middleName?: string

  @IsString()
  lastName: string

  @IsEmail()
  email: string

  @IsOptional()
  @IsString()
  @MaxLength(2)
  language?: string

  @IsOptional()
  @IsMobilePhone()
  mobileNumber?: string

  @IsOptional()
  @IsString()
  businessName?: string

  @IsString()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, {
    message: 'dateOfBirth must be in the format yyyy-mm-dd',
  })
  dateOfBirth: string
}
