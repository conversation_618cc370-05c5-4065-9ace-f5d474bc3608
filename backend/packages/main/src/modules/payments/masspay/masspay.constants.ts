export const MASSPAY_PROVIDER = 'masspay' as const

export const DESTINATIONS = {
  NORMAL: {
    token: 'dest_974927b8-c1a0-11ec-aaca-06c17397573d',
    // token: 'dest_656b116a-83f6-11ef-b954-0235c9d109d3', // staging token
    fixedFee: 100,
    precentageFee: 0.0,
    attributeTypes: {
      note: 'BillReferenceNumber',
      ssn: 'SocialSecurity',
      bankAccountType: 'BankAccountType',
      bankRoutingNumber: 'BankRoutingNumber',
      bankAccountNumber: 'BankAccountNumber',
    },
  },
  EXPRESS: {
    token: 'dest_973caa4b-c1a0-11ec-aaca-06c17397573d',
    fixedFee: 200,
    precentageFee: 0.0,
    attributeTypes: {
      note: 'BillReferenceNumber',
      ssn: 'SocialSecurity',
      bankAccountType: 'BankAccountType',
      bankRoutingNumber: 'BankRoutingNumber',
      bankAccountNumber: 'BankAccountNumber',
    },
  },
  RTP: {
    token: 'dest_a79394b3-4230-11ee-9bee-06cff3435679',
    fixedFee: 200,
    precentageFee: 0.01,
    attributeTypes: {
      note: 'BillReferenceNumber',
      ssn: 'SocialSecurity',
      bankAccountType: 'BankAccountType',
      bankRoutingNumber: 'BankRoutingNumber',
      bankAccountNumber: 'BankAccountNumber',
    },
  },
  ZELLE: {
    token: 'dest_b5d2fdc9-40fc-11ee-9bee-06cff3435679',
    fixedFee: 200,
    precentageFee: 0.01,
    attributeTypes: {
      note: 'BillReferenceNumber',
      ssn: 'SocialSecurity',
      zelleAccount: 'BankAccountNumber',
    },
  },
}
