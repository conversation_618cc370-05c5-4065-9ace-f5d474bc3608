export const getFullName = (names) => {
  return names.map((name) => name.trim().toUpperCase()).join(' ')
}

export const fuzzyMatchNames = (str1, str2) => {
  const diff = Math.abs(str1.length - str2.length)
  const maxLen = Math.max(str1.length, str2.length)

  // if lengths differ more than 20%
  if (diff / maxLen > 0.2) return false

  const matches = str1.split('').filter((char) => str2.includes(char)).length
  return matches / maxLen >= 0.8
}
