import {
  BadRequestException,
  Injectable,
  Logger,
  PreconditionFailedException,
} from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { CreateDepositDto } from './dto/create-deposit.dto'
import axios, { AxiosInstance } from 'axios'
import { PaymentHandlingService } from '../payment-handling/payment-handling.service'
import { WebhookBody } from './paycom.types'
import { SUCCESS } from 'src/common/constants'
import { PaymentsService } from '../payments.service'
import { Currency } from '@crashgg/types/dist'
import { PAYCOM_PROVIDER } from './paycom.constants'
import { PartialUpdateSessionDto } from './dto/update-session.dto'
import { ProcessApplePayDto } from './dto/process-appple-pay.dto'

@Injectable()
export class PaycomService {
  private logger = new Logger(PaycomService.name)
  private axios: AxiosInstance

  constructor(
    private config: ConfigService,
    private paymentHandlingService: PaymentHandlingService,
    private paymentsService: PaymentsService
  ) {
    const baseURL = config.get('paycom.isSandbox')
      ? 'https://api.staging.pay.com'
      : 'https://api.pay.com'

    this.axios = axios.create({
      baseURL,
      headers: {
        'x-paycom-api-key': config.get('paycom.apiKey'),
      },
    })
  }

  private async canDeposit(userId: Int, amount: Int) {
    return userId && amount

    // const restrictions =
    //   await this.paymentHandlingService.getPaymentRestrictions(userId)

    // return amount <= restrictions.paycom.maxDepositAmount
  }

  async createDeposit(userId: Int, createDepositDto: CreateDepositDto) {
    // const canDeposit = await this.canDeposit(userId, createDepositDto.amount)
    // assert(canDeposit, 'deposit_limit_reached')

    const depositId = this.paymentHandlingService.generatePaymentId()

    const externalUserId = `${this.config.get('siteTag')}:${userId}`
    const { chargeAmount } = this.calculateCharge(createDepositDto.amount)

    const res = await this.axios
      .post(`/v1/sessions/payment`, {
        customer_reference_id: externalUserId,
        reference: depositId,
        amount: chargeAmount,
        currency: 'usd',
        description: 'Balance deposit',
        billing_details: this.parseBillingDetails(createDepositDto),
        ...(createDepositDto.firstName && {
          payment_method_options: {
            card: { enforce_customer_name_as_cardholder_name: true },
          },
        }),
      })
      .catch((err) => {
        if (
          err.response?.data?.error.message ===
          'Customer name should be provided'
        ) {
          throw new PreconditionFailedException('name_required')
        }

        this.logger.warn(`Failed to create deposit %o`, err.response?.data)
        throw err
      })

    return {
      sessionId: res.data.id,
      clientSecret: res.data.client_secret,
      billingDetails: res.data.billing_details,
    }
  }

  async processApplePay(userId: Int, processApplePayDto: ProcessApplePayDto) {
    // const canDeposit = await this.canDeposit(userId, processApplePayDto.amount)
    // assert(canDeposit, 'deposit_limit_reached')

    const depositId = this.paymentHandlingService.generatePaymentId()

    const externalUserId = `${this.config.get('siteTag')}:${userId}`
    const { chargeAmount } = this.calculateCharge(processApplePayDto.amount)

    this.logger.log('Processing apple pay req=%o', processApplePayDto)

    try {
      const res = await this.axios.post(`/v1/charges`, {
        amount: chargeAmount,
        currency: 'usd',
        customer_reference_id: externalUserId,
        reference: depositId,
        description: 'Balance deposit',
        source_data: {
          type: 'applepay',
          billing_details: this.parseBillingDetails(processApplePayDto),
          ...processApplePayDto.paymentData,
        },
      })

      return { status: res.data.status }
    } catch (err) {
      this.logger.warn(
        `Failed to create applepay deposit %o`,
        err.response?.data
      )
      return { status: 'failure' }
    }
  }

  async updateSession(
    userId: Int,
    paymentId: string,
    updateSessionDto: PartialUpdateSessionDto
  ) {
    // if (updateSessionDto.amount) {
    //   const canDeposit = await this.canDeposit(userId, updateSessionDto.amount)
    //   assert(canDeposit, 'deposit_limit_reached')
    // }

    const payload: Record<string, any> = {
      billing_details: this.parseBillingDetails(updateSessionDto as any),
    }

    if (updateSessionDto.firstName) {
      payload.payment_method_options = {
        card: { enforce_customer_name_as_cardholder_name: true },
      }
    }

    if (updateSessionDto.amount) {
      const { chargeAmount } = this.calculateCharge(updateSessionDto.amount)

      payload.amount = chargeAmount
      payload.currency = 'USD'
    }

    const res = await this.axios
      .patch(`/v1/sessions/payment/${paymentId}`, payload)
      .catch((err) => {
        this.logger.warn(
          `Failed to update payment %o %o`,
          payload,
          err.response?.data
        )

        if (Math.floor(err.response?.status / 100) === 4) {
          throw new BadRequestException()
        }

        throw err
      })

    return {
      sessionId: res.data.id,
      clientSecret: res.data.client_secret,
      billingDetails: res.data.billing_details,
    }
  }

  private calculateCharge(amount: Int) {
    const { processingFee } = this.config.get('paycom')
    const chargeAmount = Math.round(amount * (1 + processingFee))

    return { chargeAmount }
  }

  private parseBillingDetails(createDepositDto: CreateDepositDto) {
    const { email, country, firstName, lastName, zipCode } = createDepositDto

    if (!email) {
      return {}
    }

    const details: Record<string, any> = {
      email,
    }

    if (firstName && lastName) {
      details.name = `${firstName} ${lastName}`
    }

    if (country && zipCode) {
      details.address = {
        country,
        postal_code: zipCode,
      }
    }

    return details
  }

  async handleWebhook(body: WebhookBody) {
    if (body.type === 'charge.succeeded') {
      await this.handleChargeSucceeded(body)
    }

    return SUCCESS
  }

  private async handleChargeSucceeded(body: WebhookBody) {
    const [siteTag, userIdStr] = body.data.customer_reference_id.split(':')
    if (siteTag !== this.config.get('siteTag')) {
      return
    }

    const { multiplier, processingFee } = this.config.get('paycom')
    const amountCharged = body.data.amount
    const preFeeAmount = Math.round(amountCharged / (1 + processingFee))
    const balanceChange = Math.round(preFeeAmount * multiplier)

    await this.paymentsService.handleDeposit({
      userId: parseInt(userIdStr),
      amountUsd: amountCharged,
      balanceChange,
      balanceCurrency: Currency.REAL,
      isFiat: true,
      externalId: body.id,
      provider: PAYCOM_PROVIDER,
    })
  }
}
