export interface WebhookBody {
  id: string
  resource: string
  type: string
  created: string
  data: Data
}

interface Data {
  id: string
  resource: string
  amount: number
  amount_human_readable: string
  amount_refunded: number
  billing_details: Billing_details
  calculated_statement_descriptor: string
  cashflow_transaction: null
  created: string
  currency: string
  customer: string
  customer_reference_id: string
  description: string
  disputed: boolean
  failure_code: null
  failure_message: null
  fraud: boolean
  hold: null
  metadata: Metadata
  paid: boolean
  payment_method: string
  payment_method_details: Payment_method_details
  payment_method_options: null
  payment_session: string
  receipt: null
  reference: string
  refunded: boolean
  refunds: Refunds
  shipping: null
  statement_descriptor_suffix: null
  status: string
  test_mode: boolean
  underlying_network_details: Underlying_network_details
  underlying_network_id: string
}

interface Billing_details {
  address: null
  email: string
  name: string
  phone: null
}

interface Metadata {
  balanceChange: Int
}

interface Payment_method_details {
  card: Card
  reference: null
  type: string
}

interface Card {
  bin: string
  brand: string
  cardholder_currency: string
  country: string
  exp_month: string
  exp_year: string
  fingerprint: string
  funding: string
  last4: string
  network: string
  security_checks: Security_checks
  three_d_secure: Three_d_secure
  token: string
  wallet: null
}

interface Security_checks {
  address_line1_check: string
  address_postal_code_check: string
  cvc_check: string
}

interface Three_d_secure {
  authentication_flow: null
  cardholder_info: null
  eci: string
  liability_shift: boolean
  result: string
  result_reason: string
  version: null
}

interface Refunds {
  data: any[]
  url: string
}

interface Underlying_network_details {
  network: string
  visa: Visa
}

interface Visa {
  arn: string
  auth_code: string
  raw_response_code: string
}
