import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
} from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { createHmac } from 'crypto'

@Injectable()
export class PaycomWebhookGuard implements CanActivate {
  private readonly logger = new Logger(PaycomWebhookGuard.name)

  constructor(private config: ConfigService) {}

  canActivate(context: ExecutionContext): boolean | Promise<boolean> {
    const req = context.switchToHttp().getRequest()

    const webhookSecret = this.config.get('paycom.webhookSecret')
    const paySignatureHeader = req.headers['pay-signature']

    const headerElements = paySignatureHeader
      .split(',')
      .reduce((acc, kvPair) => {
        const [k, v] = kvPair.split('=')
        return {
          ...acc,
          [k]: v,
        }
      }, {})

    const { t: timestamp, v1: signatureToCompare } = headerElements

    const bodyJson = JSON.stringify(req.body)
    const signedPayload = [timestamp, bodyJson].join('.')

    const computedSignature = createHmac('sha256', webhookSecret)
      .update(signedPayload, 'utf8')
      .digest('hex')

    const isWebhookValid = computedSignature === signatureToCompare

    return isWebhookValid
  }
}
