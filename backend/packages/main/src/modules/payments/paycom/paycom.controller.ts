import { Body, Controller, Param, Patch, Post, UseGuards } from '@nestjs/common'
import { PaycomService } from './paycom.service'
import { CreateDepositDto } from './dto/create-deposit.dto'
import { AccessGuard } from 'src/modules/auth/guards/access.guard'
import { BanGuard } from 'src/modules/auth/guards/ban.guard'
import { UserId } from 'src/common/user-id.decorator'
import { WebhookBody } from './paycom.types'
import { PaycomWebhookGuard } from './paycom-webhook.guard'
import { PartialUpdateSessionDto } from './dto/update-session.dto'
import { FiatDepositGuard } from '../fiat-deposit.guard'
import { ProcessApplePayDto } from './dto/process-appple-pay.dto'
import { PaymentGuard, PaymentProvider } from '../payment.guard'
import { PAYCOM_PROVIDER } from './paycom.constants'

@Controller('payments/paycom')
export class PaycomController {
  constructor(private paycomService: PaycomService) {}

  @Post()
  @PaymentProvider({
    provider: PAYCOM_PROVIDER,
    type: 'deposit',
    isCrypto: false,
  })
  @UseGuards(AccessGuard, BanGuard, FiatDepositGuard, PaymentGuard)
  async createDeposit(
    @UserId() userId: Int,
    @Body() createDepositDto: CreateDepositDto
  ) {
    return this.paycomService.createDeposit(userId, createDepositDto)
  }

  @Post('process-apple-pay')
  @PaymentProvider({
    provider: PAYCOM_PROVIDER,
    type: 'deposit',
    isCrypto: false,
  })
  @UseGuards(AccessGuard, BanGuard, FiatDepositGuard, PaymentGuard)
  async processApplePay(
    @UserId() userId: Int,
    @Body() processApplePayDto: ProcessApplePayDto
  ) {
    return this.paycomService.processApplePay(userId, processApplePayDto)
  }

  @Patch(':id')
  @UseGuards(AccessGuard, BanGuard, FiatDepositGuard)
  async updateSession(
    @UserId() userId: Int,
    @Param('id') paymentId: string,
    @Body() updateSessionDto: PartialUpdateSessionDto
  ) {
    return this.paycomService.updateSession(userId, paymentId, updateSessionDto)
  }

  @Post('webhook')
  @UseGuards(PaycomWebhookGuard)
  async handleWebhook(@Body() body: WebhookBody) {
    return this.paycomService.handleWebhook(body)
  }
}
