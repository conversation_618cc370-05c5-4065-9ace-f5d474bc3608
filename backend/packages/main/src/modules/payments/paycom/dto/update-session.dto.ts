import { PartialType } from '@nestjs/mapped-types'
import { IsEmail, IsInt, IsString, Length, Max, Min } from 'class-validator'

export class UpdateSessionDto {
  @IsInt()
  @Min(1_00)
  @Max(500_00)
  amount: Int

  @IsEmail()
  email: string

  @IsString()
  firstName: string

  @IsString()
  lastName: string

  @IsString()
  @Length(5, 6)
  zipCode: string

  @IsString()
  @Length(2, 3)
  country: string
}

export class PartialUpdateSessionDto extends PartialType(UpdateSessionDto) {}
