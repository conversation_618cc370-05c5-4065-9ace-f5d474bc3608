import { Injectable } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import { DEPOSIT_EVENT, DepositEvent } from '../events/deposit.event'
import {
  WITHDRAWAL_EVENT,
  WithdrawalEvent,
  WithdrawalStatus,
} from '../events/withdrawal.event'
import { PaymentHandlingService } from './payment-handling.service'

@Injectable()
export class PaymentHandlingListener {
  constructor(private paymentHandlingService: PaymentHandlingService) {}

  @OnEvent(DEPOSIT_EVENT, { async: true })
  async onDeposit(depositEvent: DepositEvent) {
    const { userId, provider, amountUsd, playAmount, context } = depositEvent
    await this.paymentHandlingService.sendNotificationEmail(userId, 'deposit', {
      provider,
      amountUsd,
      playAmount,
      ...context,
    })
  }

  @OnEvent(WITHDRAWAL_EVENT)
  async onWithdrawal(withdrawalEvent: WithdrawalEvent) {
    const {
      userId,
      provider,
      status,
      amountUsd,
      skipEmailNotification,
      context,
    } = withdrawalEvent
    if (skipEmailNotification) return
    if (status !== WithdrawalStatus.SUCCESS) return

    await this.paymentHandlingService.sendNotificationEmail(
      userId,
      'withdrawal',
      {
        provider,
        amountUsd,
        ...context,
      }
    )
  }
}
