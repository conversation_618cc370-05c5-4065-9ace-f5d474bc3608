import {
  WITHDRAW_CHECK_JOB,
  WITHDRAW_PAYOUT_JOB,
  WITHDRAW_QUEUE,
  WITHDRAW_REFUND_JOB,
} from './withdraw-handling.constants'
import { Logger } from '@nestjs/common'
import { Job } from 'bull'
import { WithdrawJobPayload } from './withdraw-handling.types'
import { PaymentHandlingService } from './payment-handling.service'
import { RedisService } from 'src/modules/redis/redis.service'
import { ProcessMq, ProcessorMq } from 'src/common/decorators/bullmq.decorator'

@ProcessorMq(WITHDRAW_QUEUE)
export class WithdrawHandlingProcessor {
  private readonly logger = new Logger(WithdrawHandlingProcessor.name)
  constructor(
    private paymentHandlingService: PaymentHandlingService,
    private redis: RedisService
  ) {}

  @ProcessMq(WITHDRAW_PAYOUT_JOB)
  async payout({ data }: Job<WithdrawJobPayload>) {
    const lock = await this.redis.lock([
      `withdraw:process:${data.withdrawalId}`,
    ])
    return await this.paymentHandlingService
      .withdrawPayout(data)
      .finally(lock.release)
  }

  @ProcessMq(WITHDRAW_REFUND_JOB)
  async refund({ data }: Job<WithdrawJobPayload>) {
    const lock = await this.redis.lock([
      `withdraw:process:${data.withdrawalId}`,
    ])
    return await this.paymentHandlingService
      .withdrawRefund(data)
      .finally(lock.release)
  }

  @ProcessMq(WITHDRAW_CHECK_JOB)
  async check({ data }: Job<WithdrawJobPayload>) {
    const lock = await this.redis.lock([
      `withdraw:check:${data.type}:${data.withdrawalId}`,
    ])
    return await this.paymentHandlingService
      .withdrawCheck(data)
      .finally(lock.release)
  }
}
