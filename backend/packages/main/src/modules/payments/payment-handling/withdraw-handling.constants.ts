import { Prisma } from '@prisma/client'
import { PUBLIC_USER_FIELDS } from 'src/common/constants'

export const WITHDRAW_QUEUE = '{withdraw-handling}'
export const WITHDRAW_PAYOUT_JOB = 'payout'
export const WITHDRAW_REFUND_JOB = 'refund'
export const WITHDRAW_CHECK_JOB = 'check'

export const PUBLIC_WITHDRAW_QUEUE: Prisma.WithdrawQueueSelect = {
  id: true,
  withdrawalId: true,
  status: true,
  amount: true,
  currency: true,
  isFinalized: true,
  createdAt: true,
  updatedAt: true,
}

export const INCLUDE_CHECKS_AND_USER: Prisma.WithdrawQueueInclude = {
  withdrawQueueCheck: true,
  user: { select: PUBLIC_USER_FIELDS },
}
