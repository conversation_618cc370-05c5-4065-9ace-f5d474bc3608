import { Injectable, Logger, ServiceUnavailableException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import axios, { AxiosRequestConfig } from 'axios'
import { MESHCONNECT_ENV_URLS } from '../meshconnect.constants'
import { GetLinkToken, GetLinkTokenResponse } from './meshconnect-sdk.interface'

@Injectable()
export class MeshconnectSdkAccess {
  private readonly logger = new Logger(MeshconnectSdkAccess.name)
  private readonly url: string
  private readonly secret: string
  private readonly clientId: string

  constructor(private readonly config: ConfigService) {
    const { clientId, environment, secret } = this.config.get('meshconnect')
    this.clientId = clientId
    this.secret = secret
    if (environment === 'prod') {
      this.url = MESHCONNECT_ENV_URLS.PRODUCTION
    } else {
      this.url = MESHCONNECT_ENV_URLS.SANDBOX
    }
  }

  private async request<T>(
    options: Omit<AxiosRequestConfig, 'url'> & { path: string }
  ): Promise<T | null> {
    const { path, headers, ...requestOptions } = options
    const res = await axios({
      ...requestOptions,
      url: `${this.url}/${path}`,
      headers: {
        ...headers,
        'X-Client-Id': this.clientId,
        'X-Client-Secret': this.secret,
      },
    }).catch((err) => {
      this.logger.warn(
        `Meshconnect request failed err=%o data=%o req=%o`,
        err.message,
        err.response?.data,
        options.data
      )
      throw new ServiceUnavailableException('meshconnect_down')
    })
    this.logger.log('Meshconnect req=%o res=%o', options.data, res.data)
    return res.data
  }

  async getLinkToken(data: GetLinkToken) {
    return await this.request<GetLinkTokenResponse>({
      method: 'POST',
      path: 'linktoken',
      data,
    })
  }
}
