export interface GetLinkToken {
  userId: string
  restrictMultipleAccounts?: boolean
  transferOptions?: TransferOptions
  integrationId?: string
  disableApiKeyGeneration?: boolean
  verifyWalletOptions?: VerifyWalletOptions
  subClientId?: string
}

export interface TransferOptions {
  toAddresses?: ToAddress[]
  amountInFiat?: number
  transactionId?: string
  clientFee?: number
  transferType?: TransferType
  fundingOptions?: FundingOptions
  isInclusiveFeeEnabled?: boolean
  description?: string
  goodsDetails?: GoodsDetail[]
  generatePayLink?: boolean
}

export interface ToAddress {
  networkId: string
  symbol?: string
  address?: string
  addressTag?: string
  amount?: number
  displayAmountInFiat?: number
}

export const TransferType = {
  DEPOSIT: 'deposit',
  PAYMENT: 'payment',
  ONRAMP: 'onramp',
}
export type TransferType = (typeof TransferType)[keyof typeof TransferType]

export interface FundingOptions {
  enabled: boolean
}

export interface GoodsDetail {
  goodsType?: string
  goodsCategory?: string
  referenceGoodsId?: string
  goodsName?: string
  goodsDetail?: string
}

export interface VerifyWalletOptions {
  message?: string
  verificationMethods?: VerificationMethod[]
  addresses?: string[]
  networkId?: string
  networkType?: NetworkType
}

export const VerificationMethod = {
  SIGNED_MESSAGE: 'signedMessage',
}
export type VerificationMethod =
  (typeof VerificationMethod)[keyof typeof VerificationMethod]

export const NetworkType = {
  UNKNOWN: 'unknown',
  EVM: 'evm',
  SOLANA: 'solana',
  BITCOIN: 'bitcoin',
  CARDANO: 'cardano',
  TRON: 'tron',
  AVALANCHEX: 'avalancheX',
  TEZOS: 'tezos',
  DOGECOIN: 'dogecoin',
  RIPPLE: 'ripple',
  STELLAR: 'stellar',
  LITECOIN: 'litecoin',
  SUI: 'sui',
  APTOS: 'aptos',
  TVM: 'tvm',
}
export type NetworkType = (typeof NetworkType)[keyof typeof NetworkType]

export interface GetLinkTokenResponse {
  status: ResponseStatus
  message?: string
  displayMessage?: string
  errorType?: string
  errorData: any
  content?: {
    linkToken?: string
    paymentLink?: string
  }
}

export const ResponseStatus = {
  OK: 'ok',
  SERVER_FAILURE: 'serverFailure',
  PERMISSION_DENIED: 'permissionDenied',
  BAD_REQUEST: 'badRequest',
  NOT_FOUND: 'notFound',
  CONFLICT: 'conflict',
  TOO_MANY_REQUEST: 'tooManyRequest',
  LOCKED: 'locked',
  UNAVAILABLE_FOR_LEGAL_REASONS: 'unavailableForLegalReasons',
}
export type ResponseStatus =
  (typeof ResponseStatus)[keyof typeof ResponseStatus]
