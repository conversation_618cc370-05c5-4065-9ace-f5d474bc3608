import { Prisma } from '@prisma/client'
import { CoinData } from './meshconnect.interafce'

export const MESHCONNECT_PROVIDER = 'meshconnect'
export const MESHCONNECT_ENV_URLS = {
  PRODUCTION: 'https://integration-api.meshconnect.com/api/v1',
  SANDBOX: 'https://sandbox-integration-api.meshconnect.com/api/v1',
}

export const PUBLIC_ACCESS_TOKEN = {
  accessToken: true,
  brokerType: true,
  brokerName: true,
} satisfies Prisma.MeshconnectTokensSelect

export const COIN_DATA_ON_COIN_CODE: Record<string, CoinData> = {
  BTC: { networkId: '03dee5da-7398-428f-9ec2-ab41bcb271da', symbol: 'BTC' },
  LTC: { networkId: '1709a5dc-d114-4683-bf95-5a5abb54df31', symbol: 'LTC' },
  ETH: { networkId: 'e3c7fdd8-b1fc-4e51-85ae-bb276e075611', symbol: 'ETH' },
  DOGE: { networkId: '34b66a94-f9f9-49ef-81e8-6ebd5a866f9d', symbol: 'DOGE' },
  XRP: { networkId: '0ea47ee7-9d36-460e-a2d5-64cfa8a1dddd', symbol: 'XRP' },
  SOL: { networkId: '0291810a-5947-424d-9a59-e88bb33e999d', symbol: 'SOL' },
  USDT_ERC20: {
    networkId: 'e3c7fdd8-b1fc-4e51-85ae-bb276e075611',
    symbol: 'USDT',
  },
  USDC: { networkId: 'e3c7fdd8-b1fc-4e51-85ae-bb276e075611', symbol: 'USDC' },
  LINK: { networkId: 'e3c7fdd8-b1fc-4e51-85ae-bb276e075611', symbol: 'LINK' },
  ADA: { networkId: '9cc3f8db-809a-4d06-a183-34a63a84aca8', symbol: 'ADA' },
  // meshconnect supports BNB only on BSC network
  // BNB: { networkId: '', symbol: 'BNB' },
  BNB_BSC: { networkId: 'ed0ebeec-b166-4c8b-8574-cb078f7af8cf', symbol: 'BNB' },
  TRX: { networkId: 'c5dc5d2e-68c1-4261-9a30-90b598738bf5', symbol: 'TRX' },
  TRX_USDT_S2UZ: {
    networkId: 'c5dc5d2e-68c1-4261-9a30-90b598738bf5',
    symbol: 'USDT',
  },
  TON: { networkId: '3d71a957-740b-4195-a437-389d171f21bb', symbol: 'TON' },
}
