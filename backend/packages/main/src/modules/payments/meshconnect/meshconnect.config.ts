import { registerAs } from '@nestjs/config'

const getSupportedCoins = () => {
  if (process.env.MESHCONNECT_COINS) {
    return process.env.MESHCONNECT_COINS.split(';')
  } else if (process.env.FIREBLOCKS_COINS) {
    return process.env.FIREBLOCKS_COINS.split(';')
  } else {
    return null
  }
}

export default registerAs('meshconnect', () => ({
  environment: process.env.MESHCONNECT_ENV || 'prod',
  clientId: process.env.MESHCONNECT_CLIENT_ID,
  secret: process.env.MESHCONNECT_SECRET,
  supportedCoins: getSupportedCoins() ?? [
    'BTC',
    'LTC',
    'ETH',
    'DOGE',
    'XRP',
    'SOL',
    'USDT_ERC20',
    'USDC',
    'LINK',
    'ADA',
    'BNB',
    'BNB_BSC',
    'TRX',
    'TRX_USDT_S2UZ',
    'TON',
  ],
}))
