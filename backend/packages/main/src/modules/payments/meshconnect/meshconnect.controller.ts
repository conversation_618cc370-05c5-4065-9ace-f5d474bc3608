import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common'
import { MeshconnectService } from './meshconnect.service'
import { UserId } from 'src/common/user-id.decorator'
import { AccessGuard } from 'src/modules/auth/guards/access.guard'
import { BanGuard } from 'src/modules/auth/guards/ban.guard'
import { UpdateAccessTokenDto } from './dto/update-access-token.dto'
import { PaymentGuard, PaymentProvider } from '../payment.guard'
import { MESHCONNECT_PROVIDER } from './meshconnect.constants'
import { SUCCESS } from 'src/common/constants'

@Controller('payments/meshconnect')
export class MeshconnectController {
  constructor(private readonly meshconnectService: MeshconnectService) {}

  @Get('link-token/:coinCode')
  @PaymentProvider({
    provider: MESHCONNECT_PROVIDER,
    type: 'deposit',
    isCrypto: true,
  })
  @UseGuards(AccessGuard, BanGuard, PaymentGuard)
  async getLinkToken(
    @UserId() userId: Int,
    @Param('coinCode') coinCode: string
  ) {
    return this.meshconnectService.getLinkToken(userId, coinCode)
  }

  @Post('access-tokens')
  @UseGuards(AccessGuard, BanGuard)
  async updateAccessToken(
    @UserId() userId: Int,
    @Body() updateAccessTokensDto: UpdateAccessTokenDto
  ) {
    await this.meshconnectService.updateAccessToken(
      userId,
      updateAccessTokensDto
    )
    return SUCCESS
  }

  @Get('access-tokens')
  @UseGuards(AccessGuard, BanGuard)
  async getUserAccessTokens(@UserId() userId: Int) {
    return this.meshconnectService.getUserAccessTokens(userId)
  }
}
