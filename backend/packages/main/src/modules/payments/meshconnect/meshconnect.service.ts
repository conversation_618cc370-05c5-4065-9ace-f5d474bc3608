import { Injectable } from '@nestjs/common'
import { assert } from 'src/utils/assert'
import { FireblocksService } from '../fireblocks/fireblocks.service'
import { MeshconnectSdkAccess } from './meshconnect-sdk/meshconnect-sdk.access'
import { ConfigService } from '@nestjs/config'
import {
  ResponseStatus,
  TransferType,
} from './meshconnect-sdk/meshconnect-sdk.interface'
import { COIN_DATA_ON_COIN_CODE } from './meshconnect.constants'
import { MeshconnectAccess } from './meshconnect.access'
import { UpdateAccessTokenDto } from './dto/update-access-token.dto'

@Injectable()
export class MeshconnectService {
  constructor(
    private readonly meshconnectSdk: MeshconnectSdkAccess,
    private readonly meshconnectAccess: MeshconnectAccess,
    private readonly fireblocksService: FireblocksService,
    private readonly config: ConfigService
  ) {}

  async getLinkToken(userId: Int, coinCode: string) {
    const coinData = COIN_DATA_ON_COIN_CODE[coinCode]
    assert(coinData, `unsupported_coin_code`)

    const wallet = await this.fireblocksService.getDepositAddress(
      userId,
      coinCode
    )
    const [address, addressTag] = wallet.address.split(';')
    const siteCode = this.config.get('siteCode')

    const linkToken = await this.meshconnectSdk.getLinkToken({
      userId: `${siteCode}-${userId}`,
      transferOptions: {
        transferType: TransferType.DEPOSIT,
        toAddresses: [
          {
            networkId: coinData.networkId,
            address,
            addressTag,
            symbol: coinData.symbol,
          },
        ],
      },
    })
    assert(linkToken.status === ResponseStatus.OK, 'failed_to_get_token')

    return linkToken.content
  }

  async getUserAccessTokens(userId: Int) {
    const tokens = await this.meshconnectAccess.getAccessTokens(userId)

    return {
      meshAccessTokens: tokens,
    }
  }

  async updateAccessToken(userId: Int, data: UpdateAccessTokenDto) {
    return await this.meshconnectAccess.updateAccessToken(userId, data)
  }
}
