import { Module } from '@nestjs/common'
import { FireblocksModule } from '../fireblocks/fireblocks.module'
import { MeshconnectController } from './meshconnect.controller'
import { MeshconnectService } from './meshconnect.service'
import { MeshconnectSdkAccess } from './meshconnect-sdk/meshconnect-sdk.access'
import { MeshconnectAccess } from './meshconnect.access'
import { PaymentBlocksModule } from '../payment-blocks/payment-blocks.module'

@Module({
  imports: [FireblocksModule, PaymentBlocksModule],
  providers: [MeshconnectService, MeshconnectSdkAccess, MeshconnectAccess],
  exports: [],
  controllers: [MeshconnectController],
})
export class MeshconnectModule {}
