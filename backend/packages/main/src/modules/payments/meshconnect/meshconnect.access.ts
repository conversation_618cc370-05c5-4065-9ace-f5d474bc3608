import { Injectable } from '@nestjs/common'
import { Prisma } from '@prisma/client'
import { PrismaService } from 'src/modules/prisma/prisma.service'
import { PUBLIC_ACCESS_TOKEN } from './meshconnect.constants'
import { MeshAccessTokens } from './meshconnect.interafce'
import { decrypt, encrypt } from '@crashgg/common/dist'

@Injectable()
export class MeshconnectAccess {
  constructor(private readonly prisma: PrismaService) {}

  async getAccessTokens(userId: Int): Promise<MeshAccessTokens[]> {
    const tokens = await this.prisma.meshconnectTokens.findMany({
      where: { userId },
      select: PUBLIC_ACCESS_TOKEN,
    })

    return tokens.map((token) => ({
      ...token,
      accessToken: decrypt(token.accessToken),
    }))
  }

  async updateAccessToken(
    userId: Int,
    data: Omit<Prisma.MeshconnectTokensCreateInput, 'user'>
  ) {
    data.accessToken = encrypt(data.accessToken)

    return await this.prisma.meshconnectTokens.upsert({
      where: { userId_brokerType: { userId, brokerType: data.brokerType } },
      create: { ...data, user: { connect: { id: userId } } },
      update: data,
    })
  }
}
