import { Currency, Int } from '@crashgg/types/dist'
import { Injectable, Logger, ServiceUnavailableException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import payoutsSdk from '@paypal/payouts-sdk'
import { UserService } from 'src/modules/user/user.service'
import { USD } from 'src/utils/conversion'
import { PaymentsService } from '../payments.service'
import { CashoutDto, CashoutType } from './dto/cashout.dto'

@Injectable()
export class PaypalService {
  private paypal: payoutsSdk.core.PayPalHttpClient
  private logger = new Logger(PaypalService.name)

  constructor(
    private config: ConfigService,
    private paymentsService: PaymentsService,
    private userService: UserService
  ) {
    if (config.get('paypal.isDisabled')) return

    const { clientId, clientSecret, isSandbox } = config.get('paypal')

    const Env = isSandbox
      ? payoutsSdk.core.SandboxEnvironment
      : payoutsSdk.core.LiveEnvironment
    const env = new Env(clientId, clientSecret)

    this.paypal = new payoutsSdk.core.PayPalHttpClient(env)
  }

  async cashout(userId: Int, data: CashoutDto, currency: Currency) {
    const cashoutRate = this.config.get('paypal.cashoutRate')
    const amountUsdUntaxed = ~~(cashoutRate * data.amountBalance)
    const amountUsd =
      amountUsdUntaxed - Math.min(~~(amountUsdUntaxed * 0.02), 500)

    const receiverId =
      data.type === CashoutType.PAYPAL ? data.email : data.phoneNumber

    await this.paymentsService.addRecipient(
      userId,
      'paypal',
      receiverId.replaceAll(' ', '')
    )

    await this.userService.charge({
      userId,
      amount: data.amountBalance,
      transaction: {
        category: 'cashier',
        message: `Paypal cashout attempt ${receiverId}`,
      },
      currency,
    })

    try {
      const externalId = await this.sendTransaction(
        amountUsd,
        data.type,
        receiverId
      )

      await this.paymentsService.handleWithdraw(userId, {
        amountBalance: data.amountBalance,
        amountUsd,
        currency: 'USD',
        provider: 'paypal',
        externalId: `user=${receiverId} tx=${externalId}`,
        withdrawalId: `user=${receiverId} tx=${externalId}`,
        notifText: ` to ${receiverId}`,
        context: {
          paypalReceiver: receiverId,
        },
      })

      return { success: true }
    } catch (err) {
      this.logger.error({ err, data }, `Failed to send payment`)
      await this.userService.credit(
        userId,
        data.amountBalance,
        {
          category: 'cashier',
          message: 'Paypal cashout failed',
        },
        currency
      )

      throw new ServiceUnavailableException('cannot_send_payment')
    }
  }

  async sendTransaction(amountUsd: Int, type: CashoutType, receiverId: string) {
    const id = Date.now()

    const receiver =
      type === CashoutType.PAYPAL
        ? { receiver: receiverId, recipient_type: 'EMAIL' as const }
        : {
            receiver: receiverId,
            recipient_wallet: 'VENMO' as const,
            recipient_type: 'PHONE' as const,
          }

    const request = new payoutsSdk.payouts.PayoutsPostRequest()
    request.requestBody({
      sender_batch_header: {
        email_message: "Here's your cashout from RustClash!",
        sender_batch_id: `batch:${id}`,
        email_subject: 'Cashout from RustClash',
        note: 'Cashout from RustClash',
      },
      items: [
        {
          amount: {
            currency: 'USD',
            value: USD.toString(amountUsd),
          },
          ...receiver,
        },
      ],
    })

    const response = await this.paypal.execute(request)

    return response.result.batch_header.payout_batch_id
  }
}
