import { forwardRef, Module } from '@nestjs/common'
import { PaypalService } from './paypal.service'
import { PaypalController } from './paypal.controller'
import { PaymentsModule } from '../payments.module'
import { UserModule } from 'src/modules/user/user.module'
import { KycModule } from '../antifraud/kyc/kyc.module'
import { PaymentBlocksModule } from '../payment-blocks/payment-blocks.module'

@Module({
  imports: [
    forwardRef(() => PaymentsModule),
    UserModule,
    KycModule,
    PaymentBlocksModule,
  ],
  providers: [PaypalService],
  controllers: [PaypalController],
})
export class PaypalModule {}
