import { Currency, Int } from '@crashgg/types/dist'
import { Body, Controller, Post, UseGuards } from '@nestjs/common'
import { CurrencyUsed } from 'src/common/currency.decorator'
import { UserId } from 'src/common/user-id.decorator'
import { AccessGuard } from 'src/modules/auth/guards/access.guard'
import { BanGuard } from 'src/modules/auth/guards/ban.guard'
import { RedisService } from 'src/modules/redis/redis.service'
import { WithdrawLockGuard } from 'src/modules/user/withdraw-lock.guard'
import { assert } from 'src/utils/assert'
import { PaymentGuard, PaymentProvider } from '../payment.guard'
import { CashoutDto, CashoutType } from './dto/cashout.dto'
import { PaypalService } from './paypal.service'

@Controller('payments/paypal')
export class PaypalController {
  constructor(
    private paypalService: PaypalService,
    private redis: RedisService
  ) {}

  @Post('cashout')
  @PaymentProvider({
    provider: 'paypal',
    type: 'withdraw',
    isCrypto: false,
  })
  @UseGuards(AccessGuard, BanGuard, PaymentGuard, WithdrawLockGuard())
  async cashout(
    @UserId() userId: Int,
    @Body() body: CashoutDto,
    @CurrencyUsed([Currency.REAL]) currency: Currency
  ) {
    const lock = await this.redis.lock([`charge:${userId}`])

    const paypalOk = body.type === CashoutType.PAYPAL && body.email
    const venmoOk = body.type === CashoutType.VENMO && body.phoneNumber
    assert(paypalOk || venmoOk, 'invalid_data')

    return this.paypalService
      .cashout(userId, body, currency)
      .finally(lock.release)
  }
}
