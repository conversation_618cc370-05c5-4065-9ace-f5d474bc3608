import {
  CanActivate,
  ExecutionContext,
  Injectable,
  Logger,
} from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { createHmac } from 'crypto'

@Injectable()
export class WebhookGuard implements CanActivate {
  logger = new Logger('NMIGuard')

  constructor(private config: ConfigService) {}

  canActivate(context: ExecutionContext): boolean | Promise<boolean> {
    const req = context.switchToHttp().getRequest()

    const signatureHeader = req.get('webhook-signature')
    const nonce = signatureHeader.split(',s=')[0].replace('t=', '')
    const signature = signatureHeader.split(',s=')[1]

    const sigKey = this.config.get('nmi.signingKey')

    const webhookBody = (req.body as Buffer).toString()
    const validSignature = createHmac('sha256', sigKey)
      .update(`${nonce}.${webhookBody}`)
      .digest('base64')

    req.body = JSON.parse(webhookBody)

    return signature === validSignature
  }
}
