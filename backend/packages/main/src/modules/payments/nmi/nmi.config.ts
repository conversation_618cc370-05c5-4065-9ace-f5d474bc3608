import { registerAs } from '@nestjs/config'
import { bool } from 'src/modules/config/configuration'

export default registerAs('nmi', () => ({
  isEnabled: bool(process.env.NMI_ENABLED),
  paymentUrl:
    process.env.NMI_PAYMENT_URL ||
    'https://secure.networkmerchants.com/api/transact.php',
  tokenizationKey: process.env.NMI_TOKENIZATION_KEY,
  securityKey: process.env.NMI_SECURITY_KEY,
  signingKey: process.env.NMI_SIGNING_KEY,
  multiplier: process.env.NMI_MULTIPLIER || 1.4,
}))
