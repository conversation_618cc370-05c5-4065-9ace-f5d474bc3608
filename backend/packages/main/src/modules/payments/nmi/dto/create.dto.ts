import { Int } from '@crashgg/types/dist'
import {
  IsCreditCard,
  IsEmail,
  IsInt,
  IsMobilePhone,
  IsOptional,
  IsPostalCode,
  IsString,
  Length,
  Max,
  Min,
} from 'class-validator'
import { CaptchaDto } from 'src/modules/captcha/captcha.dto'

export class CreateDto extends CaptchaDto {
  @IsInt()
  @Min(1_00)
  @Max(500_30)
  amountCents: Int

  @IsCreditCard()
  ccnumber: string

  @IsString()
  @Length(4)
  ccexp: string

  @IsString()
  @Length(3, 4)
  cvv: string

  @IsEmail()
  @Length(1, 128)
  email: string

  @IsString()
  @Length(1, 128)
  firstName: string

  @IsString()
  @Length(1, 128)
  lastName: string

  @IsString()
  @Length(1, 128)
  address: string

  @IsString()
  @Length(1, 128)
  city: string

  @IsString()
  @Length(1, 128)
  state: string

  @IsPostalCode('any')
  @Length(1, 128)
  zip: string

  @IsString()
  @Length(2)
  country: string

  @IsMobilePhone()
  @Length(1, 128)
  phoneNumber: string

  @IsOptional()
  @IsString()
  cavv?: string

  @IsOptional()
  @IsString()
  xid?: string

  @IsOptional()
  @IsString()
  eci?: string

  @IsOptional()
  @IsString()
  cardHolderAuth?: string

  @IsOptional()
  @IsString()
  threeDsVersion?: string

  @IsOptional()
  @IsString()
  directoryServerId?: string

  @IsOptional()
  @IsString()
  locationPass?: string
}
