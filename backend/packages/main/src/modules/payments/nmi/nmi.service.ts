import axios from 'axios'
import { Currency, Int } from '@crashgg/types/dist'
import { Injectable, Logger } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { USD } from 'src/utils/conversion'
import { AntiFraudService } from '../antifraud/antifraud.service'
import { CreateDto } from './dto/create.dto'
import { assert } from 'src/utils/assert'
import { PaymentsService } from '../payments.service'
import { PrismaService } from 'src/modules/prisma/prisma.service'

@Injectable()
export class NmiService {
  private logger = new Logger(NmiService.name)
  private VALID_AVS = ['X', 'Y', 'D', 'M', '2', 'S', 'A', 'B', '3', '7']

  constructor(
    private config: ConfigService,
    private antiFraud: AntiFraudService,
    private payments: PaymentsService,
    private prisma: PrismaService
  ) {}

  getTokenizationKey() {
    return this.config.get('nmi.tokenizationKey')
  }

  async createPayment(userId: Int, create: CreateDto, ip: string, checkFraud) {
    const validateResponse = await this._validateCard(create, userId, ip)
    assert(validateResponse.get('response_code') === '100', 'card_declined')
    assert(
      this.VALID_AVS.includes(validateResponse.get('avsresponse')),
      'avs_failed'
    )

    await this.antiFraud.registerCard(userId, {
      firstName: create.firstName,
      lastName: create.lastName,
      zip: create.zip,
      last4: this._getLast4(validateResponse),
    })

    if (checkFraud) {
      this.logger.log('Checking fraud create=%o', create)
      const [hasPassed] = await this._verifyNoFraud(
        create,
        validateResponse,
        create.amountCents,
        ip
      )

      assert(hasPassed, 'potential_fraud')
    }

    const chargeResponse = await this._charge(create)
    assert(chargeResponse.get('response_code') === '100', 'card_declined')

    const externalId = chargeResponse.get('transactionid')
    const multiplier = this.config.get<number>('nmi.multiplier')

    const user = await this.payments.handleDeposit({
      userId,
      amountUsd: create.amountCents,
      balanceChange: ~~((create.amountCents - 30) * (1 - 0.034) * multiplier),
      balanceCurrency: Currency.REAL,
      isFiat: true,
      externalId,
      provider: 'nmi',
    })

    return { success: true, externalId, newBalance: user.balance }
  }

  async _validateCard(create: CreateDto, userId: Int, ip?: string) {
    return this._post({
      type: 'validate',
      ccnumber: create.ccnumber,
      ccexp: create.ccexp,
      cvv: create.cvv,
      ipaddress: ip,
      merchant_defined_field_1: userId,
      ...this._get3DS(create),
      ...this._getBilling(create),
    })
  }

  async _verifyNoFraud(
    create: CreateDto,
    validateResponse: URLSearchParams,
    amount: Int,
    ip?: string
  ) {
    return this.antiFraud.verifyNoFraud({
      customer: { email: create.email },
      avsResultCode: validateResponse.get('avsresponse'),
      cvvResultCode: validateResponse.get('cvvresponse'),
      customerIP: ip,
      payment: {
        creditCard: { last4: this._getLast4(validateResponse) },
      },
      billTo: {
        firstName: create.firstName,
        lastName: create.lastName,
        address: create.address,
        city: create.city,
        state: create.state,
        zip: create.zip,
        country: create.country,
        phoneNumber: create.phoneNumber,
      },
      amount: (amount / 100).toFixed(2),
    })
  }

  _getLast4(authResponse: URLSearchParams) {
    return authResponse.get('cc_number').slice(-4)
  }

  async _charge(create: CreateDto) {
    return this._post({
      type: 'sale',
      amount: USD.toString(create.amountCents),
      currency: 'USD',
      ccnumber: create.ccnumber,
      ccexp: create.ccexp,
      cvv: create.cvv,
      ...this._get3DS(create),
      ...this._getBilling(create),
    })
  }

  _getBilling(create: CreateDto) {
    return {
      first_name: create.firstName,
      last_name: create.lastName,
      address1: create.address,
      city: create.city,
      state: create.state,
      zip: create.zip,
      country: create.country,
      phone: create.phoneNumber,
      email: create.email,
    }
  }

  _get3DS(create: CreateDto) {
    if (!create.threeDsVersion) return {}

    assert(create.cavv && create.cavv !== 'undefined', 'transaction_declined')

    return {
      cavv: create.cavv,
      ...(create.xid && { xid: create.xid }),
      eci: create.eci,
      cardHolderAuth: create.cardHolderAuth,
      threeDsVersion: create.threeDsVersion,
      directoryServerId: create.directoryServerId,

      cardholder_auth: create.cardHolderAuth,
      three_ds_version: create.threeDsVersion,
      directory_server_id: create.directoryServerId,
    }
  }

  async _post(payload: Record<string, any>) {
    payload.security_key = this.config.get('nmi.securityKey')

    // payload.test_mode = 'enabled'

    const payloadText = Object.entries(payload)
      .map(([k, v]) => `${k}=${v}`)
      .join('&')

    const res = await axios.post(this.config.get('nmi.paymentUrl'), payloadText)
    this.logger.log(`NMI payload=%s response=%s`, payloadText, res.data)

    return new URLSearchParams(res.data)
  }

  async getCardsByLast4(last4: string) {
    return this.prisma.creditCard.findMany({
      where: { last4 },
      include: {
        user: {
          select: { id: true, steamId: true },
        },
      },
    })
  }
}
