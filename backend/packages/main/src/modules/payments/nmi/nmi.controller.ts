import {
  BadRequestException,
  Body,
  Controller,
  Get,
  NotImplementedException,
  Param,
  Post,
  Req,
  ServiceUnavailableException,
  UseGuards,
} from '@nestjs/common'
import { AccessGuard } from 'src/modules/auth/guards/access.guard'
import { BanGuard } from 'src/modules/auth/guards/ban.guard'
import { NotTraderGuard, StaffGuard } from 'src/modules/auth/guards/role.guard'
import { CaptchaService } from 'src/modules/captcha/captcha.service'
import { PrismaService } from 'src/modules/prisma/prisma.service'
import { RedisService } from 'src/modules/redis/redis.service'
import { UserService } from 'src/modules/user/user.service'
import { assert } from 'src/utils/assert'
import { KycService } from '../antifraud/kyc/kyc.service'
import { LocationService } from '../antifraud/location/location.service'
import { CreateDto } from './dto/create.dto'
import { NmiService } from './nmi.service'
import { WebhookGuard } from './webhook.guard'

@Controller('payments/nmi')
export class NmiController {
  constructor(
    private userService: UserService,
    private nmiService: NmiService,
    private kycService: KycService,
    private locationService: LocationService,
    private captchaService: CaptchaService,
    private redis: RedisService,
    private prisma: PrismaService
  ) {}

  @Post()
  @UseGuards(AccessGuard, BanGuard, NotTraderGuard)
  async create(@Req() req, @Body() body: CreateDto) {
    if (Math.random() >= 0) {
      throw new ServiceUnavailableException('temporarily_disabled')
    }

    const userId = req.user.userId
    const ip = req.headers['cf-connecting-ip']
    const lock = await this.redis.lock([`nmi:${userId}`])

    const isCaptchaValid = await this.captchaService.verify(body)
    assert(isCaptchaValid, 'invalid_captcha')

    // Do not allow Discover cards
    assert(!body.ccnumber.startsWith('6'), 'card_not_accepted')

    const { kycStatus, kycName } = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { kycStatus: true, kycName: true },
    })
    const isKycVerified = this.kycService.isKyced(kycStatus)

    try {
      if (isKycVerified) {
        assert(
          this.kycService.nameMatch(
            [...body.firstName.split(' '), ...body.lastName.split(' ')],
            kycName
          ),
          'kyc_name_mismatch'
        )
      } else {
        if (body.locationPass) {
          await this.locationService.verifyPass(body.locationPass, {
            address: body.address,
            city: body.city,
            zip: body.zip,
          })
        } else {
          assert(body.threeDsVersion, '3ds_required')
        }

        const requiresKyc = await this.kycService.requiresKyc(
          userId,
          body.amountCents,
          kycStatus,
          'nmi'
        )
        assert(!requiresKyc, 'requires_kyc')
      }

      const checkFraud = !isKycVerified || !body.threeDsVersion
      return this.nmiService.createPayment(userId, body, ip, checkFraud)
    } catch (err) {
      if (err instanceof BadRequestException) {
        await this.userService.addLog({
          userId,
          category: 'cashier',
          message: `Failed to finalize NMI payment error=${err.message}`,
        })
      }

      throw err
    } finally {
      lock.release()
    }
  }

  @Post('webhook')
  @UseGuards(WebhookGuard)
  async handleWebhook() {
    throw new NotImplementedException()
  }

  @Get('by-last4/:last4')
  @UseGuards(AccessGuard, StaffGuard)
  async getCardsByLast4(@Param('last4') last4: string) {
    const cards = await this.nmiService.getCardsByLast4(last4)

    return { cards }
  }
}
