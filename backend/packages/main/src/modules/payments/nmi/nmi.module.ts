import { forwardRef, Module } from '@nestjs/common'
import { NmiService } from './nmi.service'
import { NmiController } from './nmi.controller'
import { PaymentsModule } from '../payments.module'
import { UserModule } from '../../user/user.module'
import { AntiFraudModule } from '../antifraud/antifraud.module'
import { KycModule } from '../antifraud/kyc/kyc.module'
import { LocationModule } from '../antifraud/location/location.module'
import { CaptchaModule } from 'src/modules/captcha/captcha.module'

@Module({
  imports: [
    forwardRef(() => PaymentsModule),
    AntiFraudModule,
    KycModule,
    LocationModule,
    CaptchaModule,
    UserModule,
  ],
  providers: [NmiService],
  controllers: [NmiController],
})
export class NmiModule {}
