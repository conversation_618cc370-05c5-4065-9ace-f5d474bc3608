import { Injectable, Logger, ServiceUnavailableException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import axios, { AxiosRequestConfig } from 'axios'
import { Cs2redEvents } from './cs2red.interface'

@Injectable()
export class Cs2redAccess {
  private readonly logger = new Logger(Cs2redAccess.name)
  private readonly url: string
  private readonly secret: string
  private readonly clientId: string

  constructor(private readonly config: ConfigService) {
    const { clientId, secret, url } = this.config.get('cs2red')
    this.clientId = clientId
    this.secret = secret
    this.url = url
  }

  private async request<T>(
    options: Omit<AxiosRequestConfig, 'url'> & { path: string }
  ): Promise<T | null> {
    const { path, headers, ...requestOptions } = options
    const res = await axios({
      ...requestOptions,
      url: `${this.url}/${path}`,
      headers: {
        ...headers,
        Authorization: `Bearer ${this.secret}`,
      },
    }).catch((err) => {
      this.logger.warn(
        `Request failed err=%o data=%o req=%o`,
        err.message,
        err.response?.data,
        options.data
      )
      throw new ServiceUnavailableException('cs2red_down')
    })
    this.logger.log('req=%o res=%o', options.data, res.data)
    return res.data
  }

  async sendEvent(steamId: string, eventName: Cs2redEvents) {
    return await this.request({
      method: 'POST',
      path: `partner/${this.clientId}`,
      data: {
        steamId64: steamId,
        eventName,
      },
    })
  }
}
