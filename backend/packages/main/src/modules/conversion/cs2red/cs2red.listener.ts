import { Injectable } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import {
  DEPOSIT_EVENT,
  DepositEvent,
} from 'src/modules/payments/events/deposit.event'
import { Cs2redService } from './cs2red.service'
import { BET_OUTCOME_EVENT, BetOutcomeEvent } from '@crashgg/common/dist'

@Injectable()
export class Cs2redListener {
  constructor(private cs2redService: Cs2redService) {}

  @OnEvent(DEPOSIT_EVENT, { async: true })
  async onDeposit(depositEvent: DepositEvent) {
    return this.cs2redService.sendEvent(depositEvent)
  }

  @OnEvent(BET_OUTCOME_EVENT, { async: true })
  async onBetOutcome(betOutcomeEvent: BetOutcomeEvent) {
    return this.cs2redService.sendEvent(betOutcomeEvent)
  }
}
