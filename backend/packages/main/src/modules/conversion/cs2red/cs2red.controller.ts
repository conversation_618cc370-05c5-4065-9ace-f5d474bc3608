import { Controller, Get, Param, SetMetadata, UseGuards } from '@nestjs/common'
import { Cs2redService } from './cs2red.service'
import { ApiGuard } from 'src/modules/auth/guards/api.guard'

@Controller('cs2red')
export class Cs2redController {
  constructor(private readonly cs2redService: Cs2redService) {}

  @Get('user-status/:steamId')
  @SetMetadata('validator', (token) => token.scope === 'cs2red')
  @UseGuards(ApiGuard)
  getUserStatus(@Param('steamId') steamId: string) {
    return this.cs2redService.getUserStatus(steamId)
  }
}
