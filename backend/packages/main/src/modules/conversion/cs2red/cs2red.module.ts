import { Module } from '@nestjs/common'
import { Cs2redService } from './cs2red.service'
import { Cs2redController } from './cs2red.controller'
import { UserModule } from 'src/modules/user/user.module'
import { AffiliatesModule } from 'src/modules/affiliates/affiliates.module'
import { Cs2redListener } from './cs2red.listener'
import { Cs2redAccess } from './cs2red.access'
import { PassModule } from 'src/modules/auth/pass/pass.module'

@Module({
  imports: [UserModule, AffiliatesModule, PassModule],
  providers: [Cs2redService, Cs2redListener, Cs2redAccess],
  controllers: [Cs2redController],
  exports: [],
})
export class Cs2redModule {}
