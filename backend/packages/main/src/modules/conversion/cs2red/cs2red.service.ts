import { Injectable, NotFoundException } from '@nestjs/common'
import { AffiliatesService } from 'src/modules/affiliates/affiliates.service'
import { UserService } from 'src/modules/user/user.service'
import { assert } from 'src/utils/assert'
import { CS2RED_AFFILIATE_CODES, getCacheKey } from './cs2red.constants'
import { Cs2redEvents } from './cs2red.interface'
import { DepositEvent } from 'src/modules/payments/events/deposit.event'
import { BetOutcomeEvent, REAL_CURRENCIES } from '@crashgg/common/dist'
import { RedisService } from 'src/modules/redis/redis.service'
import { YEAR } from 'src/common/constants'
import { Cs2redAccess } from './cs2red.access'

@Injectable()
export class Cs2redService {
  constructor(
    private readonly userService: UserService,
    private readonly affiliateService: AffiliatesService,
    private readonly redis: RedisService,
    private readonly cs2redAccess: Cs2redAccess
  ) {}

  private async hasActivatedCode(userId: Int) {
    const affiliateUse = await this.affiliateService.findUse(userId, {
      code: { select: { code: true } },
    })

    return CS2RED_AFFILIATE_CODES.includes(affiliateUse?.code?.code)
  }

  private async getCachedEvents(userId: Int): Promise<string[]> {
    const cacheKey = getCacheKey(userId)
    const cache = await this.redis.getJSON<string[]>(cacheKey)
    return cache || []
  }

  private async cacheEvents(userId: Int, events: string[]) {
    const cacheKey = getCacheKey(userId)
    await this.redis.set(cacheKey, events, YEAR / 1000)
  }

  async getUserStatus(steamId: string) {
    const user = await this.userService.unique({ steamId })
    assert(user, 'User not found', NotFoundException)

    return {
      registered: true,
      affiliateCodeActivated: await this.hasActivatedCode(user.id),
      discordLinked: !!user.discordId,
    }
  }

  async sendEvent(event: DepositEvent | BetOutcomeEvent) {
    const userId = event.userId
    const isActivated = await this.hasActivatedCode(userId)
    if (!isActivated) return

    const completedEvents: Cs2redEvents[] = []

    if (event instanceof DepositEvent) {
      const events = this.handleDepositEvent(event)
      completedEvents.push(...events)
    } else {
      const events = await this.handleBetOutcomeEvent(event)
      completedEvents.push(...events)
    }

    const cachedEvents = await this.getCachedEvents(userId)
    const newEvents = completedEvents.filter(
      (event) => !cachedEvents.includes(event)
    )
    if (newEvents.length === 0) return

    const [user] = await Promise.all([
      this.userService.byId(userId),
      this.cacheEvents(userId, [...cachedEvents, ...newEvents]),
    ])

    const sendEvents = newEvents.map((event) =>
      this.cs2redAccess.sendEvent(user.steamId, event)
    )

    return await Promise.all(sendEvents)
  }

  private handleDepositEvent(event: DepositEvent): Cs2redEvents[] {
    const events: Cs2redEvents[] = []

    if (event.amountUsd >= 1_00) {
      events.push(Cs2redEvents.DEPOSIT_1USD_OR_MORE)
    }
    if (event.amountUsd >= 5_00) {
      events.push(Cs2redEvents.DEPOSIT_5USD_OR_MORE)
    }

    return events
  }

  private async handleBetOutcomeEvent(
    event: BetOutcomeEvent
  ): Promise<Cs2redEvents[]> {
    const events: Cs2redEvents[] = []

    const isRealCurrency = REAL_CURRENCIES.includes(event.currency)
    if (!isRealCurrency) return

    const detectedGame = this.detectGame(event)
    if (detectedGame) {
      events.push(detectedGame)
    }

    const user = await this.userService.byId(event.userId)
    if (user.totalWagered >= 15_00) {
      events.push(Cs2redEvents.WAGERED_15GEMS_OR_MORE)
    }

    return events
  }

  private detectGame(event: BetOutcomeEvent): Cs2redEvents | null {
    switch (event.game) {
      case 'case':
      case 'battle':
        return Cs2redEvents.OPENED_CASE
      case 'roulette':
        return Cs2redEvents.PLAY_DOUBLE
      case 'mines':
        return Cs2redEvents.PLAY_MINES
      case 'crash':
        return Cs2redEvents.PLAY_CRASH
      default:
        return null
    }
  }
}
