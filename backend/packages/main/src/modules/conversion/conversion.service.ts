import { Injectable, Logger } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import axios from 'axios'
import { RedisService } from '../redis/redis.service'
import { AbandonedCartService } from './abandoned-cart/abandoned-cart.service'
import { ClickDto } from './dto/click.dto'
import { TrackingService } from './tracking/tracking.service'
import { Response } from 'express'
import { DAY_MS } from 'src/utils/constants'
import { ScaleoService } from './scaleo/scaleo.service'

@Injectable()
export class ConversionService {
  private readonly logger = new Logger(ConversionService.name)

  constructor(
    private config: ConfigService,
    private redis: RedisService,
    private trackingService: TrackingService,
    private abandonedCartService: AbandonedCartService,
    private scaleoService: ScaleoService
  ) {}

  clickIdKey(userId: Int) {
    return `conversion:clickid:${userId}`
  }

  async registerClick(userId: Int, clickDto: ClickDto, response: Response) {
    this.logger.log('registerClick userId=%d clickDto=%o', userId, clickDto)
    if (clickDto.kind === 'scaleo') {
      const scaleoCategory = await this.scaleoService.attributeCategory(userId)
      return await this.trackingService.updateTracking(userId, {
        scaleoClickId: clickDto.clickId,
        scaleoCategory,
        scaleoAttributeTime: new Date(),
      })
    }
    if (clickDto.kind === 'singular') {
      return await this.trackingService.updateTracking(
        userId,
        {
          singularDeviceId: clickDto.clickId,
        },
        clickDto.utm,
        clickDto.platformClickId
      )
    }
    if (clickDto.kind === 'cj') {
      response.cookie('cje', clickDto.clickId, {
        maxAge: 395 * DAY_MS,
        domain: this.config.get('topLevelDomain'),
        secure: true,
        sameSite: 'strict',
      })
      return await this.trackingService.updateTracking(userId, {
        cjEvent: clickDto.clickId,
      })
    }
    if (clickDto.kind === 'lootably') {
      return await this.trackingService.updateTracking(userId, {
        lootablyClickId: clickDto.clickId,
      })
    }
  }

  async registerDeposit(userId: Int, amount: Int) {
    const { isEnabled, postback } = this.config.get('conversion.redtrack')
    if (!isEnabled) {
      this.logger.log(`Not sending conversion, redtrack is disabled`)
      return
    }

    const key = this.clickIdKey(userId)
    const clickId = await this.redis.get(key)
    if (!clickId) {
      this.logger.log(
        `Not sending convesion, clickId found for userId=%d`,
        userId
      )
      return
    }

    const url = `${postback}?clickid=${clickId}&sum=${amount}`
    try {
      const res = await axios.get(url)
      this.logger.log(
        'Registered deposit conversion userId=%d res=%o',
        userId,
        res.data
      )
    } catch (err) {
      this.logger.warn(
        `Failed to register deposit conversion: %s %o`,
        err.message,
        err.response?.data
      )
    }
  }

  async registerDepositAttempt(userId: Int, provider: string) {
    const providers = this.abandonedCartService.broadenProviders(provider)
    return await this.abandonedCartService.startTracking(userId, providers)
  }
}
