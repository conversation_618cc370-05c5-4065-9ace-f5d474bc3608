import { Injectable } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import {
  DEPOSIT_EVENT,
  DepositEvent,
} from 'src/modules/payments/events/deposit.event'
import { CjService } from './cj.service'
import { PLACE_BET_EVENT, PlaceBetEvent } from '@crashgg/common/dist'

@Injectable()
export class CjListener {
  constructor(private readonly cjService: CjService) {}

  @OnEvent(DEPOSIT_EVENT, { async: true })
  async onDeposit(depositEvent: DepositEvent) {
    return this.cjService.sendDepositEvent(depositEvent)
  }

  @OnEvent(PLACE_BET_EVENT, { async: true })
  async onPlaceBet(placeBetEvent: PlaceBetEvent) {
    return this.cjService.sendPlaceBetEvent(placeBetEvent)
  }
}
