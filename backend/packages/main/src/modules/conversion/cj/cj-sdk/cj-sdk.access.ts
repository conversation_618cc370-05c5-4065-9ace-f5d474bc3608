import { Injectable, Logger } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import {
  ApolloClient,
  DocumentNode,
  InMemoryCache,
  NormalizedCacheObject,
} from '@apollo/client/core'
import { createHttpLink } from '@apollo/client/link/http'
import {
  CANCEL_ORDERS_MUTATION,
  CREATE_ORDERS_MUTATION,
  RESTATE_ORDERS_MUTATION,
} from './cj-sdk.constants'
import { CancelledOrder, NewOrder, RestatedOrder } from './cj-sdk.types'

@Injectable()
export class CjSdkAccess {
  private readonly logger = new Logger(CjSdkAccess.name)
  private readonly apolloClient: ApolloClient<NormalizedCacheObject>

  constructor(private readonly config: ConfigService) {
    const { accessToken, isTestMode } = this.config.get('conversion.cj')
    const endpoint = isTestMode
      ? 'https://tracking.api.cj.com/graphqltest'
      : 'https://tracking.api.cj.com/graphql'

    this.apolloClient = new ApolloClient({
      link: createHttpLink({
        uri: endpoint,
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }),
      cache: new InMemoryCache(),
    })
  }

  async createOrders(orders: NewOrder[]) {
    const filledOrders = this.setEnterpriseId(orders)
    return await this.mutate(CREATE_ORDERS_MUTATION, {
      newOrders: filledOrders,
    })
  }

  async restateOrders(orders: RestatedOrder[]) {
    const filledOrders = this.setEnterpriseId(orders)
    return await this.mutate(RESTATE_ORDERS_MUTATION, {
      restatedOrders: filledOrders,
    })
  }

  async cancelOrders(orders: CancelledOrder[]) {
    const filledOrders = this.setEnterpriseId(orders)
    return await this.mutate(CANCEL_ORDERS_MUTATION, {
      cancelledOrders: filledOrders,
    })
  }

  private setEnterpriseId(
    orders: (NewOrder | RestatedOrder | CancelledOrder)[]
  ) {
    const enterpriseId = this.config.get('conversion.cj.enterpriseId')
    return orders.map((order) => {
      order.enterpriseId = enterpriseId
      return order
    })
  }

  private async mutate(
    mutation: DocumentNode,
    variables: Record<string, (NewOrder | RestatedOrder | CancelledOrder)[]>
  ) {
    const res = await this.apolloClient.mutate({
      mutation,
      variables,
    })

    this.logger.log('CJ mutation vars=%o res=%o', variables, res.data)

    return res.data
  }
}
