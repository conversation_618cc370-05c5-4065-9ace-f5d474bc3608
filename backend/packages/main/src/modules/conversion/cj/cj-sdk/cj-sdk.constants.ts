import { gql } from '@apollo/client/core'

export const REVENUE_SHARE_ACTION_ID = 479673
export const FIRST_TIME_DEPO_ACTION_ID = 479674

export const CREATE_ORDERS_MUTATION = gql`
  mutation CreateOrders($newOrders: [OrderCreateRequest!]!) {
    createOrders(newOrders: $newOrders) {
      orders {
        submissionId
        orderReceivedTime
        advertiser {
          enterpriseId
        }
        actionTracker {
          id
        }
        eventTime
        orderId
        cjEvent
        amount
        discount
        items {
          unitPrice
          quantity
          sku
          discount
        }
        coupon
        currency
      }
      errors {
        message
        fields
      }
    }
  }
`

export const RESTATE_ORDERS_MUTATION = gql`
  mutation RestateOrders($restatedOrders: [OrderRestateRequest!]!) {
    restateOrders(restatedOrders: $restatedOrders) {
      orders {
        submissionId
        orderReceivedTime
        status
        updateTime
        verticalParameters {
          advertiserVertical
          confirmationNumber
          originCity
          originState
          destinationCity
          destinationState
          noCancellation
        }
        advertiser {
          enterpriseId
        }
        actionTracker {
          id
        }
        orderId
        discount
        items {
          unitPrice
          quantity
          sku
          discount
        }
      }
      errors {
        message
        fields
      }
    }
  }
`

export const CANCEL_ORDERS_MUTATION = gql`
  mutation CancelOrders($cancelledOrders: [OrderCancelRequest!]!) {
    cancelOrders(cancelledOrders: $cancelledOrders) {
      orders {
        submissionId
        status
      }
      errors {
        message
        fields
      }
    }
  }
`
