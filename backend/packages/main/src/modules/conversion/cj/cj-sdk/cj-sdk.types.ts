export interface NewOrder {
  enterpriseId?: number
  actionTrackerId: number
  eventTime?: string
  orderId: string
  cjEvent: string
  amount: number
  currency: string
  discount?: number
  coupon?: string
  verticalParameters?: {
    customerStatus: string
    brand: string
  }
}

export interface RestatedOrder {
  enterpriseId?: number
  actionTrackerId: number
  orderId: string
  updateTime: string
  verticalParameters: {
    customerStatus: string
    brand: string
  }
  amount: number
  discount: number
}

export interface CancelledOrder {
  enterpriseId?: number
  actionTrackerId: number
  orderId: string
  updateTime: string
  correctionReason: string
}
