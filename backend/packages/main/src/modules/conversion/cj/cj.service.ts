import { Injectable } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { TrackingService } from '../tracking/tracking.service'
import { CjSdkAccess } from './cj-sdk/cj-sdk.access'
import { DepositEvent } from 'src/modules/payments/events/deposit.event'
import { NewOrder } from './cj-sdk/cj-sdk.types'
import { PlaceBetEvent, REAL_CURRENCIES, noop } from '@crashgg/common/dist'
import {
  FIRST_TIME_DEPO_ACTION_ID,
  REVENUE_SHARE_ACTION_ID,
} from './cj-sdk/cj-sdk.constants'
import retry from 'async-retry'

@Injectable()
export class CjService {
  constructor(
    private readonly sdk: CjSdkAccess,
    private readonly config: ConfigService,
    private readonly trackingService: TrackingService
  ) {}

  private async getCjEvent(userId: Int) {
    const tracking = await this.trackingService.getTracking(userId)
    return tracking?.cjEvent
  }

  private balanceToUsd(balance: Int): Float {
    const usdRate = this.config.get('balanceUsdRate')

    return Math.round(balance * usdRate) / 100
  }

  async sendDepositEvent(depositEvent: DepositEvent) {
    if (!depositEvent.isFtd) {
      return
    }

    const cjEvent = await this.getCjEvent(depositEvent.userId)
    if (!cjEvent) {
      return
    }

    const cjAction: NewOrder = {
      actionTrackerId: FIRST_TIME_DEPO_ACTION_ID,
      orderId: depositEvent.transactionId.toString(),
      cjEvent,
      amount: depositEvent.amountUsd / 100,
      currency: 'USD',
    }
    await this.createOrder(cjAction)
  }

  async sendPlaceBetEvent(placeBetEvent: PlaceBetEvent) {
    if (!REAL_CURRENCIES.includes(placeBetEvent.currency)) {
      return
    }

    const cjEvent = await this.getCjEvent(placeBetEvent.userId)
    if (!cjEvent) {
      return
    }

    const cjAction: NewOrder = {
      actionTrackerId: REVENUE_SHARE_ACTION_ID,
      orderId: `${Date.now()}-${placeBetEvent.userId}`,
      cjEvent,
      amount: this.balanceToUsd(placeBetEvent.amount),
      currency: 'USD',
    }
    await this.createOrder(cjAction)
  }

  async createOrder(order: NewOrder) {
    if (!this.config.get('conversion.cj.accessToken')) {
      return
    }

    return await retry(
      async () => {
        await this.sdk.createOrders([order]).catch((err) => {
          throw err
        })
      },
      { minTimeout: 2000, retries: 3 }
    ).catch(noop)
  }
}
