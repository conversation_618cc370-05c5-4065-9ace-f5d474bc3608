import { Module } from '@nestjs/common'
import { AbandonedCartService } from './abandoned-cart.service'
import { BullModule } from '@nestjs/bullmq'
import { QUEUE_KEY } from './abandoned-cart.constants'
import { EmailModule } from 'src/modules/email/email.module'
import { AbandonedCartProcessor } from './abandoned-cart.processor'
import { UserModule } from 'src/modules/user/user.module'

@Module({
  imports: [
    BullModule.registerQueue({ name: QUEUE_KEY }),
    EmailModule,
    UserModule,
  ],
  providers: [AbandonedCartService, AbandonedCartProcessor],
  exports: [AbandonedCartService],
})
export class AbandonedCartModule {}
