import { Injectable, Logger } from '@nestjs/common'
import { QUEUE_KEY, REMINDER_DELAYS } from './abandoned-cart.constants'
import { AbandonedCartPayload } from './abandoned-cart.interface'
import { warn } from 'src/common/utilities'
import { RedisService } from 'src/modules/redis/redis.service'
import { HOUR_S } from 'src/utils/constants'
import { ConfigService } from '@nestjs/config'
import { InjectQueue } from '@nestjs/bullmq'
import { Queue } from 'bullmq'

@Injectable()
export class AbandonedCartService {
  private readonly logger = new Logger(AbandonedCartService.name)

  readonly FIAT_PROVIDERS = ['giftcard', 'nmi', 'checkout', 'zen', 'paypal']
  readonly CRYPTO_PROVIDERS = ['fireblocks']
  readonly SKIN_PROVIDERS = ['p2p', 'waxpeer', 'skinsback']
  private readonly PROVIDER_CATEGORIES = [
    this.FIAT_PROVIDERS,
    this.CRYPTO_PROVIDERS,
    this.SKIN_PROVIDERS,
  ]

  constructor(
    @InjectQueue(QUEUE_KEY)
    private queue: Queue<AbandonedCartPayload>,
    private redis: RedisService,
    private config: ConfigService
  ) {}

  broadenProviders(provider: string) {
    for (const providers of this.PROVIDER_CATEGORIES) {
      if (providers.includes(provider)) {
        return providers
      }
    }

    return [provider]
  }

  async startTracking(userId: Int, providers: string[]) {
    if (!this.config.get('isClashCsgo')) {
      return
    }

    const key = `conversion:abandoned-cart:${userId}`
    const hasTriggeredRecently = await this.redis.get(key)
    if (hasTriggeredRecently) {
      this.logger.log('Skipping tracking, triggered recently userId=%d', userId)
      return false
    }

    this.logger.log(
      'Starting tracking userId=%d providers=%o',
      userId,
      providers
    )

    const addToQueue = this.queue.add(
      '',
      {
        initiatedAt: new Date(),
        userId,
        providers,
        isAnyProvider: providers.length === 0,
        reminderNum: 1,
      },
      {
        delay: REMINDER_DELAYS[1],
      }
    )

    const registerTriggered = this.redis.set(key, 1, HOUR_S)

    await Promise.allSettled([addToQueue, registerTriggered]).catch(warn)
    return true
  }
}
