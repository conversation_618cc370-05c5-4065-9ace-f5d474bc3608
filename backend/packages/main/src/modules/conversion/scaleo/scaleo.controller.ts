import {
  Controller,
  DefaultValuePipe,
  ForbiddenException,
  Get,
  ParseIntPipe,
  Query,
} from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { ParseDatePipe } from 'src/common/parse-date.pipe'
import { ScaleoService } from './scaleo.service'

@Controller('conversion/scaleo')
export class ScaleoController {
  constructor(
    private scaleoService: ScaleoService,
    private config: ConfigService
  ) {}

  @Get('events')
  async getAggregatedEvents(
    @Query('api-key') apiKey: string,
    @Query('date_start', ParseDatePipe) dateStart: Date,
    @Query('date_end', ParseDatePipe) dateEnd: Date,
    @Query('type') type = 'bet,win,bon',
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: Int,
    @Query('perpage', new DefaultValuePipe(500_000), ParseIntPipe)
    pageSize: Int
  ) {
    const ourApiKey = this.config.get('scaleo.apiKeyPull')
    if (ourApiKey !== apiKey) {
      throw new ForbiddenException()
    }
    const types = type.split(',')

    return this.scaleoService.getAggregatedEvents({
      apiKey,
      dateStart,
      dateEnd,
      types,
      page,
      pageSize,
    })
  }
}
