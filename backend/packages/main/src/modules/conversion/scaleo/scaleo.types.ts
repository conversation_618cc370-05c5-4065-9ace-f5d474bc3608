export interface GetEventsOptions {
  apiKey: string
  dateStart: Date
  dateEnd: Date
  types: string[]
  page: Int
  pageSize: Int
}

export interface ScaleoAggregatedEventsResponse {
  status: string
  code: number
  data: {
    events: ScaleoAggregatedEvent[]
  }
}

export interface ScaleoAggregatedEvent {
  hour: string
  type: string
  click_id: string
  player_id: string
  product: string
  count: number
  amount: number
  currency: string
}

export interface ScaleoCodeUse {
  userId: Int
  code: string
  ip?: string
}

export interface ScaleoTip {
  userId: Int
  code: string
  amount: Int
}
