import { Injectable, Logger } from '@nestjs/common'
import { PrismaService } from 'src/modules/prisma/prisma.service'
import { ScaleoSdkService } from './scaleo-sdk/scaleo-sdk.service'
import {
  GetEventsOptions,
  ScaleoAggregatedEvent,
  ScaleoAggregatedEventsResponse,
  ScaleoCodeUse,
  ScaleoTip,
} from './scaleo.types'
import { DepositEvent } from 'src/modules/payments/events/deposit.event'
import {
  ScaleoBaseEvent,
  ScaleoDepositEvent,
  ScaleoFtdEvent,
  ScaleoRegisterEvent,
  ScaleoWithdrawEvent,
} from './scaleo-sdk/scaleo-sdk.interface'
import { WithdrawalEvent } from 'src/modules/payments/events/withdrawal.event'
import { warn } from 'console'
import { ConfigService } from '@nestjs/config'
import { TrackingService } from '../tracking/tracking.service'
import { TrackingUpdateEvent } from '../tracking/events/tracking-update.event'
import { paginate } from 'src/common/paginate'
import { DAY_MS, REAL_CURRENCIES } from '@crashgg/common/dist'
import { Prisma, ScaleoCategory } from '@prisma/client'
import { PRODUCTS_ON_CATEGORY } from './scaleo.constants'

@Injectable()
export class ScaleoService {
  constructor(
    private sdk: ScaleoSdkService,
    private prisma: PrismaService,
    private config: ConfigService,
    private trackingService: TrackingService
  ) {
    this.defaultProduct = this.config.get('isRustClash')
      ? PRODUCTS_ON_CATEGORY[ScaleoCategory.COLD]
      : 'casino'
  }

  private defaultProduct: string
  private logger = new Logger(ScaleoService.name)

  async usePromoCode(codeUse: ScaleoCodeUse) {
    const tracking = await this.trackingService.getTracking(codeUse.userId)
    if (tracking?.scaleoClickId) {
      return
    }

    let clickId: string | false
    try {
      clickId = await this.sdk.usePromoCode(codeUse.code, codeUse.ip)
    } catch (err) {
      this.logger.warn('Scaleo usePromoCode failed err=%o', err)
      return false
    }

    if (!clickId) return

    const scaleoCategory = await this.attributeCategory(codeUse.userId)
    await this.trackingService.updateTracking(codeUse.userId, {
      scaleoClickId: clickId,
      scaleoCategory,
      scaleoAttributeTime: new Date(),
    })
  }

  async attributeCategory(userId: Int): Promise<ScaleoCategory | null> {
    if (this.config.get('isRustClash')) {
      const lastBetPast60d = await this.prisma.bet.findFirstOrThrow({
        where: {
          userId,
          createdAt: { gte: new Date(Date.now() - 60 * DAY_MS) },
        },
      })

      return lastBetPast60d ? ScaleoCategory.WARM : ScaleoCategory.COLD
    }

    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { totalDeposits: true },
    })
    return user.totalDeposits > 0
      ? ScaleoCategory.EXISTING
      : ScaleoCategory.AFFILIATED
  }

  async getAggregatedEvents(
    options: GetEventsOptions
  ): Promise<ScaleoAggregatedEventsResponse> {
    const eventPromises: Promise<ScaleoAggregatedEvent[]>[] = options.types.map(
      (type) => {
        if (type === 'bet') {
          return this.getAggregatedBetEvents(options)
        } else if (type === 'win') {
          return this.getAggregatedWinEvents(options)
        } else if (type === 'bon') {
          return this.getAggregatedBonusEvents(options)
        }

        return Promise.resolve([] as ScaleoAggregatedEvent[])
      }
    )

    const events: ScaleoAggregatedEvent[] = await Promise.all(
      eventPromises
    ).then((v) => v.flat())

    return {
      status: 'success',
      code: 200,
      data: { events },
    }
  }

  async tipUser(tip: ScaleoTip) {
    const { userId, code, amount } = tip
    const siteCode = this.config.get('siteCode')
    const usdRate = this.config.get('balanceUsdRate')

    await this.usePromoCode({
      userId,
      code: `${siteCode}A${code}`,
    })

    const transactionId = `${siteCode}-tip-${new Date()}`
    await this.sendDepositEvent({
      userId,
      provider: 'tip',
      isFtd: false,
      amountUsd: Math.round(amount / usdRate),
      playAmount: 0,
      transactionId,
    })
  }

  private async getAggregatedBetEvents(
    options: GetEventsOptions
  ): Promise<ScaleoAggregatedEvent[]> {
    const pagination = paginate(options)

    type QueryEvent = Omit<ScaleoAggregatedEvent, 'type' | 'currency'>

    const results: QueryEvent[] = await this.prisma.$queryRaw`select
      date_trunc('hour', b."createdAt") as hour,
      ut."scaleoClickId" as click_id,
      ut."scaleoCategory" as product,
      u.id as player_id,
      count(*)::int as count,
      sum("betAmount")::int as amount
    from "Bet" b
    inner join "User" u on u.id = b."userId"
    inner join "UserTracking" ut on ut."userId" = b."userId"
    where
      ut."scaleoClickId" is not null
      and (b."currency" IN (${Prisma.join(REAL_CURRENCIES)})
        or b."currency" is null)
      and b."createdAt" > ${new Date(options.dateStart)}
      and b."createdAt" < ${new Date(options.dateEnd)}
      and b."createdAt" >= COALESCE(ut."scaleoAttributeTime", ut."createdAt")
    group by 1, 2, 3, 4
    offset ${pagination.skip} limit ${pagination.take}`

    return results.map((event): ScaleoAggregatedEvent => {
      const { product } = event
      return {
        ...event,
        hour: this.toDateString(event.hour),
        type: 'bet',
        player_id: this.toPlayerId(event.player_id),
        product: PRODUCTS_ON_CATEGORY[product] ?? this.defaultProduct,
        currency: 'USD',
        amount: this.balanceToUsd(event.amount),
      }
    })
  }

  private async getAggregatedWinEvents(
    options: GetEventsOptions
  ): Promise<ScaleoAggregatedEvent[]> {
    const pagination = paginate(options)

    type QueryEvent = Omit<ScaleoAggregatedEvent, 'type' | 'currency'>

    const results: QueryEvent[] = await this.prisma.$queryRaw`select
      date_trunc('hour', b."createdAt") as hour,
      ut."scaleoClickId" as click_id,
      ut."scaleoCategory" as product,
      u.id as player_id,
      count(*)::int as count,
      sum("winningAmount")::int as amount
    from "Bet" b
    inner join "User" u on u.id = b."userId"
    inner join "UserTracking" ut on ut."userId" = b."userId"
    where
      ut."scaleoClickId" is not null
      and (b."currency" IN (${Prisma.join(REAL_CURRENCIES)})
        or b."currency" is null)
      and b."createdAt" > ${new Date(options.dateStart)}
      and b."createdAt" < ${new Date(options.dateEnd)}
      and b."createdAt" >= COALESCE(ut."scaleoAttributeTime", ut."createdAt")
    group by 1, 2, 3, 4
    offset ${pagination.skip} limit ${pagination.take}`

    return results.map((event): ScaleoAggregatedEvent => {
      const { product } = event
      return {
        ...event,
        hour: this.toDateString(event.hour),
        type: 'win',
        player_id: this.toPlayerId(event.player_id),
        product: PRODUCTS_ON_CATEGORY[product] ?? 'casino',
        currency: 'USD',
        amount: this.balanceToUsd(event.amount),
      }
    })
  }

  private async getAggregatedBonusEvents(
    _options: GetEventsOptions
  ): Promise<ScaleoAggregatedEvent[]> {
    // TODO
    return []
  }

  async sendRegisterEvent(trackingUpdateEvent: TrackingUpdateEvent) {
    if (!trackingUpdateEvent.scaleoClickId) {
      return
    }
    const scaleoEvent: ScaleoRegisterEvent = {
      type: 'reg',
      click_id: trackingUpdateEvent.scaleoClickId,
    }
    this.sendEvent(trackingUpdateEvent.userId, scaleoEvent)
  }

  async sendDepositEvent(depositEvent: DepositEvent) {
    if (depositEvent.isFtd) {
      const scaleoEvent: ScaleoFtdEvent = {
        type: 'ftd',
        amount: depositEvent.amountUsd / 100,
        currency: 'USD',
      }
      await this.sendEvent(depositEvent.userId, scaleoEvent)
    } else {
      const scaleoEvent: ScaleoDepositEvent = {
        type: 'dep',
        amount: depositEvent.amountUsd / 100,
        currency: 'USD',
        event_id: String(depositEvent.transactionId),
      }
      await this.sendEvent(depositEvent.userId, scaleoEvent)
    }
  }

  async sendWithdrawalEvent(withdrawalEvent: WithdrawalEvent) {
    const scaleoEvent: ScaleoWithdrawEvent = {
      type: 'wdr',
      amount: withdrawalEvent.amountUsd / 100,
      currency: 'USD',
      event_id: String(withdrawalEvent.transactionId),
    }
    await this.sendEvent(withdrawalEvent.userId, scaleoEvent)
  }

  private toPlayerId(userId: Int | string) {
    const siteCode = this.config.get('siteCode')

    return `${siteCode}:${userId}`
  }

  private toDateString(date: string | Date) {
    return new Date(date).toISOString().slice(0, 19).replace('T', ' ')
  }

  private balanceToUsd(balance: Int): Float {
    const usdRate = this.config.get('balanceUsdRate')

    return Math.round(balance * usdRate) / 100
  }

  private async sendEvent<T>(
    userId: Int,
    eventPartial: Omit<T, keyof ScaleoBaseEvent>
  ) {
    const tracking = await this.trackingService.getTracking(userId)
    if (!tracking?.scaleoClickId) {
      return
    }

    const event: ScaleoBaseEvent & any = {
      ...eventPartial,
      adv_user_id: this.toPlayerId(userId),
      click_id: tracking.scaleoClickId,
    }

    await this.sdk.sendEvent(event).catch(warn)
  }
}
