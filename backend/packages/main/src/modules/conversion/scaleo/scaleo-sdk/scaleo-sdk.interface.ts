type ScaleoCurrency = 'USD'

export interface ScaleoBaseEvent {
  timestamp: string
  event_id: string
  adv_user_id: string
}

export interface ScaleoRegisterEvent {
  type: 'reg'
  click_id: string
}

export interface ScaleoFtdEvent {
  type: 'ftd'
  amount: number
  currency: ScaleoCurrency
}

export interface ScaleoDepositEvent {
  type: 'dep'
  amount: number
  currency: ScaleoCurrency
  event_id: string
}

export interface ScaleoWithdrawEvent {
  type: 'wdr'
  amount: number
  currency: ScaleoCurrency
  event_id: string
}

export interface ScaleoGetPromoCode {
  status: 'success' | string
  code: 200 | Int
  name: 'OK' | string
  info: {
    click_id: string
    affiliate_id: Int
  }
}
