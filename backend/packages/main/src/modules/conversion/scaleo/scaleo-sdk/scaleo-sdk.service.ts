import { Injectable, Logger, ServiceUnavailableException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import axios, { AxiosResponse, Method } from 'axios'
import { warn } from 'src/common/utilities'
import {
  ScaleoBaseEvent,
  ScaleoDepositEvent,
  ScaleoFtdEvent,
  ScaleoGetPromoCode,
  ScaleoRegisterEvent,
} from './scaleo-sdk.interface'
import { assert } from 'src/utils/assert'

@Injectable()
export class ScaleoSdkService {
  private logger = new Logger(ScaleoSdkService.name)

  constructor(private config: ConfigService) {}

  async sendEvent(
    event:
      | ScaleoRegisterEvent
      | ScaleoDepositEvent
      | ScaleoFtdEvent
      | ScaleoBaseEvent
  ) {
    const apiKey = this.config.get('scaleo.apiKey')
    const url = `/api/v2/network/tracking/event?api-key=${apiKey}`

    await this.request('GET', url, event).catch(warn)
  }

  async usePromoCode(code: string, ip?: string) {
    const apiKey = this.config.get('scaleo.apiKey')

    const params = new URLSearchParams({
      'api-key': apiKey,
      code,
      ...(ip && { ip }),
    })

    const url = `/api/v2/network/tracker/promo?${params.toString()}`

    try {
      const res = await this.request<ScaleoGetPromoCode>('GET', url).catch(warn)
      assert(res && res.status === 'success' && res.info.click_id)

      return res.info.click_id
    } catch (err) {
      if ((err.response as AxiosResponse)?.status === 422) {
        return false
      }

      throw err
    }
  }

  private async request<T>(
    method: Method,
    path: string,
    data: any = {}
  ): Promise<T> {
    const apiBase = this.config.get('scaleo.apiBase')

    const res = await axios
      .request({
        method,
        url: apiBase + path,
        data,
      })
      .catch((err) => {
        if ((err.response as AxiosResponse)?.status % 100 === 5) {
          this.logger.warn(
            `Scaleo request failed err=%o data=%o req=%o`,
            err.message,
            err.response?.data,
            data
          )
          throw new ServiceUnavailableException('scaleo_down')
        }
        throw err
      })

    this.logger.debug('Scaleo res=%o', res.data)

    return res.data
  }
}
