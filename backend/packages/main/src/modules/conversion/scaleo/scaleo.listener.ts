import { Injectable } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import {
  DEPOSIT_EVENT,
  DepositEvent,
} from 'src/modules/payments/events/deposit.event'
import {
  WITHDRAWAL_EVENT,
  WithdrawalEvent,
} from 'src/modules/payments/events/withdrawal.event'
import { ScaleoService } from './scaleo.service'
import {
  TRACKING_UPDATE_EVENT,
  TrackingUpdateEvent,
} from '../tracking/events/tracking-update.event'

@Injectable()
export class ScaleoListener {
  constructor(private scaleoService: ScaleoService) {}

  @OnEvent(TRACKING_UPDATE_EVENT, { async: true })
  async onKyc(trackingUpdateEvent: TrackingUpdateEvent) {
    return this.scaleoService.sendRegisterEvent(trackingUpdateEvent)
  }

  @OnEvent(DEPOSIT_EVENT, { async: true })
  async onDeposit(depositEvent: DepositEvent) {
    return this.scaleoService.sendDepositEvent(depositEvent)
  }

  @OnEvent(WITHDRAWAL_EVENT)
  async onWithdrawal(withdrawalEvent: WithdrawalEvent) {
    return await this.scaleoService.sendWithdrawalEvent(withdrawalEvent)
  }
}
