import { Module } from '@nestjs/common'
import { ScaleoController } from './scaleo.controller'
import { ScaleoListener } from './scaleo.listener'
import { ScaleoService } from './scaleo.service'
import { ScaleoSdkModule } from './scaleo-sdk/scaleo-sdk.module'
import { TrackingModule } from '../tracking/tracking.module'

@Module({
  providers: [ScaleoService, ScaleoListener],
  controllers: [ScaleoController],
  imports: [ScaleoSdkModule, TrackingModule],
  exports: [ScaleoService],
})
export class ScaleoModule {}
