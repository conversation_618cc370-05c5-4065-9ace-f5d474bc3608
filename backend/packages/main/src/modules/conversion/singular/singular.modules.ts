import { Module } from '@nestjs/common'
import { SingularService } from './singular.service'
import { SingularSdkAccess } from './singular-sdk/singular-sdk.access'
import { TrackingModule } from '../tracking/tracking.module'
import { SingularListener } from './singular.listener'

@Module({
  imports: [TrackingModule],
  providers: [SingularService, SingularSdkAccess, SingularListener],
  exports: [SingularService],
})
export class SingularModule {}
