import { Injectable } from '@nestjs/common'
import { SingularSdkAccess } from './singular-sdk/singular-sdk.access'
import { ConfigService } from '@nestjs/config'
import {
  SingularAttributionData,
  SingularEventData,
} from './singular-sdk/singular-sdk.types'
import { DepositEvent } from 'src/modules/payments/events/deposit.event'
import { Int } from '@crashgg/types'
import { TrackingService } from '../tracking/tracking.service'
import retry from 'async-retry'
import { noop } from '@crashgg/common'
import { TrackingUpdateEvent } from '../tracking/events/tracking-update.event'
import { PlatformClickIds, Utm } from './singular.types'

@Injectable()
export class SingularService {
  constructor(
    private readonly sdk: SingularSdkAccess,
    private readonly config: ConfigService,
    private readonly trackingService: TrackingService
  ) {}

  private async getDeviceId(userId: Int) {
    const tracking = await this.trackingService.getTracking(userId)
    return tracking?.singularDeviceId
  }

  private mapUtmToAttributionData(utm?: Utm) {
    if (!utm) {
      return undefined
    }

    const attribData: SingularAttributionData = {
      partner_name: utm.wpsrc ?? utm.utmSource,
      touch_timestamp: Date.now(),
      partner_campaign_id: utm.wpcid,
      partner_campaign_name: utm.wpcn ?? utm.utmCampaign,
      partner_creative_id: utm.wpcrid,
      partner_keyword: utm.wpkwn ?? utm.utmTerm,
      partner_subcampaign_id: utm.wpscid,
      partner_subcampaign_name: utm.wpscn ?? utm.utmContent,
      partner_site_name: utm.wpsnetn ?? utm.utmMedium,
    }
    // 1 is always sent: touch_timestamp
    if (Object.keys(attribData).length > 1) {
      attribData.is_attributed = true
    }

    const filteredAttribData = Object.fromEntries(
      Object.entries(attribData).filter(([, value]) => !!value)
    )
    return JSON.stringify(filteredAttribData)
  }

  private mapPlatformClickIds(clickIds: PlatformClickIds) {
    const response: Record<string, any> = clickIds
    if (clickIds.fbc) {
      response.fbclid = clickIds.fbc
    }
    if (clickIds.sc_click_id) {
      response.ScCid = clickIds.sc_click_id
      delete response.sc_click_id
    }
    return JSON.stringify(response)
  }

  async sendEvent(params: SingularEventData) {
    if (!this.config.get('conversion.singular.apiKey') || !params.sdid) {
      return
    }

    return await retry(
      async () => {
        await this.sdk.sendEvent(params).catch((err) => {
          throw err
        })
      },
      { minTimeout: 2000, retries: 3 }
    ).catch(noop)
  }

  async sendRegisterEvent(trackingUpdateEvent: TrackingUpdateEvent) {
    if (!trackingUpdateEvent.singularDeviceId) {
      return
    }
    await this.sendEvent({
      n:
        trackingUpdateEvent.dbAction === 'create'
          ? 'sng_complete_registration'
          : 'sng_login',
      custom_user_id: trackingUpdateEvent.userId,
      sdid: trackingUpdateEvent.singularDeviceId,
      // Temporarily disabled for Web SDK
      // ...(trackingUpdateEvent.utm && {
      //   attribution_data: this.mapUtmToAttributionData(trackingUpdateEvent.utm)
      // }),
      // ...(trackingUpdateEvent.platformClickId && {
      //   e: this.mapPlatformClickIds(trackingUpdateEvent.platformClickId)
      // })
    })
  }

  async sendDepositEvent(depositEvent: DepositEvent) {
    const sdid = await this.getDeviceId(depositEvent.userId)

    return await this.sendEvent({
      n: 'deposit',
      sdid,
      conversion_event: true,
      custom_user_id: depositEvent.userId,
      is_revenue_event: true,
      amt: depositEvent.amountUsd / 100,
      cur: 'USD',
      purchase_transaction_id: depositEvent.transactionId,
      e: JSON.stringify({ FTD: depositEvent.isFtd }),
    })
  }
}
