import { Injectable } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import {
  DEPOSIT_EVENT,
  DepositEvent,
} from 'src/modules/payments/events/deposit.event'
import { SingularService } from './singular.service'
import {
  TRACKING_UPDATE_EVENT,
  TrackingUpdateEvent,
} from '../tracking/events/tracking-update.event'

@Injectable()
export class SingularListener {
  constructor(private readonly singularService: SingularService) {}

  @OnEvent(TRACKING_UPDATE_EVENT, { async: true })
  async onKyc(trackingUpdateEvent: TrackingUpdateEvent) {
    return this.singularService.sendRegisterEvent(trackingUpdateEvent)
  }

  @OnEvent(DEPOSIT_EVENT, { async: true })
  async onDeposit(depositEvent: DepositEvent) {
    return this.singularService.sendDepositEvent(depositEvent)
  }
}
