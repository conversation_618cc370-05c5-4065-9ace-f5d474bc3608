import { Int } from '@crashgg/types'

export interface SingularEventData {
  // event name
  n: string
  // API_KEY
  a?: string
  // Platform Android or iOS.
  p?: Platform
  // Package Name (Android) or Bundle ID (iOS) of your application.
  i?: string
  // Ip of the event
  ip?: string
  // The IETF local tag for the device, using two-letter language and country code separated by an underscore. For example, en_US.
  lc?: string
  // Make of the device hardware, typically the consumer-facing name (e.g. Samsung, LG, Apple). This parameter must be used with the model parameter.
  ma?: string
  // Model of the device hardware (e.g. iPhone 4S, Galaxy SIII). This parameter must be used with the make parameter.
  mo?: string
  // Pass 1 if do not track is enabled, 0 if do not track is disabled
  dnt?: number
  // For iOS apps only. Upper-case raw advertising ID with dashes.
  idfa?: string
  idfv?: string
  // For Android.Lowercase raw advertising "8ecd7512-2864-440c-93f3-a3cabe62525b";
  aifa?: string
  // Android 12+ App Set ID. Should be sent if aifa is not available.
  asid?: string
  //  Lower-case raw android ID "fc8d449516de0dfb". Should be sent if other identifiers are not available.
  andi?: string
  // For conversion events - landing page url with utm params (See the web SDK doc for more details).
  web_url?: string
  // Web: The UUIDv4 device ID generated or provided by Singular's WebSDK
  sdid: string
  // OS version of the device at event time.
  ve?: string
  // IOS 14+ The App Tracking Transparency authorization status. 0 - Not Determined, 1 - Restricted, 2 - Denied, 3 - Authorized
  att_authorization_status?: number

  // optional params

  // User ID  "123456789abcd";
  custom_user_id?: Int
  // Time of the event in UNIX time.  1483228800;
  utime?: number
  // Time of the event in milliseconds UNIX time.
  umilisec?: number
  // Extract the IP field from the HTTP request. If you set this to true, don't provide the ip parameter.
  use_ip?: boolean
  // Custom event attributes in JSON format. finally URLEncoded JSON Object.
  e?: string
  // You can define up to 5 global properties. Each property key and value can be up to 200 characters long. The value must be a URLEncoded JSON Object.
  global_properties?: object
  // Whether it's a revenue event. You can omit this if the event name is __iap__ or a non-zero amt is provided.
  is_revenue_event?: boolean
  // The currency amount. This should be used in conjunction with the cur parameter.
  amt?: number
  // The ISO 4217 three-letter currency code. This should be used in conjunction with the amt parameter.
  cur?: string
  purchase_transaction_id?: string | number
  attribution_data?: string // JSON stringified SingularAttributionData
  // https://support.singular.net/hc/en-us/articles/360048588672-Server-to-Server-S2S-API-Endpoint-Reference?navigation_side_bar=true

  conversion_event?: boolean
}

export interface SingularAttributionData {
  partner_name?: string
  agency_id?: string
  is_attributed?: boolean
  touch_timestamp?: number
  partner_affiliate_id?: string
  partner_affiliate_name?: string
  partner_campaign_id?: string
  partner_campaign_name?: string
  partner_creative_id?: string
  partner_creative_name?: string
  partner_keyword?: string
  partner_site?: string
  partner_site_id?: string
  partner_site_name?: string
  partner_sub_site?: string
  partner_sub_site_id?: string
  partner_sub_site_name?: string
  partner_subcampaign_id?: string
  partner_subcampaign_name?: string
  _p?: string
}

export enum Platform {
  WEB = 'Web',
  ANDROID = 'Android',
  IOS = 'iOS',
}
