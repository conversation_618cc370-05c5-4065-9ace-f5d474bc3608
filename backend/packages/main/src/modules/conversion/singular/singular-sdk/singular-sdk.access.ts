import { Injectable, Logger, ServiceUnavailableException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import axios from 'axios'
import { Platform, SingularEventData } from './singular-sdk.types'

@Injectable()
export class SingularSdkAccess {
  private readonly logger = new Logger(SingularSdkAccess.name)

  constructor(private readonly config: ConfigService) {}

  async sendEvent(eventOptions: SingularEventData) {
    return await this._request<any>('evt', eventOptions)
  }

  private async _request<T>(
    path: string,
    params: SingularEventData
  ): Promise<T> {
    const { apiKey, packageName } = this.config.get('conversion.singular')
    params.a = apiKey
    params.i = packageName
    if (!params.p) {
      params.p = Platform.WEB
    }

    const res = await axios
      .get(`https://s2s.singular.net/api/v1/${path}`, {
        params,
      })
      .catch((err) => {
        this.logger.warn(
          `Singular request failed err=%o data=%o req=%o`,
          err.message,
          err.response?.data,
          params
        )
        throw new ServiceUnavailableException('singular_down')
      })

    this.logger.log('Singular res=%o params=%o', res.data, params)

    return res.data
  }
}
