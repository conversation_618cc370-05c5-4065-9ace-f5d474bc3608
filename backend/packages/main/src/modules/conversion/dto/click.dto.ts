import { IsOptional, IsString, Length } from 'class-validator'
import {
  PlatformClickIds as IPlatformClickIds,
  Utm,
} from '../singular/singular.types'

class PlatformClickIds implements IPlatformClickIds {
  @IsOptional()
  @IsString()
  @Length(1, 255)
  fbc?: string

  @IsOptional()
  @IsString()
  @Length(1, 255)
  sc_click_id?: string

  @IsOptional()
  @IsString()
  @Length(1, 255)
  fbclid?: string

  @IsOptional()
  @IsString()
  @Length(1, 255)
  ScCid?: string

  @IsOptional()
  @IsString()
  @Length(1, 255)
  ttclid?: string

  @IsOptional()
  @IsString()
  @Length(1, 255)
  gclid?: string

  @IsOptional()
  @IsString()
  @Length(1, 255)
  gbraid?: string

  @IsOptional()
  @IsString()
  @Length(1, 255)
  wbraid?: string

  @IsOptional()
  @IsString()
  @Length(1, 255)
  rdt_uuid?: string
}

export class ClickDto {
  @IsOptional()
  kind?: string

  @IsString()
  @Length(1, 255)
  clickId: string

  @IsOptional()
  utm?: Utm

  @IsOptional()
  platformClickId?: PlatformClickIds
}
