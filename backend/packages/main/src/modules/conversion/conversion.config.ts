import { registerAs } from '@nestjs/config'
import { bool } from 'src/modules/config/configuration'

export default registerAs('conversion', () => ({
  redtrack: {
    isEnabled: bool(process.env.REDTRACK_ENABLED),
    postback: process.env.REDTRACK_POSTBACK,
  },
  singular: {
    apiKey: process.env.SINGULAR_API_KEY,
    packageName: process.env.SINGULAR_PACKAGE_NAME,
  },
  cj: {
    isTestMode: bool(process.env.CJ_TEST_MODE),
    accessToken: process.env.CJ_ACCESS_TOKEN,
    enterpriseId: process.env.CJ_ENTERPRISE_ID,
  },
  hyros: {
    apiKey: process.env.HYROS_API_KEY,
  },
  lootably: {
    apiKey: process.env.LOOTABLY_TRACKING_API_KEY,
  },
}))
