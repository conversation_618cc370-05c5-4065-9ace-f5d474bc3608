import { PlatformClickIds, Utm } from '../../singular/singular.types'

export const TRACKING_UPDATE_EVENT = 'tracking.update'

export class TrackingUpdateEvent {
  userId: Int
  dbAction: 'create' | 'update'
  scaleoClickId?: string
  singularDeviceId?: string
  lootablyClickId?: string
  utm?: Utm
  platformClickId?: PlatformClickIds

  constructor(data: TrackingUpdateEvent) {
    Object.assign(this, data)
  }
}
