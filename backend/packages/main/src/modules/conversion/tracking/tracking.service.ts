import { BadRequestException, Injectable } from '@nestjs/common'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { Prisma } from '@prisma/client'
import { PrismaService } from 'src/modules/prisma/prisma.service'
import {
  TRACKING_UPDATE_EVENT,
  TrackingUpdateEvent,
} from './events/tracking-update.event'
import { PlatformClickIds, Utm } from '../singular/singular.types'

@Injectable()
export class TrackingService {
  constructor(
    private prisma: PrismaService,
    private eventEmitter: EventEmitter2
  ) {}

  async updateTracking(
    userId: Int,
    changes: Prisma.UserTrackingUncheckedUpdateInput,
    utm?: Utm,
    platformClickId?: PlatformClickIds
  ) {
    let dbAction = 'create'
    const tracking = await this.getTracking(userId)
    if (tracking) {
      dbAction = 'update'
      const unchangeable = ['scaleoClickId', 'lootablyClickId']
      for (const key in changes) {
        if (unchangeable.includes(key) && tracking[key] !== changes[key]) {
          throw new BadRequestException('cannot_change_field')
        }
      }
    }

    const result = await this.prisma.userTracking.upsert({
      where: { userId },
      create: { userId, ...changes } as any,
      update: changes,
    })

    this.eventEmitter.emit(
      TRACKING_UPDATE_EVENT,
      new TrackingUpdateEvent({
        userId,
        ...(changes as any),
        dbAction,
        utm,
        platformClickId,
      })
    )

    return result
  }

  async getTracking(userId: Int) {
    const tracking = await this.prisma.userTracking.findUnique({
      where: { userId },
    })

    return tracking
  }
}
