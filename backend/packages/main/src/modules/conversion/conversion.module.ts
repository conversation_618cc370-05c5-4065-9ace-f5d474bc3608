import { Module } from '@nestjs/common'
import { ConditionalModule } from '../config/conditional-module'
import { AbandonedCartModule } from './abandoned-cart/abandoned-cart.module'
import { ConversionController } from './conversion.controller'
import { ConversionService } from './conversion.service'
import { ScaleoModule } from './scaleo/scaleo.module'
import { TrackingModule } from './tracking/tracking.module'
import { SingularModule } from './singular/singular.modules'
import { CjModule } from './cj/cj.modules'
import { HyrosModule } from './hyros/hyros.module'
import { LootablyTrackingModule } from './lootably-tracking/lootably-tracking.module'
import { ScaleoService } from './scaleo/scaleo.service'
import { ScaleoSdkService } from './scaleo/scaleo-sdk/scaleo-sdk.service'
import { Cs2redModule } from './cs2red/cs2red.module'

@Module({
  providers: [ConversionService, ScaleoService, ScaleoSdkService],
  controllers: [ConversionController],
  exports: [ConversionService],
  imports: [
    AbandonedCartModule,
    ConditionalModule.forEnvVar(ScaleoModule, 'SCALEO_API_KEY'),
    ConditionalModule.forEnvVar(SingularModule, 'SINGULAR_API_KEY'),
    ConditionalModule.forEnvVar(CjModule, 'CJ_ACCESS_TOKEN'),
    ConditionalModule.forEnvVar(HyrosModule, 'HYROS_API_KEY'),
    ConditionalModule.forEnvVar(
      LootablyTrackingModule,
      'LOOTABLY_TRACKING_API_KEY'
    ),
    ConditionalModule.forEnvVar(Cs2redModule, 'CS2RED_SECRET'),
    TrackingModule,
  ],
})
export class ConversionModule {}
