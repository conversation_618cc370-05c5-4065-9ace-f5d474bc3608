import { Body, Controller, Post, Res, UseGuards } from '@nestjs/common'
import { UserId } from 'src/common/user-id.decorator'
import { AccessGuard } from '../auth/guards/access.guard'
import { ConversionService } from './conversion.service'
import { ClickDto } from './dto/click.dto'
import { Response } from 'express'

@Controller('conversion')
export class ConversionController {
  constructor(private conversionService: ConversionService) {}

  @Post('register-click')
  @UseGuards(AccessGuard)
  async setClickId(
    @UserId() userId: Int,
    @Body() clickDto: ClickDto,
    @Res({ passthrough: true }) response: Response
  ) {
    return await this.conversionService.registerClick(
      userId,
      clickDto,
      response
    )
  }

  @Post('deposit-attempt')
  @UseGuards(AccessGuard)
  async registerDepositAttempt(
    @UserId() userId: Int,
    @Body('provider') body: string
  ) {
    const success = await this.conversionService.registerDepositAttempt(
      userId,
      body
    )
    return { success }
  }
}
