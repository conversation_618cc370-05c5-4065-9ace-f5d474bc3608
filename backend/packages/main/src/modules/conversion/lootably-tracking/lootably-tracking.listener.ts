import { Injectable } from '@nestjs/common'
import { LootablyTrackingService } from './lootably-tracking.service'
import { OnEvent } from '@nestjs/event-emitter'
import {
  DEPOSIT_EVENT,
  DepositEvent,
} from 'src/modules/payments/events/deposit.event'
import {
  BET_OUTCOME_EVENT,
  BetOutcomeEvent,
  PLACE_BET_EVENT,
  PlaceBetEvent,
} from '@crashgg/common/dist'
import {
  WITHDRAWAL_EVENT,
  WithdrawalEvent,
} from 'src/modules/payments/events/withdrawal.event'
import {
  TRACKING_UPDATE_EVENT,
  TrackingUpdateEvent,
} from '../tracking/events/tracking-update.event'
import {
  USER_LOGIN_EVENT,
  UserLoginEvent,
} from 'src/modules/auth/events/user-login.event'

@Injectable()
export class LootablyTrackingListener {
  constructor(private readonly lootablyTracking: LootablyTrackingService) {}

  @OnEvent(PLACE_BET_EVENT, { async: true })
  async onPlaceBet(placeBetEvent: PlaceBetEvent) {
    return this.lootablyTracking.sendWager(placeBetEvent)
  }

  @OnEvent(BET_OUTCOME_EVENT, { async: true })
  async onBetOutcome(placeBetEvent: BetOutcomeEvent) {
    return this.lootablyTracking.sendWin(placeBetEvent)
  }

  @OnEvent(DEPOSIT_EVENT, { async: true })
  async onDeposit(depositEvent: DepositEvent) {
    return this.lootablyTracking.sendDeposit(depositEvent)
  }

  @OnEvent(WITHDRAWAL_EVENT, { async: true })
  async onWithdrawal(depositEvent: WithdrawalEvent) {
    return this.lootablyTracking.sendWithdrawal(depositEvent)
  }

  @OnEvent(TRACKING_UPDATE_EVENT, { async: true })
  async onClick(trackingUpdateEvent: TrackingUpdateEvent) {
    return this.lootablyTracking.sendRegister(trackingUpdateEvent)
  }

  @OnEvent(USER_LOGIN_EVENT, { async: true })
  async onUserLogin(userLoginEvent: UserLoginEvent) {
    return this.lootablyTracking.sendLogin(userLoginEvent)
  }
}
