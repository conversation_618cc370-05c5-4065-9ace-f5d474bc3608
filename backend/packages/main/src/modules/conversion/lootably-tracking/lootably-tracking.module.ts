import { Module } from '@nestjs/common'
import { TrackingModule } from '../tracking/tracking.module'
import { LootablyTrackingService } from './lootably-tracking.service'
import { LootablyTrackingListener } from './lootably-tracking.listener'
import { UserModule } from 'src/modules/user/user.module'

@Module({
  imports: [TrackingModule, UserModule],
  providers: [LootablyTrackingService, LootablyTrackingListener],
  exports: [LootablyTrackingService],
})
export class LootablyTrackingModule {}
