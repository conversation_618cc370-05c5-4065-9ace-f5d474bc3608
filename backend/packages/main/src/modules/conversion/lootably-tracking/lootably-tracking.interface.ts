export interface WagerRouteBody {
  clickID: string
  amount: number
  gameKey: string
  sub1?: string
}

export interface WinRouteBody {
  clickID: string
  amount: number
  gameKey: string
  sub1?: string
}

export interface DepositRouteBody {
  clickID: string
  amount: number
  depositMethodKey: string
  sub1?: string
}

export interface WithdrawalRouteBody {
  clickID: string
  amount: number
  sub1?: string
}

export interface RegisterRouteBody {
  clickID: string
  sub1?: string
}

export interface LoginRouteBody {
  clickID: string
  sub1?: string
}

export type CollectionRoute =
  | {
      route: 'wager'
      data: WagerRouteBody
    }
  | {
      route: 'win'
      data: WinRouteBody
    }
  | {
      route: 'deposit'
      data: DepositRouteBody
    }
  | {
      route: 'withdrawal'
      data: WithdrawalRouteBody
    }
  | {
      route: 'register'
      data: RegisterRouteBody
    }
  | {
      route: 'login'
      data: LoginRouteBody
    }

export type OptionalClickId<T> = T extends {
  route: string
  data: { clickID: string }
}
  ? {
      route: T['route']
      data: Omit<T['data'], 'clickID'> & { clickID?: string }
    }
  : T
