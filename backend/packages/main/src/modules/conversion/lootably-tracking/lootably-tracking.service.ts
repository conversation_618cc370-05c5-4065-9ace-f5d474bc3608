import { Injectable, Logger, ServiceUnavailableException } from '@nestjs/common'
import { CollectionRoute, OptionalClickId } from './lootably-tracking.interface'
import axios from 'axios'
import { ConfigService } from '@nestjs/config'
import {
  BetOutcomeEvent,
  HOUR_MS,
  PlaceBetEvent,
  REAL_CURRENCIES,
} from '@crashgg/common/dist'
import { DepositEvent } from 'src/modules/payments/events/deposit.event'
import { WithdrawalEvent } from 'src/modules/payments/events/withdrawal.event'
import { USD } from 'src/utils/conversion'
import { TrackingUpdateEvent } from '../tracking/events/tracking-update.event'
import { TrackingService } from '../tracking/tracking.service'
import { UserService } from 'src/modules/user/user.service'
import { UserLoginEvent } from 'src/modules/auth/events/user-login.event'

@Injectable()
export class LootablyTrackingService {
  logger = new Logger(LootablyTrackingService.name)
  private readonly URL = 'https://track.lootably.com/reporting/collect'
  private readonly apiKey: string

  constructor(
    private readonly config: ConfigService,
    private readonly userService: UserService,
    private readonly trackingService: TrackingService
  ) {
    const { apiKey } = this.config.get('conversion.lootably')
    this.apiKey = apiKey
  }

  private async request<T>({
    route,
    data,
  }: CollectionRoute): Promise<T | null> {
    const res = await axios({
      method: 'POST',
      url: this.URL + '/' + route,
      data,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': this.apiKey,
      },
    }).catch((err) => {
      this.logger.warn(
        `Lootably request failed err=%o data=%o req=%o`,
        err.message,
        err.response?.data,
        data
      )
      throw new ServiceUnavailableException('pulse_down')
    })
    this.logger.log('Lootably req=%o res=%o', data, res.data)
    return res.data
  }

  private isCompleteRoute(
    route: OptionalClickId<CollectionRoute>
  ): route is CollectionRoute {
    return !!route.data.clickID
  }

  private async send(userId: Int, collect: OptionalClickId<CollectionRoute>) {
    if (!collect.data.clickID) {
      const tracking = await this.trackingService.getTracking(userId)
      collect.data.clickID = tracking?.lootablyClickId
    }

    if (!this.isCompleteRoute(collect)) {
      this.logger.log(
        'Skipping lootably tracking event, no clickID collect=%o',
        collect
      )
      return
    }

    const siteCode = this.config.get('siteCode')
    collect.data.sub1 = `${siteCode}:${userId}`

    await this.request(collect)
  }

  async sendWager(event: PlaceBetEvent) {
    if (!REAL_CURRENCIES.includes(event.currency)) {
      return
    }

    await this.send(event.userId, {
      route: 'wager',
      data: {
        amount: USD.toFloat(event.amount),
        gameKey: event.game,
      },
    })
  }

  async sendWin(event: BetOutcomeEvent) {
    if (!REAL_CURRENCIES.includes(event.currency) || event.winAmount === 0) {
      return
    }

    await this.send(event.userId, {
      route: 'win',
      data: {
        amount: USD.toFloat(event.winAmount),
        gameKey: event.game,
      },
    })
  }

  async sendDeposit(event: DepositEvent) {
    await this.send(event.userId, {
      route: 'deposit',
      data: {
        amount: USD.toFloat(event.amountUsd),
        depositMethodKey: event.provider,
      },
    })
  }

  async sendWithdrawal(event: WithdrawalEvent) {
    await this.send(event.userId, {
      route: 'withdrawal',
      data: {
        amount: USD.toFloat(event.amountUsd),
      },
    })
  }

  async sendRegister(event: TrackingUpdateEvent) {
    if (!event.lootablyClickId) {
      return
    }

    const user = await this.userService.byId(event.userId)
    if (user.createdAt.getTime() < Date.now() - HOUR_MS) {
      this.logger.log(
        'Skipping lootably register event, old account userId=%d',
        event.userId
      )
      return
    }

    await this.send(event.userId, {
      route: 'register',
      data: {
        clickID: event.lootablyClickId,
      },
    })
  }

  async sendLogin(event: UserLoginEvent) {
    await this.send(event.userId, {
      route: 'login',
      data: {},
    })
  }
}
