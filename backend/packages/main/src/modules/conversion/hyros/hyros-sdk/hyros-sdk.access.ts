import { Injectable, Logger } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import axios, { AxiosRequestConfig, Method } from 'axios'
import { Order } from './hyros-sdk.types'

@Injectable()
export class HyrosSdkAccess {
  private logger = new Logger(HyrosSdkAccess.name)
  constructor(private readonly config: ConfigService) {}

  async createOrder(order: Order) {
    return await this._request<any>('post', '/orders', order)
  }

  private async _request<T>(
    method: Method,
    path: string,
    data: any = {},
    log = true
  ): Promise<T> {
    const apiKey = this.config.get('conversion.hyros.apiKey')

    const requestConfig: AxiosRequestConfig = {
      method,
      url: `https://api.hyros.com/v1/api/v1.0${path}`,
      headers: {
        'API-Key': apiKey,
      },
    }

    if (method.toUpperCase() === 'GET') {
      requestConfig.params = data
    } else {
      requestConfig.data = data
    }

    const res = await axios.request(requestConfig).catch((err) => {
      if (log)
        this.logger.warn(
          'Hyros request failed path=%s err=%o data=%o req=%o',
          path,
          err.message,
          err.response?.data,
          data
        )
      throw err
    })

    if (log)
      this.logger.log(
        'Hyros request sucessful path=%s data=%o res=%o',
        path,
        data,
        res.data
      )

    return res.data
  }
}
