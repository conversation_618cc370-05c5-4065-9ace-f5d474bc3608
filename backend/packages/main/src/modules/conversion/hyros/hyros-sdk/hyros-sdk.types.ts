export interface OrderItem {
  name: string
  price: Int
  externalId: string
  costOfGoods?: Int
  quantity?: Int
  taxes?: number
  itemDiscount?: number
  packages?: string[]
  tag?: string
}

export interface Order {
  email: string
  parentEmail?: string
  firstName?: string
  lastName?: string
  leadIps: string[]
  stage?: string
  phoneNumbers?: string[]
  orderId?: string
  cartId?: string
  date?: string // ISO 8601 format, e.g., "2021-04-16T20:35:00"
  priceFormat?: 'DECIMAL' | 'INTEGER'
  currency: string
  items: OrderItem[]
}
