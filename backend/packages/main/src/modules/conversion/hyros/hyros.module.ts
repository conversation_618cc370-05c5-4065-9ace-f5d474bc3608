import { Module } from '@nestjs/common'
import { HyrosService } from './hyros.service'
import { HyrosSdkAccess } from './hyros-sdk/hyros-sdk.access'
import { HyrosListener } from './hyros.listener'
import { UserModule } from 'src/modules/user/user.module'
import { IpLockModule } from 'src/modules/ip-lock/ip-lock.module'

@Module({
  providers: [HyrosService, HyrosSdkAccess, HyrosListener],
  imports: [UserModule, IpLockModule],
})
export class HyrosModule {}
