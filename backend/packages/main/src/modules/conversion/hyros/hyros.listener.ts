import { Injectable } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import {
  DEPOSIT_EVENT,
  DepositEvent,
} from 'src/modules/payments/events/deposit.event'
import { HyrosService } from './hyros.service'

@Injectable()
export class HyrosListener {
  constructor(private hyrosService: HyrosService) {}

  @OnEvent(DEPOSIT_EVENT, { async: true })
  async onDeposit(depositEvent: DepositEvent) {
    return this.hyrosService.sendDepositEvent(depositEvent)
  }
}
