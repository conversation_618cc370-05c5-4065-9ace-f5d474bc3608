import { Injectable } from '@nestjs/common'
import { DepositEvent } from 'src/modules/payments/events/deposit.event'
import { UserService } from 'src/modules/user/user.service'
import { HyrosSdkAccess } from './hyros-sdk/hyros-sdk.access'
import { HOUR_MS } from '@crashgg/common/dist'
import { ConfigService } from 'src/modules/config/config.service'
import { IpLockAccess } from 'src/modules/ip-lock/ip-lock.access'
import { Order } from './hyros-sdk/hyros-sdk.types'
import { DepositToOrder } from './hyros.types'

@Injectable()
export class HyrosService {
  constructor(
    private userService: UserService,
    private sdk: HyrosSdkAccess,
    private config: ConfigService,
    private ipLockAccess: IpLockAccess
  ) {}

  async sendDepositEvent(depositEvent: DepositEvent) {
    const { userId, context } = depositEvent
    const [user, userIps] = await Promise.all([
      this.userService.byId(userId),
      this.ipLockAccess.getUserIps(userId, 6 * HOUR_MS, 5),
    ])

    const email =
      context?.email || user.email || this._getDefaultUserEmail(userId)

    const [kycFirstName, kycLastName] = user.kycName?.split(' ') || []
    const firstName = context?.firstName || kycFirstName
    const lastName = context?.lastName || kycLastName

    const order = this._depositToOrder({
      firstName,
      lastName,
      email,
      leadIps: userIps.map((ip) => ip.address),
      depositEvent,
    })
    await this.sdk.createOrder(order)
  }

  private _getDefaultUserEmail(userId: Int) {
    const domain = this.config.get('topLevelDomain')
    return `user${userId}@${domain}`
  }

  private _depositToOrder(data: DepositToOrder): Order {
    const { depositEvent, firstName, lastName, ...rest } = data
    const { amountUsd, transactionId } = depositEvent
    return {
      firstName: firstName?.toUpperCase(),
      lastName: lastName?.toUpperCase(),
      items: [this._siteBalanceItem(amountUsd)],
      orderId: transactionId as string,
      currency: 'USD',
      stage: depositEvent.isFtd ? 'FTD' : 'Subsequent',
      priceFormat: 'INTEGER',
      ...rest,
    }
  }

  private _siteBalanceItem(amountUsd: Int) {
    return { externalId: '1', name: 'Site balance (USD)', price: amountUsd }
  }
}
