import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common'
import { BaccaratService } from './baccarat.service'
import { LockMode, RedisService } from 'src/modules/redis/redis.service'
import { UserId } from 'src/common/user-id.decorator'
import {
  CurrenciesUsed,
  CurrencyUsed,
  QueryCurrency,
  VaryCurrency,
} from 'src/common/currency.decorator'
import { Currency } from '@crashgg/types'
import { PlayDto } from './dto/play.dto'
import { AccessGuard } from 'src/modules/auth/guards/access.guard'
import { BanGuard } from 'src/modules/auth/guards/ban.guard'
import { PaginatedDto } from 'src/common/dto/paginated.dto'

@Controller('baccarat')
export class BaccaratController {
  constructor(
    private readonly baccaratService: BaccaratService,
    private readonly redis: RedisService
  ) {}

  @Post()
  @UseGuards(AccessGuard, BanGuard)
  async play(
    @UserId() userId: Int,
    @Body() body: PlayDto,
    @CurrencyUsed([Currency.REAL, Currency.PLAY])
    currency: Currency
  ) {
    const lock = await this.redis.lock(
      [`charge:${userId}`, `fairness:seed:${userId}`],
      LockMode.Fast
    )
    return this.baccaratService
      .play(userId, body, currency)
      .finally(lock.release)
  }

  @Get('game-history')
  @VaryCurrency()
  @UseGuards(AccessGuard)
  async getGameHistory(
    @UserId() userId: Int,
    @Query() paginationOptions: PaginatedDto,
    @QueryCurrency() currency?: Currency
  ) {
    return this.baccaratService.getGameHistory(
      userId,
      paginationOptions,
      currency
    )
  }

  @Get('recent')
  @VaryCurrency()
  async getRecentGames(
    @CurrenciesUsed([Currency.REAL, Currency.PLAY])
    currencies: Currency[]
  ) {
    return this.baccaratService.getRecentGames(currencies)
  }
}
