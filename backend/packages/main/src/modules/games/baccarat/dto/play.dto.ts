import { applyDecorators } from '@nestjs/common'
import { Type } from 'class-transformer'
import {
  IsInt,
  IsObject,
  IsOptional,
  IsString,
  Length,
  Max,
  Min,
  ValidateNested,
} from 'class-validator'
import { ClientSeedDto } from 'src/common/dto/client-seed.dto'

function IsBetAmount() {
  return applyDecorators(IsOptional(), IsInt(), Min(10), Max(5_000_00))
}

export class Bet {
  @IsBetAmount()
  banker?: Int

  @IsBetAmount()
  player?: Int

  @IsBetAmount()
  tie?: Int
}

export class PlayDto extends ClientSeedDto {
  @IsObject()
  @ValidateNested()
  @Type(() => Bet)
  bet: Bet

  @IsOptional()
  @IsString()
  @Length(3, 64)
  serverHash?: string
}
