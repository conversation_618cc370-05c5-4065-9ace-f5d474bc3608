import { Module } from '@nestjs/common'
import { BaccaratController } from './baccarat.controller'
import { BaccaratService } from './baccarat.service'
import { FairnessModule } from 'src/modules/fairness/fairness.module'
import { UserModule } from 'src/modules/user/user.module'
import { BaccaratAccess } from './baccarat.access'
import { RainModule } from 'src/modules/rain/rain.module'

@Module({
  imports: [FairnessModule, UserModule, RainModule],
  providers: [BaccaratService, BaccaratAccess],
  controllers: [BaccaratController],
})
export class BaccaratModule {}
