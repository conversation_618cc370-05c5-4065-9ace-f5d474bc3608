import { Card, RANKS, SUITS } from '@crashgg/types'
import seedrandom from 'seedrandom'
import {
  BACCARAT_CARDS_SPECIAL_VALUE,
  BANKER_DRAW_AFTER_PLAYER_RULES,
} from './baccarat.constants'
import {
  HistoryOutcome,
  RawNonce,
} from 'src/modules/fairness/rotating-seed/rotating-seed.interface'
import { GameCondensed } from './baccarat.interface'

export function* cardIndexGenerator(): Generator<number, never, void> {
  let index = 0
  while (true) {
    yield index++
  }
}

export function drawCards(
  privateSeed: string,
  cardIndex: Generator<number, never, void>,
  cardsToGenerate = 1
): Card[] {
  const cards: Card[] = []
  for (let i = 0; i < cardsToGenerate; i++) {
    const cardSeed = `${privateSeed}:${cardIndex.next().value}`
    const id = Math.round(seedrandom(cardSeed)() * 51)

    const card: Card = [
      RANKS[id % RANKS.length],
      SUITS[Math.floor(id / RANKS.length)],
    ]

    cards.push(card)
  }

  return cards
}

export function calculateHandValue(cards: Card[]): Int {
  const value = cards.reduce(
    (sum, [cardRank]) =>
      sum + (BACCARAT_CARDS_SPECIAL_VALUE[cardRank] ?? cardRank),
    0
  )
  return value % 10
}

export function checkScore(score: Int) {
  const isNatural = score === 8 || score === 9
  const drawCard = score <= 5

  return [isNatural, drawCard]
}

export const getBaccaratOutcomesFromNonce = (
  nonce: RawNonce
): HistoryOutcome[] => {
  const data = nonce.Bet[0].data as object as GameCondensed
  return [
    {
      nonce: nonce.nonce,
      type: 'BACCARAT',
      outcome: {
        bankerHand: data.bc,
        playerHand: data.pc,
      },
      seed: data.s,
    },
  ]
}

export const shouldBankerDrawCard = (
  bankerScore: Int,
  playerThirdCard: Card
) => {
  return BANKER_DRAW_AFTER_PLAYER_RULES.some((rule) => {
    const { PLAYER_THIRD_CARD, BANKER_MAX_SCORE } = rule

    return (
      PLAYER_THIRD_CARD.includes(playerThirdCard[0]) &&
      bankerScore <= BANKER_MAX_SCORE
    )
  })
}
