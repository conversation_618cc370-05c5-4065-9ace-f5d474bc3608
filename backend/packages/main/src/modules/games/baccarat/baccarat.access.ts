import { Injectable } from '@nestjs/common'
import { Prisma } from '@prisma/client'
import { PUBLIC_USER_FIELDS } from 'src/common/constants'
import { PaginatedDto } from 'src/common/dto/paginated.dto'
import { paginate } from 'src/common/paginate'
import { PrismaService } from 'src/modules/prisma/prisma.service'
import { GAME_NAME } from './baccarat.constants'

@Injectable()
export class BaccaratAccess {
  constructor(private readonly prisma: PrismaService) {}

  async getHistory(
    where: Prisma.BetWhereInput,
    paginationOptions: PaginatedDto
  ) {
    where.game = GAME_NAME
    const count = await this.prisma.read.bet.count({ where })
    const data = await this.prisma.read.bet.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        betAmount: true,
        winningAmount: true,
        data: true,
        createdAt: true,
        currency: true,
      },
      ...paginate(paginationOptions),
    })

    return { data, count }
  }

  async getRecentBets(currencies: string[]) {
    return await this.prisma.read.bet.findMany({
      where: { game: GAME_NAME, currency: { in: currencies } },
      select: {
        data: true,
        betAmount: true,
        winningAmount: true,
        currency: true,
        user: {
          select: PUBLIC_USER_FIELDS,
        },
      },
      orderBy: { id: 'desc' },
      take: 10,
    })
  }
}
