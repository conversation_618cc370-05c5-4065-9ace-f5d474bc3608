import { Card, Int } from '@crashgg/types'
import { Injectable } from '@nestjs/common'
import { PlayDto } from './dto/play.dto'
import { assert } from 'src/utils/assert'
import { FairnessService } from 'src/modules/fairness/fairness.service'
import {
  calculateHandValue,
  cardIndexGenerator,
  checkScore,
  drawCards,
  shouldBankerDrawCard,
} from './baccarat.helpers'
import { BaccaratGameResult, GameCondensed } from './baccarat.interface'
import { BET_FIELD_MAPPING, GAME_NAME } from './baccarat.constants'
import {
  BET_OUTCOME_EVENT,
  BetOutcomeEvent,
  getBalance,
  noop,
  onBetFinished,
  PrismaService,
  REAL_CURRENCIES,
} from '@crashgg/common/dist'
import { Bet, User } from '@prisma/client'
import { UserService } from 'src/modules/user/user.service'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { paginated } from 'src/common/paginate'
import { PaginatedDto } from 'src/common/dto/paginated.dto'
import { BaccaratAccess } from './baccarat.access'
import { RainService } from 'src/modules/rain/rain.service'

@Injectable()
export class BaccaratService {
  constructor(
    private readonly fairnessService: FairnessService,
    private readonly userService: UserService,
    private readonly prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
    private readonly baccaratAccess: BaccaratAccess,
    private readonly rainService: RainService
  ) {}

  async play(userId: Int, body: PlayDto, currency: Currency) {
    const validHash =
      !body.serverHash ||
      body.serverHash === (await this.fairnessService.getServerHash(userId))
    assert(validHash, 'server_hash_mismatch')

    const { bet: betData } = body
    const betAmount = Object.values(betData).reduce(
      (acc, value) => acc + (value ?? 0),
      0
    )
    assert(!betData.banker || !betData.player, 'invalid_bet')
    assert(betAmount > 0, 'invalid_bet_amount')

    const { privateSeed, publicSeed, historyId } =
      await this.fairnessService.useRotatingSeed({
        userId,
        clientSeed: body.clientSeed,
      })

    const game = this.playBaccarat(privateSeed)
    const winHand = BET_FIELD_MAPPING[game.result]
    const winAmount = Math.floor(
      (betData[winHand.KEY] ?? 0) * winHand.MULTIPLIER
    )

    const gameCondensed: GameCondensed = {
      pc: game.playerCards,
      bc: game.bankerCards,
      ps: game.playerScore,
      bs: game.bankerScore,
      b: betData,
      w: winAmount,
      s: publicSeed,
      r: game.result,
    }

    const user: User = await this.prisma.$transaction(async (tx) => {
      const { user, bet } = await this.userService.placeBet(tx, {
        amount: betAmount,
        userId,
        ticketsEarned: betAmount,
        game: GAME_NAME,
        transactionData: {
          message: `Played baccarat`,
        },
        betData: {
          winningAmount: winAmount,
          data: gameCondensed as object,
          rotatingSeedHistory: { connect: { id: historyId } },
        },
        currency,
      })

      if (winAmount > 0) {
        const { user: winner } = await this.userService.handleWin(tx, {
          betId: (bet as Bet).id,
          winningAmount: winAmount,
          transactionData: { message: 'baccarat win' },
          currency,
        })

        return winner
      } else {
        await onBetFinished(tx, userId, 0, true, currency)
      }
      return user
    })

    this.eventEmitter.emit(
      BET_OUTCOME_EVENT,
      new BetOutcomeEvent({
        userId,
        betAmount,
        winAmount,
        game: GAME_NAME,
        currency,
        multiplier: winHand.MULTIPLIER,
        context: {
          gameCondensed,
        },
      })
    )

    if (REAL_CURRENCIES.includes(currency)) {
      this.rainService.registerBet(betAmount).catch(noop)
    }

    return {
      ...game,
      winAmount,
      newBalance: getBalance(user, currency),
      publicSeed,
    }
  }

  private playBaccarat(privateSeed: string) {
    const cardIndex = cardIndexGenerator()
    const playerCards: Card[] = drawCards(privateSeed, cardIndex, 2)
    const bankerCards: Card[] = drawCards(privateSeed, cardIndex, 2)

    const playerScore = calculateHandValue(playerCards)
    const bankerScore = calculateHandValue(bankerCards)

    const [playerNatural, playerDraw] = checkScore(playerScore)
    const [bankerNatural, bankerDrawIfPlayerStands] = checkScore(bankerScore)

    if (playerNatural || bankerNatural) {
      return this.getGameResult({
        playerCards,
        playerScore,
        bankerCards,
        bankerScore,
      })
    }

    if (playerDraw) {
      const [thirdCard] = drawCards(privateSeed, cardIndex)
      playerCards.push(thirdCard)

      if (shouldBankerDrawCard(bankerScore, thirdCard)) {
        bankerCards.push(...drawCards(privateSeed, cardIndex))
      }
    }
    if (!playerDraw && bankerDrawIfPlayerStands) {
      bankerCards.push(...drawCards(privateSeed, cardIndex))
    }

    const playerFinalScore = calculateHandValue(playerCards)
    const bankerFinalScore = calculateHandValue(bankerCards)

    return this.getGameResult({
      playerCards,
      playerScore: playerFinalScore,
      bankerCards,
      bankerScore: bankerFinalScore,
    })
  }

  private getGameResult({
    playerCards,
    playerScore,
    bankerCards,
    bankerScore,
  }: {
    playerCards: Card[]
    playerScore: Int
    bankerCards: Card[]
    bankerScore: Int
  }) {
    let result: BaccaratGameResult
    if (playerScore === bankerScore) {
      result = BaccaratGameResult.TIE
    } else if (playerScore > bankerScore) {
      result = BaccaratGameResult.PLAYER_WIN
    } else {
      result = BaccaratGameResult.BANKER_WIN
    }

    return {
      playerCards,
      bankerCards,
      playerScore,
      bankerScore,
      result,
    }
  }

  async getGameHistory(
    userId: Int,
    paginationOptions: PaginatedDto,
    currency?: Currency
  ) {
    paginationOptions.pageSize ??= 100

    const { data, count } = await this.baccaratAccess.getHistory(
      {
        userId,
        currency,
      },
      paginationOptions
    )

    return paginated({ data, count, pageSize: paginationOptions.pageSize })
  }

  async getRecentGames(currencies: Currency[]) {
    const bets = await this.baccaratAccess.getRecentBets(currencies)

    return bets.map((bet) => {
      const data = bet.data as object as GameCondensed
      return {
        avatarUrl: bet.user.avatar,
        userId: bet.user.id,
        betAmount: bet.betAmount,
        winningAmount: bet.winningAmount,
        currency: bet.currency,
        placed: data.b,
        result: {
          state: data.r,
          playerCards: data.pc,
          bankerCards: data.bc,
        },
      }
    })
  }
}
