import { BaccaratGameResult } from './baccarat.interface'

export const GAME_NAME = 'baccarat'
export const BACCARAT_CARDS_SPECIAL_VALUE = {
  A: 1,
  10: 0,
  J: 0,
  Q: 0,
  K: 0,
}

export const BET_FIELD_MAPPING = {
  [BaccaratGameResult.PLAYER_WIN]: {
    KEY: 'player',
    MULTIPLIER: 2,
  },
  [BaccaratGameResult.BANKER_WIN]: {
    KEY: 'banker',
    MULTIPLIER: 2 * 0.95,
  },
  [BaccaratGameResult.TIE]: {
    KEY: 'tie',
    MULTIPLIER: 9,
  },
}

export const BANKER_DRAW_AFTER_PLAYER_RULES = [
  {
    PLAYER_THIRD_CARD: [9, 10, 'J', 'Q', 'K', 'A'],
    BANKER_MAX_SCORE: 3,
  },
  {
    PLAYER_THIRD_CARD: [8],
    BANKER_MAX_SCORE: 2,
  },
  {
    PLAYER_THIRD_CARD: [6, 7],
    BANKER_MAX_SCORE: 6,
  },
  {
    PLAYER_THIRD_CARD: [4, 5],
    BANKER_MAX_SCORE: 5,
  },
  {
    PLAYER_THIRD_CARD: [2, 3],
    BANKER_MAX_SCORE: 4,
  },
]
