import { assert } from 'src/utils/assert'
import { ItemDto } from '../cases/dto/case-admin.dto'
import { isClashCsgo } from '../../config/configuration'

export class SuperSpinService {
  isEligible: boolean
  topItems: string[] = []
  hitSuperSpin = false

  constructor(
    private caseItems: ItemDto[],
    isEnabled: boolean
  ) {
    if (isEnabled) {
      this._findTopItems()
    }
  }

  static getTopItems(items: ItemDto[]) {
    const { hardMaxSum, softMaxSum, minMultiplier } =
      SuperSpinService.configuration()
    const rtp =
      items.reduce((acc, item) => {
        const rarity = item.ticketsEnd - item.ticketsStart + 1
        return acc + rarity * item.price
      }, 0) / 100_000
    const casePrice = rtp * 1.1

    const byPrice = items.sort((a, b) => b.price - a.price)

    const top = []
    let raritySum = 0
    for (const item of byPrice) {
      const rarity = item.ticketsEnd - item.ticketsStart + 1
      if (raritySum >= softMaxSum || raritySum + rarity > hardMaxSum) {
        break
      }

      if (item.price < minMultiplier * casePrice) {
        continue
      }

      top.push(item.name)
      raritySum += rarity
    }

    return top.length < 2 ? [] : top
  }

  static configuration() {
    if (isClashCsgo()) {
      return { hardMaxSum: 15_000, softMaxSum: 10_000, minMultiplier: 2 }
    }

    return { hardMaxSum: 11_000, softMaxSum: 10_000, minMultiplier: 3 }
  }

  private _findTopItems() {
    const top = SuperSpinService.getTopItems(this.caseItems)

    if (top.length === 0) {
      this.isEligible = false
      return
    }

    this.topItems = top
    this.isEligible = true
  }

  check(item: ItemDto) {
    if (!this.isEligible) {
      return false
    }

    assert(item, 'check_super_spin_item_empty')

    const isSuperSpin = this.topItems.includes(item.name)
    if (isSuperSpin) {
      this.hitSuperSpin = true
    }

    return isSuperSpin
  }
}
