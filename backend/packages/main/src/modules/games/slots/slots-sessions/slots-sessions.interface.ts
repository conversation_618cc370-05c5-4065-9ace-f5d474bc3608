import { SlotsProvider } from '@prisma/client'

export interface Options {
  provider: SlotsProvider
  externalSessionId: string
  externalActionId: string
  userId: Int
  roundId: string
  currency?: Currency
  // transactionId some providers use same id for all actions in a round
  txId?: string
  fullData?: object
}

export interface CreateOptions extends Omit<Options, 'roundId' | 'txId'> {
  asyncLock?: boolean
}

export type CloseOptions = Omit<Options, 'currency' | 'txId'>

export interface BetOptions extends Options {
  amount: Int
}

export enum SessionErrors {
  UNKNOWN_ERROR,
  PLAYER_NOT_FOUND,
  TRANSACTION_NOT_FOUND,
  INVALID_SESSION,
  INSUFFICIENT_BALANCE,
}

export class SessionError extends Error {
  code: SessionErrors
  source = 'slots-sessions'
  constructor(code: SessionErrors = SessionErrors.UNKNOWN_ERROR) {
    super('Session error')
    this.code = code
  }
}
