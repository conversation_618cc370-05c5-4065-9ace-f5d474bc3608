import { Injectable } from '@nestjs/common'
import { CaseItem } from '../cases.interface'

@Injectable()
export class RiskFactorService {
  private readonly MAX_TICKETS = 100_000

  private getItemChance(item: CaseItem) {
    const tickets = item.ticketsEnd + 1 - item.ticketsStart
    const chance = tickets / this.MAX_TICKETS
    return chance
  }

  private getGiniCoefficient(items: CaseItem[]) {
    const ev = items.reduce(
      (sum, item) => sum + item.price * this.getItemChance(item),
      0
    )
    let giniSum = 0
    for (const item1 of items) {
      for (const item2 of items) {
        giniSum +=
          Math.abs(item1.price - item2.price) *
          this.getItemChance(item1) *
          this.getItemChance(item2)
      }
    }
    const gini = giniSum / (2 * ev)
    return gini
  }

  private getLoseChance(price: number, items: CaseItem[]) {
    const chanceSum = items.reduce((acc, item) => {
      if (item.price < price) {
        const chance = this.getItemChance(item)
        acc += chance
      }
      return acc
    }, 0)
    return chanceSum
  }

  getRiskFactor(price: number, items: CaseItem[]) {
    const gini = this.getGiniCoefficient(items)
    const loseChance = this.getLoseChance(price, items)
    return gini * loseChance
  }
}
