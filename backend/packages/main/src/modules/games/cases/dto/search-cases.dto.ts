import { Type } from 'class-transformer'
import {
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  Length,
  Max,
  Min,
} from 'class-validator'
import { PaginatedDto } from 'src/common/dto/paginated.dto'
import { SortOrder } from 'src/common/types/sort'

export enum SortBy {
  Popularity = 'popularity',
  Likes = 'likes',
  Date = 'date',
  Opens = 'opens',
  Price = 'price',
}

export class SearchCasesDto extends PaginatedDto {
  @IsOptional()
  @IsEnum(SortBy)
  sortBy?: SortBy

  @IsOptional()
  @IsEnum(SortOrder)
  sortOrder?: SortOrder

  @IsOptional()
  @IsString()
  @Length(1, 32)
  search?: string

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(200_000_00)
  minPrice?: Int

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(200_000_00)
  maxPrice?: Int

  @IsOptional()
  @Type(() => Number)
  @Min(0)
  @Max(1)
  minRisk?: Float

  @IsOptional()
  @Type(() => Number)
  @Min(0)
  @Max(1)
  maxRisk?: number
}
