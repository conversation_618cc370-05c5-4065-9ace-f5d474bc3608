import { Transform } from 'class-transformer'
import {
  IsArray,
  IsBooleanString,
  IsInt,
  IsOptional,
  IsString,
  Length,
} from 'class-validator'
import { transformIntArray } from 'src/common/utilities'

export class SearchOfficialCasesDto {
  @IsOptional()
  @Transform(transformIntArray)
  @IsArray()
  @IsInt({ each: true })
  mandatoryOneOfTags?: Int[]

  @IsOptional()
  @IsString()
  @Length(1, 32)
  search?: string

  @IsOptional()
  @IsBooleanString()
  onlySearchName?: string

  @IsOptional()
  @IsString()
  type?: string

  @IsOptional()
  @IsBooleanString()
  fresh?: string
}
