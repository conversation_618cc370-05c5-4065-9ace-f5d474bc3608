import { Type } from 'class-transformer'
import {
  ArrayNotEmpty,
  IsArray,
  IsInt,
  IsOptional,
  IsPositive,
  IsString,
  IsUrl,
  Max,
  Min,
  ValidateNested,
} from 'class-validator'

export class CaseProductsDto {
  @IsOptional()
  @IsPositive()
  id?: Int

  @IsString()
  name: string

  @IsUrl()
  image: string

  @IsString()
  type: string

  @IsOptional()
  @IsInt()
  levelRequired?: Int

  @IsArray()
  @IsString({ each: true })
  categories: string[]

  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => ProductItemDto)
  items: ProductItemDto[]
}

export class ProductItemDto {
  @IsInt()
  productId: Int

  @IsInt()
  @Min(0)
  @Max(99_999)
  ticketsStart: Int

  @IsInt()
  @Min(0)
  @Max(99_999)
  ticketsEnd: Int

  // This is used internally and generated on the fly
  @IsOptional()
  isReward?: boolean
}
