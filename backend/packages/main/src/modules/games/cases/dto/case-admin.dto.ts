import { Int } from '@crashgg/types/dist'
import { Type } from 'class-transformer'
import {
  ArrayNotEmpty,
  IsArray,
  IsInt,
  IsOptional,
  IsString,
  IsUrl,
  Max,
  Min,
  ValidateNested,
} from 'class-validator'

export class CaseAdminDto {
  @IsString()
  name: string

  @IsUrl()
  image: string

  @IsInt()
  @Min(0)
  price: Int

  @IsString()
  type: string

  @IsOptional()
  @IsInt()
  levelRequired?: Int

  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => ItemDto)
  items: ItemDto[]
}

export class ItemDto {
  @IsInt()
  price: Int

  @IsString()
  name: string

  @IsUrl()
  image: string

  @IsInt()
  @IsOptional()
  productId?: Int

  @IsInt()
  @Min(0)
  @Max(100_000)
  ticketsStart: Int

  @IsInt()
  @Min(0)
  @Max(100_000)
  ticketsEnd: Int

  // This is used internally and generated on the fly
  @IsOptional()
  isReward?: boolean
}
