import { Transform } from 'class-transformer'
import { IsIn, IsString, Length } from 'class-validator'
import { IsCensuralString } from 'src/common/decorators/is-censural-string.decorator'
import { transformTrim } from 'src/common/utilities'

const STANDARD_IMAGES = new Array(20)
  .fill(0)
  .map((_, i) => String(i).padStart(2, '0'))
const COMMUNITY_IMAGES = [
  ...STANDARD_IMAGES,
  'halloween01',
  'halloween02',
  'halloween03',
  'xmas01',
  'xmas02',
  'xmas03',
]

export class ModifiableCaseDto {
  @IsString()
  @Length(1, 64)
  @Transform(transformTrim)
  @IsCensuralString('case')
  name: string

  @IsString()
  @IsIn(COMMUNITY_IMAGES)
  image: string
}
