import { Module } from '@nestjs/common'
import { BombRunService } from './bomb-run.service'
import { BombRunController } from './bomb-run.controller'
import { UserModule } from 'src/modules/user/user.module'
import { RainModule } from 'src/modules/rain/rain.module'
import { FairnessModule } from 'src/modules/fairness/fairness.module'

@Module({
  imports: [UserModule, RainModule, FairnessModule],
  providers: [BombRunService],
  controllers: [BombRunController],
})
export class BombRunModule {}
