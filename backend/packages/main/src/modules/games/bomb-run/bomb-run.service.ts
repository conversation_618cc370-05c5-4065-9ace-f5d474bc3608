import { Injectable } from '@nestjs/common'
import { Bet, BombRun, Prisma, SingleGameStatus } from '@prisma/client'
import { PrismaService } from 'src/modules/prisma/prisma.service'
import { UserService } from 'src/modules/user/user.service'
import { CreateDto } from './dto/create.dto'
import { assert } from 'src/utils/assert'
import { Currency } from '@crashgg/types/dist'
import {
  BET_OUTCOME_EVENT,
  BetOutcomeEvent,
  getBalance,
} from '@crashgg/common/dist'
import { RainService } from 'src/modules/rain/rain.service'
import { CreateOptions } from './bomb-run.types'
import { PublicGame } from './public-game.entity'
import seedrandom from 'seedrandom'
import { ALL_STEPS as STEPS, BOMB_COUNT } from './bomb-run.constants'
import { SUCCESS } from 'src/common/constants'
import { plainToInstance } from 'class-transformer'
import { ProfileStatsService } from 'src/modules/user/profile-stats/profile-stats.service'
import {
  highestWin,
  timesPlayed,
} from 'src/modules/user/profile-stats/profile-stats.helpers'
import { StatCategory } from 'src/modules/user/profile-stats/profile-stats.types'
import { PaginatedDto } from 'src/common/dto/paginated.dto'
import { paginate, paginated } from 'src/common/paginate'
import { EventEmitter2 } from '@nestjs/event-emitter'

@Injectable()
export class BombRunService {
  static GAME = 'bomb-run'
  static HOUSE_EDGE = 0.1

  constructor(
    private readonly prisma: PrismaService,
    private readonly userService: UserService,
    private readonly profileStatsService: ProfileStatsService,
    private readonly rainService: RainService,
    private readonly eventEmitter: EventEmitter2
  ) {}

  static WITH_AVATAR = {
    user: {
      select: { avatar: true },
    },
  }

  async getLatest() {
    const games = await this.prisma.bombRun.findMany({
      where: {
        status: { in: [SingleGameStatus.WIN, SingleGameStatus.LOSE] },
      },
      orderBy: { id: 'desc' },
      include: BombRunService.WITH_AVATAR,
      take: 10,
    })

    return games.map((game) => {
      return plainToInstance(PublicGame, game)
    })
  }

  async getUserHistory(userId: Int) {
    const games = await this.prisma.read.bombRun.findMany({
      where: {
        userId,
        status: { in: [SingleGameStatus.WIN, SingleGameStatus.LOSE] },
      },
      orderBy: { id: 'desc' },
      take: 500,
    })

    return games
  }

  async getGameHistory(
    userId: Int,
    paginationOptions: PaginatedDto,
    currency?: Currency
  ) {
    paginationOptions.pageSize ??= 100

    const where: Prisma.BombRunWhereInput = {
      userId,
      status: { in: [SingleGameStatus.WIN, SingleGameStatus.LOSE] },
      currency,
    }

    const count = await this.prisma.read.bombRun.count({ where })
    const data = await this.prisma.read.bombRun.findMany({
      where,
      orderBy: { id: 'desc' },
      ...paginate(paginationOptions),
    })

    return paginated({ data, count, pageSize: paginationOptions.pageSize })
  }

  async getCurrent(userId: Int): Promise<BombRun | null> {
    const game = await this.prisma.bombRun.findFirst({
      where: {
        userId,
        status: SingleGameStatus.IN_PROGRESS,
      },
    })
    return game
  }

  async byId(gameId: Int, userId: Int) {
    const game = await this.prisma.bombRun.findUnique({
      where: { userId, id: gameId },
    })
    assert(game, 'game_not_found')
    assert(game.status === SingleGameStatus.IN_PROGRESS, 'game_not_found')

    return game
  }

  async create(
    userId: Int,
    body: CreateDto,
    serverSeed: string,
    currency: Currency
  ) {
    const current = await this.getCurrent(userId)
    assert(!current, 'game_in_progress')

    const { betAmount, riskLevel, clientSeed } = body

    return this.prisma.$transaction(async (tx) => {
      const { user, bet } = await this.userService.placeBet(tx, {
        userId,
        amount: betAmount,
        game: BombRunService.GAME,
        transactionData: { message: `Bomb run game riskLevel=${riskLevel}` },
        currency,
        isAsync: true,
      })

      await this.profileStatsService.updateField(
        timesPlayed({ userId, category: StatCategory.BombRun, prisma: tx })
      )

      this.rainService.registerBet(betAmount)
      const betId = (bet as Bet).id

      const game = await this.createGame({
        userId,
        clientSeed,
        riskLevel,
        betAmount,
        serverSeed,
        currency,
        betId,
      })

      await tx.bet.update({
        where: { id: betId },
        data: { bombRunId: game.id },
      })

      return {
        game: new PublicGame(game),
        newBalance: getBalance(user, currency),
      }
    })
  }

  private async createGame(options: CreateOptions) {
    const {
      userId,
      clientSeed,
      riskLevel,
      betAmount,
      serverSeed,
      currency,
      betId,
    } = options
    const bombCount = BOMB_COUNT[riskLevel]
    const bombs = this.generateBombs(serverSeed, clientSeed, bombCount)

    const game = await this.prisma.bombRun.create({
      data: {
        user: {
          connect: { id: userId },
        },
        riskLevel,
        betAmount,
        status: SingleGameStatus.IN_PROGRESS,
        step: 0,
        clientSeed,
        serverSeed,
        bombs,
        currency,
        betId,
      },
    })

    return game
  }

  private generateBombs(
    serverSeed: string,
    clientSeed: string,
    bombCount: Int
  ) {
    const seed = [serverSeed, clientSeed].join(':')
    const random = seedrandom(seed)

    const bombs = new Set<Int>()
    while (bombs.size !== bombCount) {
      bombs.add(Math.ceil(random() * STEPS))
    }

    return [...bombs].sort((a, b) => a - b)
  }

  async step(userId: Int, gameId: Int) {
    const game = await this.byId(gameId, userId)
    const step = game.step + 1
    const isLost = step === game.bombs[0]

    const newGame = await this.prisma.bombRun.update({
      where: { id: gameId },
      data: {
        step: { increment: 1 },
        status: isLost ? SingleGameStatus.LOSE : SingleGameStatus.IN_PROGRESS,
      },
      include: BombRunService.WITH_AVATAR,
    })

    let payout = 0
    let nextPayout = 0
    if (!isLost) {
      payout = this.getPayout(step, game.bombs.length, game.betAmount)
      nextPayout = this.getPayout(step + 1, game.bombs.length, game.betAmount)
    } else {
      this.sendUpdate(newGame)
    }

    return {
      game: new PublicGame(newGame),
      payout,
      nextPayout,
    }
  }

  private getPayoutRate(step: Int, bombs: Int): Int {
    if (step === 0) return 0

    let rate = 1
    for (let i = 0; i < step; i++) {
      const remainingTiles = STEPS - i
      const winProbability = (remainingTiles - bombs) / remainingTiles
      rate *= 1 / winProbability
    }

    const houseEdgeMultiplier = 1 - BombRunService.HOUSE_EDGE
    rate *= houseEdgeMultiplier

    const roundTo = 1e3
    return Math.round(rate * roundTo) / roundTo
  }

  private getPayout(step: Int, bombs: Int, amount: Int) {
    const rate = this.getPayoutRate(step, bombs)
    const uncapped = Math.floor(rate * amount)

    return Math.max(Math.min(uncapped, 150_000_00), 0)
  }

  async cashout(userId: Int, gameId: Int) {
    const game = await this.byId(gameId, userId)
    assert(game.step > 0, 'cannot_cashout')

    const winAmount = this.getPayout(
      game.step,
      game.bombs.length,
      game.betAmount
    )

    const { user, newGame } = await this.prisma.$transaction(async (tx) => {
      const newGame = await tx.bombRun.update({
        where: { id: game.id },
        data: {
          status: SingleGameStatus.WIN,
          winAmount,
        },
        include: BombRunService.WITH_AVATAR,
      })

      const { user } = await this.userService.handleWin(tx, {
        betId: game.betId,
        winningAmount: winAmount,
        transactionData: { message: 'Bomb run win' },
        currency: game.currency as Currency,
        isAsync: true,
      })

      await this.profileStatsService.updateField(
        highestWin({
          userId,
          category: StatCategory.BombRun,
          value: winAmount,
          prisma: tx,
        })
      )

      return { user, newGame }
    })
    this.sendUpdate(newGame)

    return {
      ...SUCCESS,
      newBalance: getBalance(user, game.currency as Currency),
    }
  }

  private emitBetOutcome(game: BombRun) {
    this.eventEmitter.emit(
      BET_OUTCOME_EVENT,
      new BetOutcomeEvent({
        userId: game.userId,
        betAmount: game.betAmount,
        winAmount: game.winAmount,
        game: BombRunService.GAME,
        currency: game.currency as Currency,
        context: {
          step: game.step,
          bombs: game.bombs,
          riskLevel: game.riskLevel,
        },
      })
    )
  }

  private sendUpdate(game: BombRun) {
    this.emitBetOutcome(game)
  }
}
