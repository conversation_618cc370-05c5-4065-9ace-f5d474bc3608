import { $Enums, BombRun } from '@prisma/client'
import { Exclude } from 'class-transformer'

export class <PERSON>Game implements BombRun {
  id: number
  @Exclude() userId: number
  @Exclude() betId: number
  riskLevel: string
  status: $Enums.SingleGameStatus
  step: number
  @Exclude() bombs: number[]
  @Exclude() clientSeed: string
  @Exclude() serverSeed: string
  currency: string
  createdAt: Date
  updatedAt: Date
  betAmount: number
  winAmount: number
  user: any

  constructor(partial: Partial<BombRun>) {
    Object.assign(this, partial)
  }
}
