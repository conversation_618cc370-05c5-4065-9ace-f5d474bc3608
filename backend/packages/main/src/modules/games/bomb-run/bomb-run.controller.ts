import {
  Body,
  Controller,
  Get,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common'
import { BombRunService } from './bomb-run.service'
import { LockMode, RedisService } from 'src/modules/redis/redis.service'
import { FairnessService } from 'src/modules/fairness/fairness.service'
import { UserId } from 'src/common/user-id.decorator'
import { ActionDto } from './dto/action.dto'
import { AccessGuard } from 'src/modules/auth/guards/access.guard'
import { BanGuard } from 'src/modules/auth/guards/ban.guard'
import { PublicGame } from './public-game.entity'
import { CreateDto } from './dto/create.dto'
import { CurrencyUsed, QueryCurrency } from 'src/common/currency.decorator'
import { Currency } from '@crashgg/types/dist'
import { assert } from 'src/utils/assert'
import { PaginatedDto } from 'src/common/dto/paginated.dto'

@Controller('bomb-run')
export class BombRunController {
  constructor(
    private bombRunService: BombRunService,
    private redis: RedisService,
    private fairnessService: FairnessService
  ) {}

  @Get('latest')
  async getLatest() {
    return this.bombRunService.getLatest()
  }

  @Get('my-history')
  @UseGuards(AccessGuard, BanGuard)
  async getMyHistory(@UserId() userId: Int) {
    return this.bombRunService.getUserHistory(userId)
  }

  @Get('game')
  @UseGuards(AccessGuard, BanGuard)
  async getGame(@UserId() userId: Int) {
    const current = await this.bombRunService.getCurrent(userId)

    const game = current ? new PublicGame(current) : null

    return { game }
  }

  @Get('game-history')
  @UseGuards(AccessGuard, BanGuard)
  async getGameHistory(
    @UserId() userId: Int,
    @Query() paginationOptions: PaginatedDto,
    @QueryCurrency() currency?: Currency
  ) {
    return await this.bombRunService.getGameHistory(
      userId,
      paginationOptions,
      currency
    )
  }

  @Post('game')
  @UseGuards(AccessGuard, BanGuard)
  async create(
    @UserId() userId: Int,
    @Body() body: CreateDto,
    @CurrencyUsed([Currency.REAL, Currency.CCY])
    currency: Currency
  ) {
    const lock = await this.redis.lock([
      `charge:${userId}`,
      `fairness:seed:${userId}`,
      `bomb-run:${userId}`,
    ])
    try {
      const serverSeed = await this.fairnessService.getServerSeed(userId)

      const validHash =
        !body.serverHash ||
        body.serverHash === this.fairnessService.hash(serverSeed)
      assert(validHash, 'server_hash_mismatch')

      const game = await this.bombRunService.create(
        userId,
        body,
        serverSeed,
        currency
      )

      const newServerHash = await this.fairnessService.createServerSeed(
        userId,
        true
      )

      return { ...game, newServerHash }
    } finally {
      lock.release()
    }
  }

  @Patch('game/step')
  @UseGuards(AccessGuard, BanGuard)
  async step(@UserId() userId: Int, @Body() body: ActionDto) {
    const lock = await this.redis.lock([`bomb-run:${userId}`], LockMode.Fast)

    return await this.bombRunService
      .step(userId, body.gameId)
      .finally(lock.release)
  }

  @Patch('game/cashout')
  @UseGuards(AccessGuard, BanGuard)
  async cashout(@UserId() userId: Int, @Body() body: ActionDto) {
    const lock = await this.redis.lock([`bomb-run:${userId}`], LockMode.Fast)

    return await this.bombRunService
      .cashout(userId, body.gameId)
      .finally(lock.release)
  }
}
