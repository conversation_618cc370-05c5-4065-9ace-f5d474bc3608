import { IsEnum, IsInt, IsOptional, IsString, <PERSON>, <PERSON> } from 'class-validator'
import { RiskLevel } from '../bomb-run.types'
import { ClientSeedDto } from 'src/common/dto/client-seed.dto'

export class CreateDto extends ClientSeedDto {
  @IsInt()
  @Min(10)
  @Max(10_000_00)
  betAmount: Int

  @IsEnum(RiskLevel)
  riskLevel: RiskLevel

  @IsOptional()
  @IsString()
  serverHash?: string
}
