import { BjState } from '@crashgg/types/dist'
import {
  HistoryOutcome,
  RawNonce,
} from '../../fairness/rotating-seed/rotating-seed.interface'

export const getBlackjackOutcomesFromNonce = (
  nonce: RawNonce
): HistoryOutcome[] => {
  const bet = nonce.Bet[0]
  const state = bet.blackjack.state as any as BjState

  return [
    {
      nonce: nonce.nonce,
      type: 'BLACKJACK',
      outcome: {
        dealerHand: state.dealerHand,
        playerHands: state.playerHands.map((hand) => hand.cards),
      },
      seed: state.publicSeed,
    },
  ]
}
