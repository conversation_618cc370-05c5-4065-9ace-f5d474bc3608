import { Int } from '@crashgg/types/dist'
import {
  IsOptional,
  IsString,
  Length,
  registerDecorator,
  ValidationOptions,
} from 'class-validator'
import { ClientSeedDto } from 'src/common/dto/client-seed.dto'

function IsIntOrIntArray(
  validationOptions?: ValidationOptions & { min: Int; max: Int }
) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: 'IsIntOrIntArray',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate(value: any) {
          const { min, max } = validationOptions
          const isValidNumber = (v: any) => {
            return Number.isInteger(v) && v >= min && v <= max
          }

          if (typeof value === 'number') return isValidNumber(value)
          if (Array.isArray(value) && value.length > 0) {
            return value.every((v) => isValidNumber(v))
          }
          return false
        },
        defaultMessage() {
          const { min, max } = validationOptions
          return `Value must be an integer or an array of integers with a value between ${min} and ${max}`
        },
      },
    })
  }
}

export class CreateDto extends ClientSeedDto {
  @IsIntOrIntArray({ min: 10, max: 500_00 })
  amount: Int | Int[]

  @IsOptional()
  @IsString()
  @Length(64, 64)
  serverHash: string
}
