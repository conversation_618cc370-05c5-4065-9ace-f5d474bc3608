import { Currency, Int } from '@crashgg/types/dist'
import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common'
import { UserId } from 'src/common/user-id.decorator'
import { assert } from 'src/utils/assert'
import { AccessGuard } from '../../auth/guards/access.guard'
import { BanGuard } from '../../auth/guards/ban.guard'
import { FairnessService } from '../../fairness/fairness.service'
import { RedisService } from '../../redis/redis.service'
import { BlackjackService } from './blackjack.service'
import { CreateDto } from './dto/create.dto'
import { PlayDto } from './dto/play.dto'
import {
  CurrencyUsed,
  QueryCurrency,
  VaryCurrency,
} from 'src/common/currency.decorator'
import { PaginatedDto } from 'src/common/dto/paginated.dto'

@Controller('blackjack')
export class BlackjackController {
  constructor(
    private blackjackService: BlackjackService,
    private fairnessService: FairnessService,
    private redis: RedisService
  ) {}

  @Get('history')
  @VaryCurrency()
  async getHistory(
    @CurrencyUsed([Currency.REAL, Currency.PLAY])
    currency: Currency
  ) {
    return this.blackjackService.getHistory(currency)
  }

  @Get('game-history')
  @UseGuards(AccessGuard)
  @VaryCurrency()
  async getGameHistory(
    @UserId() userId: Int,
    @Query() paginationOptions: PaginatedDto,
    @QueryCurrency() currency?: Currency
  ) {
    return this.blackjackService.getGameHistory(
      userId,
      paginationOptions,
      currency
    )
  }

  @Get('current')
  @UseGuards(AccessGuard, BanGuard)
  async getCurrent(@UserId() userId: Int) {
    return this.blackjackService.getCurrentPublic(userId)
  }

  @Post('create')
  @UseGuards(AccessGuard, BanGuard)
  async create(
    @UserId() userId: Int,
    @Body() body: CreateDto,
    @CurrencyUsed([Currency.REAL, Currency.PLAY])
    currency: Currency
  ) {
    const lock = await this.redis.lock([
      `charge:${userId}`,
      `fairness:seed:${userId}`,
    ])
    try {
      if (body.serverHash && !this.fairnessService.isRotatingSeed) {
        const serverHash = await this.fairnessService.getServerHash(userId)
        const isValidHash = body.serverHash === serverHash
        assert(isValidHash, 'server_hash_mismatch')
      }

      const game = await this.blackjackService.create(userId, body, currency)

      const newServerHash = await this.fairnessService.createServerSeed(
        userId,
        true
      )

      return { ...game, newServerHash }
    } finally {
      lock.release()
    }
  }

  @Post('play')
  @UseGuards(AccessGuard, BanGuard)
  async play(@UserId() userId: Int, @Body() body: PlayDto) {
    const lock = await this.redis.lock([
      `charge:${userId}`,
      `blackjack:play:${userId}`,
    ])

    return await this.blackjackService.play(userId, body).finally(lock.release)
  }
}
