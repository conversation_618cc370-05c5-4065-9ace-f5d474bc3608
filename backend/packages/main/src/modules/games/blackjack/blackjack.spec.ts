import assert from 'assert'
import { HandAction, HandStatus } from '@crashgg/types/dist'
import { Blackjack } from './blackjack.class'

const SEEDS = {
  PLAYER_HIT_WIN: 'hi',
  DEALER_A: 'hi',
  DEALER_BJ: '229',
  TWO_BJ: '92',
  STAND_DEALER_BUST: 'aaa',
  SPLITTABLE: '10',
  DEALER_A_WITH_FIRST_PLAYER_BJ: '269',
  ACES_TO_SPLIT: '552',
}

const publicSeed = 'x'

describe('blackjack', () => {
  it('draws cards', () => {
    const bj = new Blackjack()
    bj.start('test', publicSeed)

    assert.equal(bj.state.nextCardId, 5)
  })

  it('hits', () => {
    const bj = new Blackjack()
    bj.start(SEEDS.PLAYER_HIT_WIN, publicSeed)
    bj.play(0, HandAction.NoInsurance)
    bj.play(0, HandAction.Hit)

    assert.equal(bj.state.nextCardId, 6)
    assert(!bj.state.playerHands[0].in)
    assert.equal(bj.state.playerHands[0].cards.length, 3)
    assert.equal(bj.state.playerHands[0].status, HandStatus.Win)
  })

  it('stands', () => {
    const bj = new Blackjack()
    bj.start(SEEDS.STAND_DEALER_BUST, publicSeed)
    bj.play(0, HandAction.Stand)

    assert(!bj.state.playerHands[0].in)
    assert.equal(bj.state.playerHands[0].cards.length, 2)
    assert.equal(bj.state.playerHands[0].status, HandStatus.Win)
  })

  it('insta-ties on 2 bjs', () => {
    const bj = new Blackjack()
    bj.start(SEEDS.TWO_BJ, publicSeed, [100])

    expect(bj.state.playerHands[0].status).toBe(HandStatus.Tie)
    expect(bj.balanceDiff.bet).toBe(100)
    expect(bj.balanceDiff.win).toBe(100)
  })

  it('doubles down', () => {
    const bj = new Blackjack()
    bj.start(SEEDS.PLAYER_HIT_WIN, publicSeed, [100])
    bj.play(0, HandAction.NoInsurance)
    bj.play(0, HandAction.Double)

    expect(bj.state.playerHands[0].status).toBe(HandStatus.Win)
    expect(bj.balanceDiff.bet).toBe(200)
    expect(bj.balanceDiff.win).toBe(400)
  })

  it('takes insurance', () => {
    {
      const bj = new Blackjack()
      bj.start(SEEDS.DEALER_A, publicSeed, [100])
      bj.play(0, HandAction.Insurance)
      bj.play(0, HandAction.Stand) // Lose
      expect(bj.balanceDiff.win).toBe(0)
    }

    {
      const bj = new Blackjack()
      bj.start(SEEDS.DEALER_BJ, publicSeed, [100])
      bj.play(0, HandAction.Insurance)
      expect(bj.state.playerHands[0].status).toBe(HandStatus.Lose)
      expect(bj.balanceDiff.bet).toBe(150)
      expect(bj.balanceDiff.win).toBe(150)
    }
  })

  it('does not take insurance', () => {
    const bj = new Blackjack()
    bj.start(SEEDS.DEALER_BJ, publicSeed, [100])
    bj.play(0, HandAction.NoInsurance)

    expect(bj.state.playerHands[0].in).toBe(false)
    expect(bj.state.playerHands[0].status).toBe(HandStatus.Lose)
    expect(bj.balanceDiff.win).toBe(0)
  })

  it('splits', () => {
    const bj = new Blackjack()
    bj.start(SEEDS.SPLITTABLE, publicSeed)

    const cards = bj.state.playerHands[0].cards

    bj.play(0, HandAction.Split)

    assert.equal(bj.state.playerHands.length, 2)
    expect(bj.state.playerHands[0].cards[0]).toEqual(cards[0])
    expect(bj.state.playerHands[0].cards[1]).not.toEqual(cards[1])
    expect(bj.state.playerHands[1].cards[0]).toEqual(cards[1])
  })

  it('splits, waits for all hands before determining winner', () => {
    const bj = new Blackjack()
    bj.start(SEEDS.SPLITTABLE, publicSeed)

    bj.play(0, HandAction.Split)
    bj.play(0, HandAction.Stand)

    expect(
      bj.state.playerHands.every((h) => h.status === HandStatus.InProgress)
    ).toBeTruthy()

    bj.play(1, HandAction.Stand)
    expect(
      bj.state.playerHands.every((h) => h.status !== HandStatus.InProgress)
    ).toBeTruthy()
  })

  it('properly charge split games', () => {
    const bj = new Blackjack()
    bj.start(SEEDS.SPLITTABLE, publicSeed, [100])
    expect(bj.balanceDiff.bet).toBe(100)
    bj.play(0, HandAction.Split)
    expect(bj.balanceDiff.bet).toBe(200)
    bj.play(0, HandAction.Stand)
    bj.play(1, HandAction.Hit) // Tie
    expect(bj.balanceDiff.win).toBe(100)
  })

  it("does not reveal dealer's card", () => {
    const bj = new Blackjack()
    bj.start(SEEDS.STAND_DEALER_BUST, publicSeed)
    expect(bj.public.dealerHand.length).toBe(2)
    expect(bj.public.dealerHand[1]).toEqual([-1])
    bj.play(0, HandAction.Stand)
    expect(bj.public.playerHands[0].status).toBe(HandStatus.Win)
    expect(bj.public.dealerHand[1]).not.toEqual([-1])
  })

  it('resumes', () => {
    const bj = new Blackjack()
    bj.start(SEEDS.SPLITTABLE, publicSeed)
    bj.play(0, HandAction.Split)

    const bj2 = new Blackjack()
    bj2.resume(bj.state)

    expect(bj2.state).toEqual(bj.state)
    expect(bj2.public).toEqual(bj.public)
  })

  it('start with 3 hands', () => {
    const bj = new Blackjack()
    bj.start(SEEDS.SPLITTABLE, publicSeed, [100, 200, 300])

    expect(bj.state.playerHands.length).toBe(3)
    expect(bj.balanceDiff.bet).toBe(600)
    expect(bj.balanceDiff.win).toBe(0)
    expect(bj.state.playerHands[0].stake).toBe(100)
    expect(bj.state.playerHands[1].stake).toBe(200)
    expect(bj.state.playerHands[2].stake).toBe(300)
    expect(bj.state.playerHands[0].in).toBe(true)
    expect(bj.state.playerHands[1].in).toBe(true)
    expect(bj.state.playerHands[2].in).toBe(true)
  })

  it('3 hands instant tie in one hand', () => {
    const bj = new Blackjack()
    bj.start(SEEDS.DEALER_BJ, publicSeed, [100, 200, 300])

    expect(bj.state.playerHands.length).toBe(3)

    expect(bj.state.playerHands[0].in).toBe(false)
    expect(bj.state.playerHands[0].status).toBe(HandStatus.Lose)

    expect(bj.state.playerHands[1].in).toBe(false)
    expect(bj.state.playerHands[1].status).toBe(HandStatus.Tie)

    expect(bj.state.playerHands[2].in).toBe(false)
    expect(bj.state.playerHands[2].status).toBe(HandStatus.Lose)

    expect(bj.balanceDiff.bet).toBe(600)
    expect(bj.balanceDiff.win).toBe(200)
  })

  it('start 3 hands split one hand', () => {
    const bj = new Blackjack()
    bj.start(SEEDS.SPLITTABLE, publicSeed, [100, 200, 300])

    expect(bj.state.playerHands.length).toBe(3)

    expect(bj.state.playerHands[0].in).toBe(true)
    expect(bj.public.playerHands[0].canSplit).toBe(true)

    expect(bj.state.playerHands[1].in).toBe(true)
    expect(bj.public.playerHands[1].canSplit).toBe(false)

    expect(bj.state.playerHands[2].in).toBe(true)
    expect(bj.public.playerHands[2].canSplit).toBe(false)

    expect(bj.balanceDiff.bet).toBe(600)

    const firstHandCards = bj.state.playerHands[0].cards
    const secondHandCards = bj.state.playerHands[1].cards
    const thirdHandCards = bj.state.playerHands[2].cards

    bj.play(0, HandAction.Split)
    expect(bj.state.playerHands.length).toBe(4)

    expect(bj.state.playerHands[0].in).toBe(true)
    expect(bj.public.playerHands[0].cards[0]).toBe(firstHandCards[0])

    expect(bj.state.playerHands[1].in).toBe(true)
    expect(bj.public.playerHands[1].cards[0]).toBe(firstHandCards[1])

    expect(bj.state.playerHands[2].in).toBe(true)
    expect(bj.public.playerHands[2].cards).toBe(secondHandCards)

    expect(bj.state.playerHands[3].in).toBe(true)
    expect(bj.public.playerHands[3].cards).toBe(thirdHandCards)
  })

  describe('3 hands insurance', () => {
    const bj = new Blackjack()
    it('start game', () => {
      bj.start(SEEDS.DEALER_A, publicSeed, [100, 200, 300])

      expect(bj.state.playerHands.length).toBe(3)
      expect(bj.state.playerHands[0].in).toBe(true)
      expect(bj.state.playerHands[1].in).toBe(true)
      expect(bj.state.playerHands[2].in).toBe(true)
    })

    it('take insurance on first hand', () => {
      bj.play(0, HandAction.Insurance)

      expect(bj.state.playerHands[0].in).toBe(true)
      expect(bj.state.playerHands[0].insuranceDecided).toBe(true)
      expect(bj.state.playerHands[0].withInsurance).toBe(true)

      expect(bj.state.playerHands[1].in).toBe(true)
      expect(bj.state.playerHands[1].insuranceDecided).toBe(false)
      expect(bj.state.playerHands[1].withInsurance).toBe(false)

      expect(bj.state.playerHands[2].in).toBe(true)
      expect(bj.state.playerHands[2].insuranceDecided).toBe(false)
      expect(bj.state.playerHands[2].withInsurance).toBe(false)
    })

    it('can not hit first hand', () => {
      expect(() => bj.play(0, HandAction.Hit)).toThrow('bad_request')
    })

    it('can not stand first hand', () => {
      expect(() => bj.play(0, HandAction.Stand)).toThrow('bad_request')
    })

    it('decide no insurance on second hand', () => {
      bj.play(1, HandAction.NoInsurance)

      expect(bj.state.playerHands[1].insuranceDecided).toBe(true)
      expect(bj.state.playerHands[1].withInsurance).toBe(false)
    })

    it('insurance last hand', () => {
      bj.play(2, HandAction.Insurance)

      expect(bj.state.playerHands[0].insuranceDecided).toBe(true)
      expect(bj.state.playerHands[0].withInsurance).toBe(false)

      expect(bj.state.playerHands[1].insuranceDecided).toBe(true)
      expect(bj.state.playerHands[1].withInsurance).toBe(false)

      expect(bj.state.playerHands[2].insuranceDecided).toBe(true)
      expect(bj.state.playerHands[2].withInsurance).toBe(false)

      expect(bj.public.dealerHand[1]).toEqual([-1])
    })

    it('stand all hands and lose', () => {
      bj.play(0, HandAction.Stand)
      bj.play(1, HandAction.Stand)
      bj.play(2, HandAction.Stand)

      expect(bj.state.playerHands[0].status).toBe(HandStatus.Lose)
      expect(bj.state.playerHands[1].status).toBe(HandStatus.Lose)
      expect(bj.state.playerHands[2].status).toBe(HandStatus.Lose)

      expect(bj.public.dealerHand.length).toBe(2)
      expect(bj.public.dealerHand[1]).not.toEqual([-1])

      expect(bj.balanceDiff.bet).toBe(800)
      expect(bj.balanceDiff.win).toBe(0)
    })
  })

  describe('3 hands insurance with blackjack on first', () => {
    const bj = new Blackjack()
    it('start game', () => {
      bj.start(SEEDS.DEALER_A_WITH_FIRST_PLAYER_BJ, publicSeed, [100, 200, 300])

      expect(bj.state.playerHands.length).toBe(3)
      expect(bj.state.playerHands[0].in).toBe(false)
      expect(bj.state.playerHands[0].status).toBe(HandStatus.Blackjack)
      expect(bj.state.playerHands[1].in).toBe(true)
      expect(bj.state.playerHands[2].in).toBe(true)
    })

    it('can not take insurance on first hand', () => {
      expect(() => bj.play(0, HandAction.Insurance)).toThrow('bad_request')
    })

    it('can not hit first hand', () => {
      expect(() => bj.play(0, HandAction.Hit)).toThrow('bad_request')
    })

    it('can not stand first hand', () => {
      expect(() => bj.play(0, HandAction.Stand)).toThrow('bad_request')
    })

    it('decide no insurance on second hand', () => {
      bj.play(1, HandAction.NoInsurance)

      expect(bj.state.playerHands[1].insuranceDecided).toBe(true)
      expect(bj.state.playerHands[1].withInsurance).toBe(false)
    })

    it('insurance last hand', () => {
      bj.play(2, HandAction.Insurance)

      expect(bj.state.playerHands[1].insuranceDecided).toBe(true)
      expect(bj.state.playerHands[1].withInsurance).toBe(false)

      expect(bj.state.playerHands[2].insuranceDecided).toBe(true)
      expect(bj.state.playerHands[2].withInsurance).toBe(true)

      expect(bj.public.dealerHand[1]).toEqual([-1])
    })

    it('stand rest of hands and lose', () => {
      bj.play(1, HandAction.Stand)
      bj.play(2, HandAction.Stand)

      expect(bj.state.playerHands[0].status).toBe(HandStatus.Blackjack)
      expect(bj.state.playerHands[1].status).toBe(HandStatus.Lose)
      expect(bj.state.playerHands[2].status).toBe(HandStatus.Lose)

      expect(bj.public.dealerHand.length).toBe(2)
      expect(bj.public.dealerHand[1]).not.toEqual([-1])

      expect(bj.balanceDiff.bet).toBe(750)
      expect(bj.balanceDiff.win).toBe(250)
    })
  })

  it('split aces', () => {
    const bj = new Blackjack()
    bj.start(SEEDS.ACES_TO_SPLIT, publicSeed)

    const cards = bj.state.playerHands[0].cards
    bj.play(0, HandAction.Split)

    assert.equal(bj.state.playerHands.length, 2)
    expect(bj.state.playerHands[0].cards[0]).toEqual(cards[0])
    expect(bj.state.playerHands[0].cards[1]).not.toEqual(cards[1])
    expect(bj.state.playerHands[1].cards[0]).toEqual(cards[1])
    expect(bj.state.playerHands[0].in).toBe(false)
    expect(bj.state.playerHands[1].in).toBe(false)
  })
})

export const findSeed = (condition: (s: any) => boolean) => {
  let seed = 1
  while (true) {
    const bj = new Blackjack()
    bj.start(String(seed), publicSeed)

    if (condition(bj.state)) return String(seed)

    seed += 1
  }
}
