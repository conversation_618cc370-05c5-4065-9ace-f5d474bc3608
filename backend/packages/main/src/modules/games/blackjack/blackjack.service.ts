import { Int, BjState } from '@crashgg/types/dist'
import { BadRequestException, Injectable } from '@nestjs/common'
import { assert } from 'src/utils/assert'
import { CreateDto } from './dto/create.dto'
import { PrismaService } from '../../prisma/prisma.service'
import { RedisService } from '../../redis/redis.service'
import { UserService } from '../../user/user.service'
import { Blackjack } from './blackjack.class'
import { Bet, Prisma } from '@prisma/client'
import { PlayDto } from './dto/play.dto'
import { FairnessService } from '../../fairness/fairness.service'
import {
  BET_OUTCOME_EVENT,
  BetOutcomeEvent,
  getBalance,
} from '@crashgg/common/dist'
import { AFFILIATE_RATE, HISTORY_INCLUDE } from './blackjack.constants'
import { GameData } from './blackjack.interface'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { ConfigService } from '@nestjs/config'
import { paginate, paginated } from 'src/common/paginate'
import { PaginatedDto } from 'src/common/dto/paginated.dto'

@Injectable()
export class BlackjackService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly redis: RedisService,
    private readonly userService: UserService,
    private readonly fairnessService: FairnessService,
    private readonly eventEmitter: EventEmitter2,
    private readonly config: ConfigService
  ) {}

  async getHistory(currency: Currency) {
    const games = await this.prisma.read.blackjack.findMany({
      where: { status: 'finished', currency },
      take: 10,
      include: HISTORY_INCLUDE,
      orderBy: { createdAt: 'desc' },
    })

    return games.map((game) => this.#toHistoryEntry(game))
  }

  #toHistoryEntry(game: any) {
    return {
      id: game.id,
      currency: game.currency,
      user: game.user,
      ...game.bet,
    }
  }

  async #getCurrent(userId: Int) {
    return this.prisma.blackjack.findFirst({
      where: { userId, status: 'in-progress' },
    })
  }

  async getGameHistory(
    userId: Int,
    paginationOptions: PaginatedDto,
    currency?: Currency
  ) {
    paginationOptions.pageSize ??= 100

    const where: Prisma.BlackjackWhereInput = {
      userId,
      status: 'finished',
      ...(currency && { currency }),
    }

    const getGames = this.prisma.read.blackjack.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      include: HISTORY_INCLUDE,
      ...paginate(paginationOptions),
    })

    const getCount = this.prisma.read.blackjack.count({
      where,
    })

    const [games, count] = await Promise.all([getGames, getCount])

    return paginated({
      data: games.map((game) => {
        const { publicSeed: seed } = game.state as any as BjState
        const { createdAt, currency } = game

        return {
          gameId: game.id,
          ...game.bet,
          currency,
          seed,
          createdAt,
        }
      }),
      count,
      pageSize: paginationOptions.pageSize,
    })
  }

  async getCurrentPublic(userId: Int) {
    const game = await this.#getCurrent(userId)
    if (!game) return { game: null }

    const bj = new Blackjack()
    bj.resume(game.state as any as BjState)

    return { game: bj.public, currency: game.currency, gameId: game?.id }
  }

  private validateAndGetBetAmount(amounts: Int | Int[]) {
    if (typeof amounts === 'number') return amounts

    const initHandsLimit = this.config.get('blackjack.initHandsLimit')
    assert(
      amounts.length <= initHandsLimit && amounts.length > 0,
      'invalid_hands_count'
    )

    return amounts.reduce((acc, amount) => {
      assert(amount > 0, 'invalid_amount')
      return acc + amount
    }, 0)
  }

  async create(userId: Int, body: CreateDto, currency: Currency) {
    const hasPending = await this.#getCurrent(userId)
    assert(!hasPending, 'has_pending_game')

    const { amount: amounts, clientSeed } = body
    const amount = this.validateAndGetBetAmount(amounts)

    const { privateSeed, publicSeed, historyId } =
      await this.fairnessService.useRotatingSeed({
        clientSeed,
        userId,
      })
    const serverSeed = publicSeed.split(':')[0]

    return this.prisma.$transaction(async (tx) => {
      const { user, bet } = await this.userService.placeBet(tx, {
        userId,
        amount,
        affiliateEarnings: amount * AFFILIATE_RATE,
        game: 'blackjack',
        transactionData: { message: 'Blackjack' },
        ...(historyId && {
          betData: { rotatingSeedHistory: { connect: { id: historyId } } },
        }),
        currency,
        isAsync: true,
      })

      const bj = new Blackjack()
      const amountsArr = Array.isArray(amounts) ? amounts : [amounts]
      bj.start(privateSeed, publicSeed, amountsArr)

      const blackjack = await tx.blackjack.create({
        data: {
          betId: (bet as Bet).id,
          state: bj.state as any,
          status: bj.allHandsDone ? 'finished' : 'in-progress',
          currency,
          serverSeed,
          clientSeed,
          userId,
        },
        include: HISTORY_INCLUDE,
      })
      await tx.bet.update({
        where: { id: (bet as Bet).id },
        data: { blackjackId: blackjack.id },
      })

      const newBalance = getBalance(user, currency) + BigInt(bj.balanceDiff.win)

      if (bj.allHandsDone) {
        await this.userService.handleWin(tx, {
          betId: (bet as Bet).id,
          winningAmount: bj.balanceDiff.win,
          transactionData: { message: 'Blackjack win' },
          currency,
          isAsync: true,
        })
        this.#onGameEnd(
          {
            ...blackjack,
            bet: {
              betAmount: bj.balanceDiff.bet,
              winningAmount: bj.balanceDiff.win,
            },
          },
          bj.cardsDrawn,
          newBalance
        )
      }

      return {
        game: bj.public,
        gameId: blackjack.id,
        balanceDiff: bj.balanceDiff,
        cardsDrawn: bj.cardsDrawn,
        currency,
        newBalance,
        ...(historyId
          ? { seed: publicSeed }
          : bj.allHandsDone && {
              serverSeed,
              clientSeed,
              serverHash: this.fairnessService.hash(serverSeed),
            }),
      }
    })
  }

  async play(userId: Int, body: PlayDto) {
    const { action, gameId, handId } = body

    const game = await this.prisma.blackjack.findUnique({
      where: { id: gameId },
    })
    assert(game?.status === 'in-progress', 'game_not_found')
    assert(game.userId === userId, 'not_your_game')

    let cardsDrawn
    const bj = new Blackjack()
    bj.resume(game.state as any)
    const cardsDrawnBefore = bj.cardsDrawn
    try {
      bj.play(handId, action)
      cardsDrawn = bj.cardsDrawn - cardsDrawnBefore
    } catch (err) {
      throw new BadRequestException('forbidden_action')
    }

    let newUser = null

    await this.prisma.$transaction(async (tx) => {
      const { bet, win } = bj.balanceDiff

      if (bet) {
        const { user } = await this.userService.addToBet(tx, {
          betId: game.betId,
          userId,
          amount: bet,
          currency: game.currency as Currency,
          affiliateEarnings: bet * AFFILIATE_RATE,
          transactionData: { message: 'Blackjack extras' },
        })
        newUser = user
      }

      if (win) {
        const { user } = await this.userService.handleWin(tx, {
          betId: game.betId,
          winningAmount: win,
          currency: game.currency as Currency,
          transactionData: { message: 'Blackjack win' },
          isAsync: true,
        })
        newUser = user
      }

      const updated = await tx.blackjack.update({
        where: { id: gameId },
        data: {
          ...(bj.allHandsDone && { status: 'finished' }),
          state: bj.state as any,
        },
        ...(bj.allHandsDone && { include: HISTORY_INCLUDE }),
      })

      if (bj.allHandsDone) {
        this.#onGameEnd(updated, cardsDrawn)
      }
    })

    return {
      game: bj.public,
      gameId: game.id,
      balanceDiff: bj.balanceDiff,
      cardsDrawn,
      ...(newUser && {
        newBalance: getBalance(newUser, game.currency as Currency),
      }),
      ...(game.serverSeed === 'x'
        ? { seed: (game.state as any).publicSeed }
        : bj.allHandsDone && {
            serverSeed: game.serverSeed,
            serverHash: this.fairnessService.hash(game.serverSeed),
            clientSeed: game.clientSeed,
          }),
    }
  }

  private emitBetOutcome(game: GameData, balanceAfter?: bigint) {
    this.eventEmitter.emit(
      BET_OUTCOME_EVENT,
      new BetOutcomeEvent({
        userId: game.userId,
        betAmount: game.bet.betAmount,
        winAmount: game.bet.winningAmount,
        game: 'blackjack',
        currency: game.currency as Currency,
        balanceAfter,
      })
    )
  }

  #onGameEnd(game: GameData, cardsDrawn: number, newBalance?: bigint) {
    setTimeout(
      () => {
        this.redis.publishPublic('blackjack:new', this.#toHistoryEntry(game))
        this.emitBetOutcome(game, newBalance)
      },
      (cardsDrawn + 1) * 1000
    )
  }
}
