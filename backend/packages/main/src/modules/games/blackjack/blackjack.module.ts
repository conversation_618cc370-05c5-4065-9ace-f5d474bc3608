import { Module } from '@nestjs/common'
import { BlackjackService } from './blackjack.service'
import { BlackjackController } from './blackjack.controller'
import { UserModule } from '../../user/user.module'
import { FairnessModule } from '../../fairness/fairness.module'

@Module({
  imports: [UserModule, FairnessModule],
  providers: [BlackjackService],
  controllers: [BlackjackController],
})
export class BlackjackModule {}
