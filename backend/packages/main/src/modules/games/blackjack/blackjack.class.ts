import seedrandom from 'seedrandom'
import {
  BjState,
  Card,
  HandAction,
  HandStatus,
  PlayerHand,
  RANKS,
  SUITS,
  BjPublic,
  Int,
  HandAddition,
} from '@crashgg/types/dist'
import { assert } from '../../../utils/assert'
import { Logger } from '@nestjs/common'

export class Blackjack {
  private readonly logger = new Logger(Blackjack.name)
  private readonly BLACKJACK_PAYS = 3 / 2
  private readonly INSURANCE_PAYS = 2 / 1
  private readonly BJ = 21

  #privateSeed: string
  #publicSeed: string
  #nextCardId: Int
  #dealerHand: Card[]
  #playerHands: PlayerHand[] = []
  #playerWinDiff = 0
  #playerBetDiff = 0

  get state(): BjState {
    return {
      privateSeed: this.#privateSeed,
      publicSeed: this.#publicSeed,
      nextCardId: this.#nextCardId,
      dealerHand: this.#dealerHand,
      playerHands: this.#playerHands,
    }
  }

  get public(): BjPublic {
    return {
      dealerHand: this.allHandsDone
        ? this.#dealerHand
        : [this.#dealerHand[0], [-1]],
      dealerHandValue: this.allHandsDone
        ? this.#handValue(this.#dealerHand)
        : this.#cardValue(this.#dealerHand[0]),
      playerHands: this.#playerHands.map((hand) => {
        return {
          ...hand,
          value: this.#handValue(hand.cards),
          canSplit: this.canSplit(hand),
          canInsurance: this.canInsurance(hand),
          canDouble: this.canDouble(hand),
        }
      }),
      allHandsDone: this.allHandsDone,
    }
  }

  get balanceDiff() {
    return {
      bet: this.#playerBetDiff,
      win: this.#playerWinDiff,
    }
  }

  get cardsDrawn() {
    return this.#nextCardId - 1
  }

  draw() {
    const drawCards = (cards: Card[]) => {
      const hand = cards.map((c) => c[0]).join(', ')
      const value = this.#handValue(cards)

      return `${hand} (${value})`
    }

    let output = `dealer: ${drawCards(this.#dealerHand)}\n`
    this.#playerHands.forEach((hand) => {
      output += `player: ${drawCards(hand.cards)} [${hand.status}]\n`
    })

    this.logger.debug(output)
  }

  start(privateSeed: string, publicSeed: string, stake: Int[] = [0]) {
    this.#privateSeed = privateSeed
    this.#publicSeed = publicSeed
    this.#nextCardId = 1
    this.#dealerHand = [this.#drawCard(), this.#drawCard()]
    stake.forEach((handStake) => {
      this.#playerHands.push(
        this.#newPlayerHand([this.#drawCard(), this.#drawCard()], handStake)
      )
      this.#playerBetDiff += handStake
    })

    const realBjHands = this.#playerHands.filter((hand) => {
      const playerValue = this.#handValue(hand.cards)
      return playerValue === this.BJ
    })

    realBjHands.forEach((hand) => {
      this.#onPlayerBj(hand)
    })

    if (realBjHands.some((hand) => hand.status === HandStatus.Tie)) {
      this.#playerHands.forEach((hand) => {
        if (hand.status !== HandStatus.Tie) {
          hand.in = false
          hand.status = HandStatus.Lose
        }
      })
    }

    if (this.allHandsDone) {
      this.#determineWinner()
      this.#determinePayouts()
    }
  }

  resume(
    state: BjState & { withInsurance?: boolean; insuranceDecided?: boolean }
  ) {
    this.#privateSeed = state.privateSeed
    this.#publicSeed = state.publicSeed
    this.#nextCardId = state.nextCardId
    this.#dealerHand = state.dealerHand
    let playerHands = state.playerHands
    if (playerHands.some((hand) => !('withInsurance' in hand))) {
      playerHands = playerHands.map((hand) => ({
        ...hand,
        withInsurance: state.withInsurance,
        insuranceDecided: state.insuranceDecided,
      }))
    }
    this.#playerHands = playerHands
  }

  play(idx: Int, action: HandAction) {
    const playerHand = this.#playerHands[idx]
    assert(playerHand, 'invalid_hand')
    assert(playerHand.in)

    switch (action) {
      case HandAction.Hit:
        this.#hit(playerHand)
        break
      case HandAction.Stand:
        this.#stand(playerHand)
        break
      case HandAction.Double:
        this.#doubleDown(playerHand)
        break
      case HandAction.Split:
        this.#split(playerHand)
        break
      case HandAction.Insurance:
        this.#insurance(playerHand)
        break
      case HandAction.NoInsurance:
        this.#noInsurance(playerHand)
        break
    }

    if (this.allHandsDone) {
      this.#drawDealerCards()
      this.#determineWinner()
      this.#determinePayouts()
    }
  }

  #hit(playerHand: PlayerHand) {
    assert(this.#playerHands.every((hand) => !this.canInsurance(hand)))

    playerHand.cards.push(this.#drawCard())

    const value = this.#handValue(playerHand.cards)

    // We only want to show bust before all hands finish,
    // so dealer hand is not leaked
    if (value >= this.BJ) {
      playerHand.in = false
      playerHand.status = HandStatus.Lose
    }
  }

  #stand(playerHand: PlayerHand) {
    assert(this.#playerHands.every((hand) => !this.canInsurance(hand)))

    playerHand.in = false
  }

  canDouble(playerHand: PlayerHand) {
    return (
      playerHand.in &&
      playerHand.cards.length === 2 &&
      this.#playerHands.every((hand) => !this.canInsurance(hand))
    )
  }

  #doubleDown(playerHand: PlayerHand) {
    assert(this.canDouble(playerHand))

    this.#playerBetDiff += playerHand.stake
    playerHand.stake *= 2
    playerHand.in = false
    playerHand.cards.push(this.#drawCard())
  }

  canSplit(playerHand: PlayerHand) {
    if (this.#playerHands.some((hand) => this.canInsurance(hand))) return false
    if (
      playerHand.isSplit ||
      (!('isSplit' in playerHand) && this.#playerHands.length !== 1)
    )
      return false
    if (!playerHand.in) return false

    const cards = playerHand.cards

    return cards.length === 2 && cards[0][0] === cards[1][0]
  }

  #split(playerHand: PlayerHand) {
    assert(this.canSplit(playerHand))

    const { cards, stake } = playerHand

    const dataToCopy = {
      isSplit: true,
      insuranceDecided: playerHand.insuranceDecided,
      withInsurance: playerHand.withInsurance,
    }

    const handIndex = this.#playerHands.indexOf(playerHand)
    this.#playerHands.splice(handIndex, 1)

    const isAcesSplit = cards[0][0] === 'A'

    this.#playerHands = [
      {
        ...this.#newPlayerHand([cards[0], this.#drawCard()], stake, dataToCopy),
        in: !isAcesSplit,
      },
      {
        ...this.#newPlayerHand([cards[1], this.#drawCard()], stake, dataToCopy),
        in: !isAcesSplit,
      },
      ...this.#playerHands,
    ]

    this.#playerBetDiff += stake

    this.#playerHands.forEach((hand) => {
      const value = this.#handValue(hand.cards)

      if (value === this.BJ) hand.in = false
    })
  }

  canInsurance(playerHand: PlayerHand) {
    return (
      !this.allHandsDone &&
      !playerHand.withInsurance &&
      this.#dealerHand[0][0] === 'A' &&
      !playerHand.insuranceDecided &&
      playerHand.in
    )
  }

  #insurance(playerHand: PlayerHand) {
    assert(this.canInsurance(playerHand))

    playerHand.withInsurance = true
    playerHand.insuranceDecided = true
    this.#playerBetDiff += ~~(playerHand.stake / 2)

    this.#afterInsurance()
  }

  #noInsurance(playerHand: PlayerHand) {
    assert(this.canInsurance(playerHand))

    playerHand.insuranceDecided = true

    this.#afterInsurance()
  }

  #afterInsurance() {
    if (this.#playerHands.some((hand) => !hand.insuranceDecided)) return

    if (this.#handValue(this.#dealerHand) === this.BJ) {
      this.#playerHands = this.#playerHands.map((hand) => ({
        ...hand,
        in: false,
      }))
    } else {
      this.#playerHands = this.#playerHands.map((hand) => ({
        ...hand,
        withInsurance: false,
      }))
    }
  }

  get allHandsDone() {
    return this.#playerHands.every((hand) => !hand.in)
  }

  #determineWinner() {
    const dealerValue = this.#handValue(this.#dealerHand)

    this.#playerHands.forEach((hand) => {
      const playerValue = this.#handValue(hand.cards)

      if (playerValue > this.BJ) {
        hand.status = HandStatus.Lose
        return
      }

      if (playerValue === this.BJ) {
        return this.#onPlayerBj(hand)
      }

      if (dealerValue > this.BJ) {
        hand.status = HandStatus.Win
        return
      }

      if (playerValue === dealerValue) {
        hand.status = HandStatus.Tie
        return
      }

      hand.status = playerValue > dealerValue ? HandStatus.Win : HandStatus.Lose
    })
  }

  #determinePayouts() {
    this.#playerHands.forEach((hand) => {
      switch (hand.status) {
        case HandStatus.Win:
          this.#playerWinDiff += 2 * hand.stake
          break
        case HandStatus.Tie:
          this.#playerWinDiff += hand.stake
          break
        case HandStatus.Blackjack:
          this.#playerWinDiff += ~~((1 + this.BLACKJACK_PAYS) * hand.stake)
          break
      }

      if (
        hand.withInsurance &&
        this.#dealerHand.length === 2 &&
        this.#handValue(this.#dealerHand) === this.BJ
      ) {
        const insuranceStake = ~~(hand.stake / 2)
        this.#playerWinDiff += (1 + this.INSURANCE_PAYS) * insuranceStake
      }
    })
  }

  #onPlayerBj(playerHand: PlayerHand) {
    const dealerValue = this.#handValue(this.#dealerHand)
    playerHand.in = false

    const isRealBlackjack = playerHand.cards.length === 2 && !playerHand.isSplit
    const winStatus = isRealBlackjack ? HandStatus.Blackjack : HandStatus.Win
    playerHand.status = dealerValue === this.BJ ? HandStatus.Tie : winStatus
  }

  #drawDealerCards() {
    while (this.#handValue(this.#dealerHand) < 17) {
      this.#dealerHand.push(this.#drawCard())
    }
  }

  #drawCard(): Card {
    const cardSeed = `${this.#privateSeed}:${this.#nextCardId}`
    const id = Math.round(seedrandom(cardSeed)() * 51)

    const card: Card = [
      RANKS[id % RANKS.length],
      SUITS[Math.floor(id / RANKS.length)],
    ]

    this.#nextCardId += 1

    return card
  }

  #handValue(cards: Card[]): Int {
    let value = cards.reduce((sum, card) => sum + this.#cardValue(card), 0)

    let acesCount = cards.reduce(
      (count, [rank]) => count + (rank === 'A' ? 1 : 0),
      0
    )

    while (value > this.BJ && acesCount > 0) {
      value -= 10
      acesCount -= 1
    }

    return value
  }

  #cardValue([rank]: Card) {
    if (typeof rank === 'number') return rank
    if (rank === 'A') return 11
    return 10
  }

  #newPlayerHand(
    cards: Card[],
    stake: Int,
    additionalData?: HandAddition
  ): PlayerHand {
    const {
      isSplit = false,
      insuranceDecided = false,
      withInsurance = false,
    } = additionalData ?? {}

    return {
      cards,
      status: HandStatus.InProgress,
      stake,
      in: true,
      isSplit,
      insuranceDecided,
      withInsurance,
    }
  }
}
