import alertsConfig from 'src/modules/admin/alerts/alerts.config'
import sportsbookConfig from 'src/modules/games/sportsbook/sportsbook.config'
import adscendConfig from 'src/modules/payments/adscend/adscend.config'
import quickerConfig from 'src/modules/payments/quicker/quicker.config'
import rafflesConfig from 'src/modules/rewards/raffles/raffles.config'
import affiliates from '../affiliates/affiliates.config'
import antifraudConfig from '../antifraud/antifraud.config'
import auth from '../auth/auth.config'
import twoFactor from '../auth/two-factor/two-factor.config'
import cases from '../games/cases/cases.config'
import coinflip from '../games/coinflip/coinflip.config'
import conversionConfig from '../conversion/conversion.config'
import scaleoConfig from '../conversion/scaleo/scaleo.config'
import duels from '../games/duels/duels.config'
import email from '../email/email.config'
import roulette from '../games/roulette/roulette.config'
import tripleGreen from '../games/roulette/triple-green/triple-green.config'
import imagesConfig from '../images/images.config'
import jackpot from '../games/jackpot/jackpot.config'
import luckyPot from '../games/jackpot/lucky-pot/lucky-pot.config'
import leaderboard from '../leaderboard/leaderboard.config'
import lottery from '../lottery/lottery.config'
import antifraud from '../payments/antifraud/antifraud.config'
import kyc from '../payments/antifraud/kyc/kyc.config'
import location from '../payments/antifraud/location/location.config'
import checkout from '../payments/checkout/checkout.config'
import currency from '../payments/currency/currency.config'
import expayConfig from '../payments/expay/expay.config'
import fireblocks from '../payments/fireblocks/fireblocks.config'
import giftcards from '../payments/giftcards/giftcards.config'
import nmi from '../payments/nmi/nmi.config'
import paycomConfig from '../payments/paycom/paycom.config'
import paypal from '../payments/paypal/paypal.config'
import skinsback from '../payments/skinsback/skinsback.config'
import snipeskinsConfig from '../payments/snipeskins/snipeskins.config'
import stripe from '../payments/stripe/stripe.config'
import masspayConfig from '../payments/masspay/masspay.config'
import memepayConfig from '../payments/memepay/memepay.config'
import unlimit from '../payments/unlimit/unlimit.config'
import waxpeer from '../payments/waxpeer/waxpeer.config'
import zen from '../payments/zen/zen.config'
import rain from '../rain/rain.config'
import calendar from '../rewards/calendar/calendar.config'
import faucet from '../rewards/faucet/faucet.config'
import playfaucet from '../rewards/play-faucet/play-faucet.config'
import vip from '../rewards/vip/vip.config'
import premium from '../user/premium/premium.config'
import configuration from './configuration'
import userConfig from '../user/user.config'
import itemCoinflip from '../games/item-coinflip/item-coinflip.config'
import rakeback from '../rewards/rakeback/rakeback.config'
import payments from '../payments/payments.config'
import numipayConfig from '../payments/numipay/numipay.config'
import paytechConfig from '../payments/paytech/paytech.config'
import captchaConfig from '../captcha/captcha.config'
import lootablyConfig from '../payments/lootably/lootably.config'
import lossbackCaseConfig from '../rewards/lossback-case/lossback-case.config'
import dailyConfig from '../rewards/daily/daily.config'
import skywindConfig from '../games/slots/skywind/skywind.config'
import betFeedConfig from '../bet-feed/bet-feed.config'
import blackjackConfig from '../games/blackjack/blackjack.config'
import pulseConfig from '../payments/pulse/pulse.config'
import missionsConfig from '../rewards/missions/missions.config'
import levelTransferConfig from '../user/level-transfer/level-transfer.config'
import meshconnectConfig from '../payments/meshconnect/meshconnect.config'
import gitSlotParkConfig from '../games/slots/git-slot-park/git-slot-park.config'
import oneSignalConfig from '../one-signal/one-signal.config'
import hub88Config from '../games/slots/hub88/hub88.config'
import rewardEarningsConfig from '../rewards/reward-earnings/reward-earnings.config'
import cs2redConfig from '../conversion/cs2red/cs2red.config'
import betbyConfig from '../games/slots/betby/betby.config'

export const CONFIG_MODULES = [
  configuration,
  auth,
  twoFactor,
  faucet,
  playfaucet,
  calendar,
  affiliates,
  vip,
  cases,
  duels,
  fireblocks,
  giftcards,
  nmi,
  paypal,
  antifraud,
  location,
  kyc,
  leaderboard,
  rain,
  skinsback,
  stripe,
  zen,
  unlimit,
  lottery,
  email,
  coinflip,
  itemCoinflip,
  jackpot,
  luckyPot,
  waxpeer,
  currency,
  roulette,
  tripleGreen,
  checkout,
  premium,
  conversionConfig,
  alertsConfig,
  sportsbookConfig,
  quickerConfig,
  rafflesConfig,
  adscendConfig,
  snipeskinsConfig,
  antifraudConfig,
  imagesConfig,
  paycomConfig,
  expayConfig,
  scaleoConfig,
  masspayConfig,
  userConfig,
  memepayConfig,
  rakeback,
  payments,
  numipayConfig,
  paytechConfig,
  captchaConfig,
  lootablyConfig,
  lossbackCaseConfig,
  dailyConfig,
  skywindConfig,
  betFeedConfig,
  blackjackConfig,
  pulseConfig,
  missionsConfig,
  levelTransferConfig,
  meshconnectConfig,
  gitSlotParkConfig,
  oneSignalConfig,
  hub88Config,
  rewardEarningsConfig,
  cs2redConfig,
  betbyConfig,
]
