import { strBool } from '@crashgg/common/dist'
import { SITE_CODES } from 'src/common/constants'

export default () => ({
  isDev: process.env.NODE_ENV === 'development',
  features: (process.env.FEATURES || '-').toLowerCase().split(';'),
  debugLogging: bool(process.env.DEBUG_LOGGING),
  siteTag: process.env.SITE_TAG || 'rustclash',
  siteName: process.env.SITE_NAME || 'RustClash',
  legalEntity: process.env.LEGAL_ENTITY || 'Rust Clash Entertainment Ltd',
  balanceUsdRate: Number(process.env.BALANCE_USD_RATE ?? 1),
  siteCode: ensureInEnv(
    SITE_CODES,
    process.env.SITE_CODE || 'RC',
    'Unknown siteCode, add your new siteCode to SITE_CODES array'
  ),
  topLevelDomain: process.env.TOP_LEVEL_DOMAIN || 'rustclash.com',
  port: parseInt(process.env.$PORT) || parseInt(process.env.PORT, 10) || 7000,
  isApiPath: bool(process.env.IS_API_PATH),
  isClashCsgo: isClashCsgo(),
  isClash:
    bool(process.env.IS_RUSTCLASH) ||
    bool(process.env.IS_CLASH) ||
    bool(process.env.IS_CLASH_CSGO),
  isRustClash: bool(process.env.IS_RUSTCLASH),
  isDotaClash: bool(process.env.IS_DOTACLASH),
  isCases: bool(process.env.IS_CASESGG),
  isCrash: bool(process.env.IS_CRASHGG),
  isHypeblox: bool(process.env.IS_HYPEBLOX),
  clientUrl: process.env.CLIENT_URL,
  corsOrigins: process.env.CORS_ORIGINS
    ? process.env.CORS_ORIGINS.split(';')
    : [],
  apiUrl: process.env.API_URL,
  apiUrlInternal: process.env.API_URL_INTERNAL || process.env.API_URL,
  hashidsSalt: process.env.HASHIDS_SALT,
  isStaging: bool(process.env.IS_STAGING),
  serverId: process.env.SERVER_ID || 'web',
  sharedRedisUrl: ensureEnv('SHARED_REDIS_URL'),
  isJobProcessor: strBool(process.env.IS_JOB_PROCESSOR, true),
  isTenant: bool(process.env.IS_TENANT),
  tenantId: process.env.TENANT_ID,
  mastermindSignature: process.env.MASTERMIND_SIGNATURE,
})

export const isClashCsgo = () => {
  return bool(process.env.IS_CLASH_CSGO)
}

export const isCases = () => {
  return bool(process.env.IS_CASESGG)
}

export const bool = (variable: string) => ['1', 'true'].includes(variable)

/**
 * @deprecated
 */
export const enabledModules = <T>(modules: T[]) => {
  return modules.filter(Boolean)
}

export const enabledIf = (mod: any, condition: boolean) => {
  return condition ? mod : null
}

export const enabledIfEnv = (mod: any, env: string) => {
  return process.env[env] ? mod : null
}

const SITES = {
  clash: 'IS_CLASH_FAIMLY',
  clashCsgo: 'IS_CLASH_CSGO',
  crash: 'IS_CRASHGG',
  hypeblox: 'IS_HYPEBLOX',
  cases: 'IS_CASESGG',
}
type Site = keyof typeof SITES

const ensureEnv = (env: string) => {
  if (process.env[env]) return process.env[env]
  throw new Error(`Failed to load ${env}`)
}

const ensureInEnv = (arr: any[] | readonly any[], env: string, msg: string) => {
  if (arr.includes(env)) return env
  throw new Error(msg)
}

/**
 * @deprecated
 */
export const enabledFor = (mod: any, sites: Site | Site[]) => {
  if (!Array.isArray(sites)) sites = [sites]

  const condition =
    process.env.NODE_ENV === 'development' ||
    sites.some((site) => bool(process.env[SITES[site]]))

  return enabledIf(mod, condition)
}
