import { ConfigModule } from '@nestjs/config'
import { Feature, isFeature } from '@crashgg/common/dist'
import { DynamicModule, Logger } from '@nestjs/common'

export class ConditionalModule {
  static async forFeature(
    module: any,
    feature: Feature
  ): Promise<DynamicModule> {
    return ConditionalModule.forPredicate(module, () => isFeature(feature))
  }

  static async forEnvVar(module: any, envVar: string): Promise<DynamicModule> {
    return ConditionalModule.forPredicate(module, () =>
      Boolean(process.env[envVar])
    )
  }

  private static async forPredicate(
    module: any,
    predicate: () => boolean
  ): Promise<DynamicModule> {
    await ConfigModule.envVariablesLoaded

    const returnModule: Required<
      Pick<DynamicModule, 'module' | 'imports' | 'exports'>
    > = { module: ConditionalModule, imports: [], exports: [] }

    if (!predicate()) {
      Logger.debug(`Skipping module=${module?.name}`)
      return returnModule
    }

    returnModule.imports.push(module)
    returnModule.exports.push(module)

    return returnModule
  }
}
