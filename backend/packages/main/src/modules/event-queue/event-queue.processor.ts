import { EVENT_QUEUE_NAME } from '@crashgg/common/dist'
import { Logger } from '@nestjs/common'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { Job } from 'bullmq'
import { ProcessMq, ProcessorMq } from 'src/common/decorators/bullmq.decorator'

@ProcessorMq(EVENT_QUEUE_NAME)
export class EventQueueProcessor {
  private log = new Logger(EventQueueProcessor.name)
  constructor(private eventEmmiter: EventEmitter2) {}

  @ProcessMq('*')
  async process(job: Job<any, any, string>) {
    this.log.log('Got new event job name=%s data=%o', job.name, job.data)
    this.eventEmmiter.emit(job.name, job.data)
  }
}
