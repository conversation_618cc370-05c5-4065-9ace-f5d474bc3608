import { PRIMARY_COLOR_SHADE_KEYS } from '../components/branded-tailwind'
import {
  BASE_SHADE,
  GRADIENT_DARK_OFFSET,
  GRADIENT_LIGHT_OFFSET,
  HUE_MAX,
  HUE_MIN,
  LIGHTNESS_STRENGTH,
  SATURATION_STRENGTH,
} from '../constants/colors'

export const formatNumber = (price: number) => {
  return (price / 100).toFixed(2)
}

export const maskText = (text: string, revelated = 4) => {
  const maskLength = Math.max(text.length - revelated, 0)
  let masked = ''
  for (let i = 0; i < maskLength; i++) {
    masked += '*'
  }
  return masked + text.slice(-revelated)
}

export const formatProviderName = (provider: string) => {
  if (provider === 'paytech') return 'CARD'
  return provider?.toUpperCase()
}

export const hexToHsl = (hex: string) => {
  const r = parseInt(hex.slice(1, 3), 16) / 255
  const g = parseInt(hex.slice(3, 5), 16) / 255
  const b = parseInt(hex.slice(5, 7), 16) / 255

  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)

  let h: number
  let s: number
  const l = (max + min) / 2

  if (max === min) {
    h = s = 0
  } else {
    const d = max - min
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min)
    switch (max) {
      case r:
        h = (g - b) / d + (g < b ? 6 : 0)
        break
      case g:
        h = (b - r) / d + 2
        break
      case b:
        h = (r - g) / d + 4
        break
      default:
        h = 0
        console.error('Should never happen')
        break
    }
    h *= 60
  }

  return {
    h,
    s: s * 100,
    l: l * 100,
  }
}

export const hslToHex = ({ h, s, l }: { h: number; s: number; l: number }) => {
  s /= 100
  l /= 100

  const k = (n: number) => (n + h / 30) % 12
  const a = s * Math.min(l, 1 - l)
  const f = (n: number) =>
    Math.round(
      255 * (l - a * Math.max(-1, Math.min(k(n) - 3, Math.min(9 - k(n), 1))))
    )

  return `#${[f(0), f(8), f(4)]
    .map((x) => x.toString(16).padStart(2, '0'))
    .join('')}`
}

export const getColorRatio = (shade: number) => {
  const min = 50
  const max = 950

  const d = Math.abs(shade - BASE_SHADE)
  const maxD = Math.max(BASE_SHADE - min, max - BASE_SHADE)
  return d / maxD
}

export const getPrimaryColorShades = (primaryColor: string) => {
  const baseColor = hexToHsl(primaryColor)
  const primary: Record<string, string> = {}
  PRIMARY_COLOR_SHADE_KEYS.forEach((shade) => {
    if (shade === BASE_SHADE) {
      primary[shade] = primaryColor
    }
    if (shade === 'gradient-dark') {
      primary[shade] = hslToHex({
        ...baseColor,
        l: baseColor.l + GRADIENT_DARK_OFFSET,
      })
    } else if (shade === 'gradient-light') {
      primary[shade] = hslToHex({
        ...baseColor,
        l: baseColor.l + GRADIENT_LIGHT_OFFSET,
      })
    } else {
      const ratio = getColorRatio(shade)

      const h = baseColor.h + ratio * (shade < BASE_SHADE ? HUE_MIN : HUE_MAX)
      const s = Math.min(
        100,
        Math.max(
          0,
          baseColor.s +
            ratio * (shade < BASE_SHADE ? 1 : -1) * SATURATION_STRENGTH
        )
      )
      const l = Math.min(
        98,
        Math.max(
          1,
          baseColor.l +
            ratio *
              (shade < 500 ? 1 : -1) *
              LIGHTNESS_STRENGTH *
              (PRIMARY_COLOR_SHADE_KEYS.length / 2)
        )
      )

      primary[shade] = hslToHex({ h, s, l })
    }
  })
  return primary
}
