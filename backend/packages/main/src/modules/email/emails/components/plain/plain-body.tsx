import { Body } from '@react-email/components'
import React, { ComponentProps } from 'react'

interface PlainBodyProps extends ComponentProps<typeof Body> {
  children?: React.ReactNode
  className?: string
}

export const PlainBody: React.FC<PlainBodyProps> = ({
  children,
  className = '',
  ...props
}) => {
  return (
    <Body className={`bg-offwhite text-black ${className}`.trim()} {...props}>
      {children}
    </Body>
  )
}
