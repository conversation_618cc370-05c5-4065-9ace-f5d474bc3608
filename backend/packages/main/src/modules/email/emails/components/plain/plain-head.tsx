import React from 'react'
import { Head } from '@react-email/components'
import { BrandedFonts } from '../branded-fonts'
import { EmailBrand } from '../../constants/brand'

export const PlainHead: React.FC<{
  children?: React.ReactNode
  brand: EmailBrand
}> = ({ children, brand }) => {
  return (
    <Head>
      <BrandedFonts brand={brand} />
      <meta name="color-scheme" content="light" />
      <meta name="supported-color-schemes" content="light" />
      {children}
    </Head>
  )
}
