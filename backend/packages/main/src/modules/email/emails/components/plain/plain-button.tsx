import React from 'react'
import { Button as ReactEmailButton } from '@react-email/components'
import { type ComponentProps } from 'react'
import { EmailBrand, isBrand } from '../../constants/brand'

type ButtonSize = 'square' | 'xsmall' | 'small' | 'medium' | 'large'
type ButtonColor = 'primary' | 'secondary' | 'transparent'
type ButtonVariant = 'icon' | 'branded' | 'outlined'

export type PlainButtonProps = {
  brand: EmailBrand
  size?: ButtonSize
  color?: ButtonColor
  variant?: ButtonVariant
  disabled?: boolean
  className?: string
} & ComponentProps<typeof ReactEmailButton>

export const PlainButton: React.FC<PlainButtonProps> = ({
  brand,
  children,
  size,
  color,
  variant,
  disabled,
  className,
  ...props
}) => {
  const sizes: Record<ButtonSize, string> = {
    square:
      'relative flex h-10 w-10 items-center justify-center rounded text-xl',
    xsmall: 'rounded px-2 py-0.5 text-xs',
    small: 'rounded px-2 py-1 text-sm',
    medium: 'rounded px-4 py-2.5 text-sm',
    large: 'rounded p-4 text-sm',
  }

  const colors: Record<ButtonColor, string> = {
    primary: isBrand(brand, ['csgo', 'rust'])
      ? 'bg-green-500 text-white'
      : 'bg-primary-500 text-white',
    secondary: 'bg-gray-300 text-gray-900',
    transparent: 'bg-transparent',
  }

  const variants: Record<ButtonVariant, string> = {
    icon: 'flex items-center justify-center gap-3 py-3 font-normal text-white',
    branded: 'bg-primary-300 font-bold text-gray-500',
    outlined: 'border border-gray-300 bg-transparent text-gray-200',
  }

  const baseClasses =
    'border border-transparent tracking-wider transition-colors duration-100 ease-in-out focus:outline-none'
  const disabledClasses = disabled
    ? 'cursor-not-allowed opacity-50'
    : 'hover:opacity-50'
  const sizeClasses = sizes[size ?? 'medium']
  const colorClasses = colors[color ?? 'primary']
  const variantClasses = variant ? variants[variant] : ''

  const combinedClassName =
    `${baseClasses} ${disabledClasses} ${sizeClasses} ${colorClasses} ${variantClasses} ${
      className || ''
    }`.trim()

  return (
    <ReactEmailButton className={combinedClassName} {...props}>
      {children}
    </ReactEmailButton>
  )
}
