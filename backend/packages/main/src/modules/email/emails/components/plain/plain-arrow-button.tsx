import React from 'react'
import { PlainButton, PlainButtonProps } from './plain-button'
import { PlainRightArrow } from './plain-right-arrow'

interface PlainArrowButtonProps extends PlainButtonProps {
  children: React.ReactNode
}

export const PlainArrowButton: React.FC<PlainArrowButtonProps> = ({
  children,
  ...props
}) => {
  return (
    <PlainButton {...props}>
      <span>{children}</span>
      <PlainRightArrow style={{ verticalAlign: 'middle', marginBottom: '2px' }} />
    </PlainButton>
  )
}
