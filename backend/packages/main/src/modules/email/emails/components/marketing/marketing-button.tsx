import React from 'react'
import {
  <PERSON><PERSON> as ReactEmailButton,
  ButtonProps as ReactEmailButtonProps,
} from '@react-email/components'

export type MarketingButtonProps = {
  children: React.ReactNode
} & ReactEmailButtonProps

export const MarketingButton: React.FC<MarketingButtonProps> = ({
  children,
  style,
  className,
  ...props
}) => {
  const buttonClasses =
    'text-primary-700 no-underline bg-transparent border border-solid border-transparent rounded-[60px] px-[50px] py-[5px] font-bold text-base tracking-normal leading-8 text-center'

  return (
    <ReactEmailButton
      {...props}
      className={`${buttonClasses} ${className || ''}`}
      style={{
        fontFamily: "'Helvetica Neue', Helvetica, Arial, sans-serif",
        wordBreak: 'keep-all',
        background:
          'linear-gradient(#1D2020, #1D2020) padding-box, linear-gradient(currentColor, currentColor) border-box',
        backgroundRepeat: 'no-repeat',
        ...style,
      }}
    >
      <span className="safe-text-primary-700">{children}</span>
    </ReactEmailButton>
  )
}
