import React from 'react'
import { Head } from '@react-email/components'
import { BrandedFonts } from '../branded-fonts'
import { EmailBrand } from '../../constants/brand'

export const MarketingHead: React.FC<{
  children?: React.ReactNode
  brand: EmailBrand
}> = ({ children, brand }) => {
  return (
    <Head>
      <BrandedFonts brand={brand} />
      <meta name="color-scheme" content="dark light"/>
      <meta name="supported-color-schemes" content="dark light"/>
      {/* no inline CSS needed for text color hack; handled via Tailwind plugin */}

      {children}
    </Head>
  )
}


