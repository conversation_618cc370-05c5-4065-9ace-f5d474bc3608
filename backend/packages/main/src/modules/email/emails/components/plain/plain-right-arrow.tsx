import { Img } from '@react-email/components'
import React, { ComponentProps } from 'react'

export const PlainRightArrow: React.FC<ComponentProps<typeof Img>> = (props) => {
  const { className, style, ...rest } = props

  return (
    <Img
      src="https://clash.gg/assets/common/emails/right-arrow.png"
      alt="Right Arrow"
      style={{
        width: '12px',
        height: '12px',
        marginLeft: '8px',
        verticalAlign: 'middle',
        display: 'inline-block',
        ...style,
      }}
      {...rest}
    />
  )
}
