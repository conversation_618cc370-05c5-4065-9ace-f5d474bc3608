import React from 'react'
import { Font } from '@react-email/components'
import { EmailBrand } from '../constants/brand'

interface Font {
  weight: string
  fontFamily: string
  path: string
  style: string
}

const BAI_JAMJUREE_FONTS: Font[] = [
  {
    weight: '400',
    fontFamily: 'BaiJamjuree',
    path: 'https://clash.gg/assets/common/fonts/BaiJamjuree/BaiJamjuree-Regular.woff2',
    style: 'normal',
  },
  {
    weight: '500',
    fontFamily: 'BaiJamjuree',
    path: 'https://clash.gg/assets/common/fonts/BaiJamjuree/BaiJamjuree-Medium.woff2',
    style: 'normal',
  },
  {
    weight: '600',
    fontFamily: 'BaiJamjuree',
    path: 'https://clash.gg/assets/common/fonts/BaiJamjuree/BaiJamjuree-SemiBold.woff2',
    style: 'normal',
  },
  {
    weight: '700',
    fontFamily: 'BaiJamjuree',
    path: 'https://clash.gg/assets/common/fonts/BaiJamjuree/BaiJamjuree-Bold.woff2',
    style: 'normal',
  },
]

const WIGRUM_FONTS: Font[] = [
  // { weight: '100', fontFamily: 'Wigrum', path: 'https://cases.gg/assets/fonts/Wigrum-Thin.woff2', style: 'normal' },
  // { weight: '100', fontFamily: 'Wigrum', path: 'https://cases.gg/assets/fonts/Wigrum-ThinItalic.woff2', style: 'italic' },
  // { weight: '200', fontFamily: 'Wigrum', path: 'https://cases.gg/assets/fonts/Wigrum-ExtraLight.woff2', style: 'normal' },
  // { weight: '200', fontFamily: 'Wigrum', path: 'https://cases.gg/assets/fonts/Wigrum-ExtraLightItalic.woff2', style: 'italic' },
  // { weight: '300', fontFamily: 'Wigrum', path: 'https://cases.gg/assets/fonts/Wigrum-Light.woff2', style: 'normal' },
  // { weight: '300', fontFamily: 'Wigrum', path: 'https://cases.gg/assets/fonts/Wigrum-LightItalic.woff2', style: 'italic' },
  {
    weight: '400',
    fontFamily: 'Wigrum',
    path: 'https://cases.gg/assets/fonts/Wigrum-Regular.woff2',
    style: 'normal',
  },
  // { weight: '400', fontFamily: 'Wigrum', path: 'https://cases.gg/assets/fonts/Wigrum-Italic.woff2', style: 'italic' },
  {
    weight: '500',
    fontFamily: 'Wigrum',
    path: 'https://cases.gg/assets/fonts/Wigrum-Medium.woff2',
    style: 'normal',
  },
  // { weight: '500', fontFamily: 'Wigrum', path: 'https://cases.gg/assets/fonts/Wigrum-MediumItalic.woff2', style: 'italic' },
  {
    weight: '700',
    fontFamily: 'Wigrum',
    path: 'https://cases.gg/assets/fonts/Wigrum-Bold.woff2',
    style: 'normal',
  },
  // { weight: '700', fontFamily: 'Wigrum', path: 'https://cases.gg/assets/fonts/Wigrum-BoldItalic.woff2', style: 'italic' },
  {
    weight: '900',
    fontFamily: 'Wigrum',
    path: 'https://cases.gg/assets/fonts/Wigrum-Black.woff2',
    style: 'normal',
  },
  // { weight: '900', fontFamily: 'Wigrum', path: 'https://cases.gg/assets/fonts/Wigrum-BlackItalic.woff2', style: 'italic' },
]

const BRANDED_FONTS: Record<EmailBrand, Font[]> = {
  csgo: BAI_JAMJUREE_FONTS,
  rust: BAI_JAMJUREE_FONTS,
  common: WIGRUM_FONTS,
}

interface BrandedFontsProps {
  brand: EmailBrand
}

export const BrandedFonts = ({ brand }: BrandedFontsProps) => {
  const fonts = BRANDED_FONTS[brand]

  return (
    <>
      {fonts?.map((font) => (
        <Font
          key={font.weight}
          fontFamily={font.fontFamily}
          fallbackFontFamily="sans-serif"
          webFont={{
            format: 'woff2',
            url: font.path,
          }}
          fontWeight={font.weight}
          fontStyle={font.style}
        />
      ))}
    </>
  )
}
