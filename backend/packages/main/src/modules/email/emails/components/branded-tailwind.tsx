import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON> } from '../constants/brand'
import { Tailwind } from '@react-email/components'
import { getPrimaryColorShades } from '../helpers/helpers'
import { deepmerge } from 'deepmerge-ts'

type TailwindConfig = React.ComponentProps<typeof Tailwind>['config']

export const PRIMARY_COLOR_SHADE_KEYS = [
  'gradient-dark',
  'gradient-light',
  950,
  900,
  700,
  500,
  300,
  100,
] as const

export type PrimaryColorShades = Record<
  (typeof PRIMARY_COLOR_SHADE_KEYS)[number],
  string
>

const brandConfig: Record<EmailBrand, TailwindConfig> &
  Record<'custom', (primaryColor: string) => TailwindConfig> = {
  csgo: {
    theme: {
      extend: {
        colors: {
          primary: {
            'gradient-dark': '#FF8D24',
            'gradient-light': '#FFC700',
            950: '#663300',
            900: '#EB9A15',
            700: '#FFA510',
            500: '#FFC701',
            300: '#FFD819',
            100: '#FFE45E',
          },
        },
      },
    },
  },
  rust: {
    theme: {
      extend: {
        colors: {
          primary: {
            'gradient-dark': '#8140b2',
            'gradient-light': '#b059e8',
            950: '#400060',
            900: '#682b97',
            700: '#8d46d1',
            500: '#a351e2',
            300: '#b059e8',
            100: '#d269ff',
          },
        },
      },
    },
  },
  common: {
    theme: {
      extend: {
        colors: {
          primary: {
            'gradient-dark': '#2D8CB0',
            'gradient-light': '#6FCBEF',
            950: '#003340',
            900: '#1A5368',
            700: '#2D8CB0',
            500: '#4DB5DA',
            300: '#6FCBEF',
            100: '#A7E0F7',
          },
        },
      },
    },
  },
  custom: (primaryColor: string): TailwindConfig => {
    const primary = getPrimaryColorShades(primaryColor)

    return {
      theme: {
        extend: {
          colors: {
            primary,
          },
        },
      },
    }
  },
}

const defaultConfig: TailwindConfig = {
  theme: {
    extend: {
      colors: {
        gray: {
          900: '#151719',
          700: '#1d2020',
          500: '#242829',
          300: '#353a3b',
          250: '#5b5c5e',
          200: '#8f9092',
          100: '#c7c8c8',
        },
        offwhite: '#f8f8f8',
      },
    },
  },
  plugins: [
    // Gmail likes to invert colors. This plugin prevents that.
    function safeTextPlugin({ matchUtilities, theme }: any) {
      const flattenColorPalette = (colors: Record<string, any>) => {
        const flattened: Record<string, string> = {}
        for (const [colorName, colorValue] of Object.entries(colors || {})) {
          if (colorValue && typeof colorValue === 'object') {
            for (const [shade, hex] of Object.entries(colorValue)) {
              if (typeof hex === 'string') {
                flattened[`${colorName}-${shade}`] = hex
              }
            }
          } else if (typeof colorValue === 'string') {
            flattened[colorName] = colorValue
          }
        }
        return flattened
      }

      matchUtilities(
        {
          // Replaces the default `.text-*`
          'safe-text': (value: string) => {
            return {
              // Keep the actual color in a var for reuse
              '--tw-text-color': String(value),

              // The hack: paint the text with a gradient of the same color
              backgroundImage:
                'linear-gradient(var(--tw-text-color), var(--tw-text-color))',

              // Clip background to glyphs
              backgroundClip: 'text',
              WebkitBackgroundClip: 'text',

              // Make the real text color transparent so Gmail can't invert it
              color: '#FFFFFF01 !important',
              WebkitTextFillColor: '#FFFFFF01 !important',

              // (Optional but harmless) ensure no stray bg props interfere
              backgroundRepeat: 'no-repeat',
            }
          }
        },
        {
          // Support theme colors and arbitrary values (e.g. text-[#1D2020])
          values: flattenColorPalette(theme('colors')),
          type: 'color',
        }
      )
    },
  ],
}

export const BrandedTailwind: React.FC<{
  brand: EmailBrand
  primaryColor?: string
  children: React.ReactNode
}> = ({ brand, children, primaryColor }) => {
  const mergedConfig = deepmerge(
    defaultConfig,
    primaryColor ? brandConfig.custom(primaryColor) : brandConfig[brand]
  )

  return <Tailwind config={mergedConfig}>{children}</Tailwind>
}
