import React from 'react'
import { Body, Container } from '@react-email/components'

interface MarketingBodyProps {
  children: React.ReactNode
}

export const MarketingBody: React.FC<MarketingBodyProps> = ({ children }) => {
  return (
    <Body
      className="body bg-gray-700 m-0 p-0"
      style={{
        backgroundImage: 'linear-gradient(#1D2020, #1D2020)',
      }}
    >
      <Container
        className="max-w-[650px] overflow-hidden p-2"
        style={{
          fontFamily: 'Arial, Helvetica, sans-serif',
        }}
      >
        {children}
      </Container>
    </Body>
  )
}
