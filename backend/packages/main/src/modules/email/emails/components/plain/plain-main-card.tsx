import React from 'react'
import { Container, Img, Link } from '@react-email/components'
import { isBrand } from '../../constants/brand'
import { EmailProps } from '../../constants/types'

interface PlainMainCardProps extends EmailProps {
  children: React.ReactNode
  imageFilename?: string
  imageAlt?: string
  unsubscribeLink: string
  className?: string
}

export const PlainMainCard: React.FC<PlainMainCardProps> = ({
  children,
  brand,
  imageFilename,
  imageAlt,
  unsubscribeLink,
  className,
  clientUrl,
  siteName,
  legalEntity,
}) => {
  return (
    <Container>
      <div
        className={`bg-white rounded-xl overflow-hidden mx-2 mt-6 ${
          isBrand(brand, ['csgo', 'rust']) ? 'font-medium' : ''
        } ${className}`}
        style={{
          border: '1px solid #e0e0e0',
          boxShadow: '0 0 10px 0 rgba(0, 0, 0, 0.08)',
        }}
      >
        {imageFilename ? (
          <Img
            src={`${clientUrl}/assets/${brand}/emails/${imageFilename}`}
            alt={imageAlt}
            className="max-w-full"
          />
        ) : (
          <div
            className="w-full py-6 sm:py-8"
            style={{
              background: 'linear-gradient(to bottom, #1d2020, #1d2020)',
            }}
          >
            <Img
              src={
                isBrand(brand, ['csgo', 'rust'])
                  ? `${clientUrl}/assets/${brand}/branding/logo-full.png`
                  : `${clientUrl}/assets/branding/logo.png`
              }
              alt={`${siteName} logo`}
              className={`h-auto mx-5 sm:mx-8 ${
                isBrand(brand, ['csgo', 'rust'])
                  ? 'max-w-[min(70%,160px)] sm:max-w-[min(70%,300px)]'
                  : 'max-w-[min(70%,120px)] sm:max-w-[min(70%,200px)]'
              }`}
            />
          </div>
        )}

        <div className="p-4 sm:p-6 pt-2 sm:pt-4 flex flex-col items-stretch">
          <Container>{children}</Container>
        </div>
      </div>

      <div className="text-center text-sm text-gray-300 mt-6 px-4">
        Brought to you by <span className="font-semibold">{legalEntity}</span>
      </div>

      <div className="text-center text-xs font-semibold mt-1 mb-8 px-4">
        {unsubscribeLink && (
          <Link href={unsubscribeLink} className="text-primary-900">
            Unsubscribe
          </Link>
        )}
      </div>
    </Container>
  )
}
