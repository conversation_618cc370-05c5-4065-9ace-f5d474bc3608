import React from 'react'
import { Img, Section } from '@react-email/components'
import { EmailBrand } from '../../constants/brand'
import { MarketingGmailSafeHr } from './marketing-gmail-safe-hr'

interface MarketingHeaderProps {
  brand: EmailBrand
  clientUrl: string
}

export const MarketingHeader: React.FC<MarketingHeaderProps> = ({
  brand,
  clientUrl,
}) => {
  const baseImageUrl = `${clientUrl}/assets/${brand}/emails/welcome-1`

  return (
    <Section className="py-1">
      <Img
        src={`${baseImageUrl}/LOGO.png`}
        width="346"
        alt="Logo"
        className="mx-auto my-5 max-w-full"
      />

      <MarketingGmailSafeHr className="my-5" />
    </Section>
  )
}
