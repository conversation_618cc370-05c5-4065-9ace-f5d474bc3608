import React from 'react'
import { Heading, Img, Link, Section, Text } from '@react-email/components'
import { EmailBrand } from '../../constants/brand'
import { EmailProps } from '../../constants/types'
import { MarketingGmailSafeHr } from './marketing-gmail-safe-hr'

interface MarketingFooterProps extends EmailProps {
  brand: EmailBrand
  unsubscribeLink: string
}

export const MarketingFooter: React.FC<MarketingFooterProps> = ({
  brand,
  unsubscribeLink,
  clientUrl,
  siteName,
  legalEntity,
}) => {
  const baseImageUrl = `${clientUrl}/assets/${brand}/emails/welcome-1`

  return (
    <Section className="py-1">
      <MarketingGmailSafeHr className="my-5" />

      <Heading as="h3" className="safe-text-white text-2xl font-bold px-2.5">
        We'll see you there!
      </Heading>

      <Text className="text-base leading-tight px-2.5">
        <span className="safe-text-white">
          Have questions or feedback? Join the community or reach out to our
          support team anytime.
        </span>
        <br />
        <br />
        <span className="safe-text-white">Thanks for being part of the </span>
        <Link href={clientUrl} className="safe-text-primary-700 underline">
          {siteName}
        </Link>{' '}
        <span className="safe-text-white">family.</span>
        <br />
        <br />
        <strong>
          <span className="safe-text-white">The </span>
          <Link href={clientUrl} className="safe-text-primary-700 underline">
            {siteName}
          </Link>{' '}
          <span className="safe-text-white">Team</span>
        </strong>
      </Text>

      <Link href={`${clientUrl}/discord`}>
        <Img
          src={`${baseImageUrl}/6TH_-_DISCORD_CTA_BUTTON.png`}
          className="max-w-full"
          alt="Discord"
        />
      </Link>

      <MarketingGmailSafeHr className="my-5" />

      <Img
        src={`${baseImageUrl}/LOGO.png`}
        width="346"
        alt="Logo"
        className="mx-auto my-5 max-w-full"
      />

      <Text className="text-center text-base">
        <span className="safe-text-white">
          © All rights reserved 2021 - 2025{' '}
        </span>
        <Link href={clientUrl} className="safe-text-primary-700 underline">
          {siteName}
        </Link>
      </Text>

      <Text className="safe-text-gray-100 text-center text-base px-2.5">
        {siteName} is owned and operated by {legalEntity}
      </Text>

      <Text className="safe-text-gray-100 text-center text-base">
        <Link
          href={`mailto:support@${siteName}`}
          className="safe-text-primary-700 underline"
        >
          support@{siteName}
        </Link>
        <br />
        <Link
          href={unsubscribeLink}
          className="safe-text-primary-700 underline"
        >
          Unsubscribe
        </Link>
      </Text>
    </Section>
  )
}
