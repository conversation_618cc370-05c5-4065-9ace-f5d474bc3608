import React from 'react'
import {
  Column,
  Heading,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
} from '@react-email/components'
import { EmailProps } from '../../constants/types'
import { BrandedTailwind } from '../../components/branded-tailwind'
import { MarketingHead } from '../../components/marketing/marketing-head'
import { EXAMPLE_BRAND, EXAMPLE_CONTEXT } from '../../constants/example'
import { MarketingButton } from '../../components/marketing/marketing-button'
import { MarketingBody } from '../../components/marketing/marketing-body'
import { MarketingHeader } from '../../components/marketing/marketing-header'
import { MarketingFooter } from '../../components/marketing/marketing-footer'
import { MarketingGmailSafeHr } from '../../components/marketing/marketing-gmail-safe-hr'

export interface Marketing1stReminderFreeDailyCaseProps extends EmailProps {
  name: string
  unsubscribeLink: string
}

type Marketing1stReminderFreeDailyCaseEmailComponent =
  React.FC<Marketing1stReminderFreeDailyCaseProps> & {
    PreviewProps?: Marketing1stReminderFreeDailyCaseProps
  }

export const Marketing1stReminderFreeDailyCaseEmail: Marketing1stReminderFreeDailyCaseEmailComponent =
  (props) => {
    const { brand, name, clientUrl } = props
    const imageUrl = `${clientUrl}/assets/${brand}/emails/1st-reminder-free-daily-case`

    return (
      <BrandedTailwind brand={brand}>
        <Html>
          <MarketingHead brand={brand} />
          <Preview>Your daily free case is waiting</Preview>
          <MarketingBody>
            <MarketingHeader {...props} />
            <Section className="py-1">
              <Img
                src={`${imageUrl}/Clash-Marketing-Email-Banners-5Artboard-11.png`}
                className="max-w-full"
                alt="Header"
              />

              <Heading as="h3" className="text-2xl font-bold px-2.5">
                🎁{' '}
                <span className="safe-text-white">
                  Your daily free case is waiting
                </span>
              </Heading>

              <Text className="safe-text-white text-base leading-tight px-2.5">
                Hey {name}, Did you know you can unlock free cases?
                <br />
                <br />
                With an active deposit, you get access to a free case every 24
                hours. Open it before the timer resets and see what drops.
                <br />
                <br />
                If you’ve been meaning to log in, now’s the time.
              </Text>

              <Section className="text-center py-2.5">
                <MarketingButton href="https://clash.gg/welcome/r/ads">
                  UNLOCK MY FREE CASE
                </MarketingButton>
              </Section>

              <MarketingGmailSafeHr className="my-5" />

              <Img
                src={`${imageUrl}/PROFIT_CASES.png`}
                className="max-w-full"
                alt="Profit Cases"
              />
            </Section>

            <Section>
              <Row>
                <Column align="center" className="w-1/2 p-1">
                  <Heading
                    as="h1"
                    className="safe-text-white text-2xl sm:text-4xl"
                  >
                    Rags 2 Rags
                  </Heading>
                  <MarketingButton href="https://clash.gg/casescs2/Rags-2-Rags">
                    UNBOX NOW
                  </MarketingButton>
                </Column>
                <Column align="center" className="w-1/2 p-1">
                  <Img
                    src={`${clientUrl}/assets/${brand}/emails/welcome-1/RAGS_TO_RAGS.png`}
                    width="218"
                    className="max-w-full"
                    alt="Rags to Rags"
                  />
                </Column>
              </Row>
              <Row>
                <Column align="center" className="w-1/2 p-1">
                  <Img
                    src={`${clientUrl}/assets/${brand}/emails/welcome-1/10_SNOWBALL.png`}
                    width="204"
                    className="max-w-full"
                    alt="10% Snowball"
                  />
                </Column>
                <Column align="center" className="w-1/2 p-1">
                  <Heading
                    as="h1"
                    className="safe-text-white text-2xl sm:text-4xl"
                  >
                    10% SNOWBALL
                  </Heading>
                  <MarketingButton href="https://clash.gg/casescs2/10percent-Snowball">
                    UNBOX NOW
                  </MarketingButton>
                </Column>
              </Row>
              <Row>
                <Column align="center" className="w-1/2 p-1">
                  <Heading
                    as="h1"
                    className="safe-text-white text-2xl sm:text-4xl"
                  >
                    BLACK OPS
                  </Heading>
                  <MarketingButton href="https://clash.gg/casescs2/Black-Ops">
                    UNBOX NOW
                  </MarketingButton>
                </Column>
                <Column align="center" className="w-1/2 p-1">
                  <Img
                    src={`${clientUrl}/assets/${brand}/emails/welcome-1/BLACK_OPS.png`}
                    width="218"
                    className="max-w-full"
                    alt="Black Ops"
                  />
                </Column>
              </Row>
            </Section>
            <MarketingFooter {...props} />
          </MarketingBody>
        </Html>
      </BrandedTailwind>
    )
  }

Marketing1stReminderFreeDailyCaseEmail.PreviewProps = {
  ...EXAMPLE_CONTEXT,
  name: 'John Doe',
  brand: EXAMPLE_BRAND,
  unsubscribeLink: 'https://example.com/unsubscribe',
} satisfies Marketing1stReminderFreeDailyCaseProps

export default Marketing1stReminderFreeDailyCaseEmail
