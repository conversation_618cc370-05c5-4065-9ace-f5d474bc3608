import React from 'react'
import {
  Heading,
  Html,
  Img,
  Preview,
  Section,
  Text,
} from '@react-email/components'
import { EmailProps } from '../../constants/types'
import { BrandedTailwind } from '../../components/branded-tailwind'
import { MarketingHead } from '../../components/marketing/marketing-head'
import { EXAMPLE_BRAND, EXAMPLE_CONTEXT } from '../../constants/example'
import { MarketingButton } from '../../components/marketing/marketing-button'
import { MarketingBody } from '../../components/marketing/marketing-body'
import { MarketingHeader } from '../../components/marketing/marketing-header'
import { MarketingFooter } from '../../components/marketing/marketing-footer'

export interface MarketingReusableAnnouncementProps extends EmailProps {
  name: string
  unsubscribeLink: string
}

type MarketingReusableAnnouncementEmailComponent =
  React.FC<MarketingReusableAnnouncementProps> & {
    PreviewProps?: MarketingReusableAnnouncementProps
  }

export const MarketingReusableAnnouncementEmail: MarketingReusableAnnouncementEmailComponent =
  (props) => {
    const { brand, name, clientUrl } = props
    const imageUrl = `${clientUrl}/assets/${brand}/emails/reusable-announcement`

    return (
      <BrandedTailwind brand={brand}>
        <Html>
          <MarketingHead brand={brand} />
          <Preview>Clash.gg Announcement</Preview>
          <MarketingBody>
            <MarketingHeader {...props} />
            <Section className="py-1">
              <Img
                src={`${imageUrl}/Clash-Marketing-Email-Banners-6Artboard-11.png`}
                className="max-w-full"
                alt="Header"
              />

              <Heading as="h3" className="text-2xl font-bold px-2.5">
                📢{' '}
                <span className="safe-text-white">Clash.gg Announcement</span>
              </Heading>

              <Text className="safe-text-white text-base leading-tight px-2.5">
                Hey {name},
                <br />
                <br />
                We’re always working to level up your experience on Clash.gg -
                and today, we’ve got something new to share.
                <br />
                <br />
                Whether it’s a new feature, game mode, reward system, or
                platform improvement, you’ll always hear it here first.
                <br />
                <br />
                This is just the beginning. Stay tuned for even more updates
                coming your way soon.
              </Text>

              <ul
                className="safe-text-white text-base leading-loose pl-10 my-2.5"
                style={{ listStyleType: 'revert' }}
              >
                <li>Feature 1: [Short description]</li>
                <li>Feature 2: [Short description]</li>
                <li>Feature 3: [Short description]</li>
              </ul>

              <Section className="text-center py-2.5">
                <MarketingButton href="https://clash.gg/welcome/r/ads">
                  CHECK IT OUT
                </MarketingButton>
              </Section>
            </Section>
            <MarketingFooter {...props} />
          </MarketingBody>
        </Html>
      </BrandedTailwind>
    )
  }

MarketingReusableAnnouncementEmail.PreviewProps = {
  ...EXAMPLE_CONTEXT,
  name: 'John Doe',
  brand: EXAMPLE_BRAND,
  unsubscribeLink: 'https://example.com/unsubscribe',
} satisfies MarketingReusableAnnouncementProps

export default MarketingReusableAnnouncementEmail
