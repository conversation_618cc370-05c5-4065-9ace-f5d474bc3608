import React from 'react'
import {
  Column,
  Heading,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
} from '@react-email/components'
import { EmailProps } from '../../constants/types'
import { BrandedTailwind } from '../../components/branded-tailwind'
import { MarketingHead } from '../../components/marketing/marketing-head'
import { EXAMPLE_BRAND, EXAMPLE_CONTEXT } from '../../constants/example'
import { MarketingButton } from '../../components/marketing/marketing-button'
import { MarketingBody } from '../../components/marketing/marketing-body'
import { MarketingHeader } from '../../components/marketing/marketing-header'
import { MarketingFooter } from '../../components/marketing/marketing-footer'
import { MarketingGmailSafeHr } from '../../components/marketing/marketing-gmail-safe-hr'

export interface Welcome2Props extends EmailProps {
  name: string
  unsubscribeLink: string
}

type Welcome2EmailComponent = React.FC<Welcome2Props> & {
  PreviewProps?: Welcome2Props
}

export const Welcome2Email: Welcome2EmailComponent = (props) => {
  const { brand, name, clientUrl, siteName } = props
  const imageUrl = `${clientUrl}/assets/${brand}/emails/welcome-2`
  const baseImageUrl = `${clientUrl}/assets/${brand}/emails/welcome-1`

  return (
    <BrandedTailwind brand={brand}>
      <Html>
        <MarketingHead brand={brand} />
        <Preview>We've missed you at {siteName}!</Preview>
        <MarketingBody>
          <MarketingHeader {...props} />
          <Section className="bg-gray-700 py-1">
            <Img
              src={`${baseImageUrl}/1ST_-_HEADER_IMAGE.png`}
              className="max-w-full"
              alt="Header"
            />

            <Heading
              as="h3"
              className="safe-text-white text-2xl font-bold px-2.5"
            >
              Hey {name},
            </Heading>

            <Text className="safe-text-white text-base leading-tight px-2.5">
              Noticed it's been a little while since your last visit, everything
              alright?
              <br />
              <br />
              You've already taken the first step with signing up, but there's
              still plenty waiting for you. Your 5% bonus is still active, so if
              you're thinking about jumping back in, now's a solid time.
            </Text>

            <Section className="text-center py-2.5">
              <MarketingButton href="https://clash.gg/welcome/r/CLASH5">
                CLAIM 5% DEPOSIT
              </MarketingButton>
            </Section>

            <MarketingGmailSafeHr className="my-5" />

            <Img
              src={`${imageUrl}/5_bonus.png`}
              className="max-w-full"
              alt="5% Bonus"
            />

            <Heading
              as="h3"
              className="safe-text-white text-2xl font-bold p-2.5"
            >
              How to claim your bonus:
            </Heading>

            <ol
              className="safe-text-white text-base leading-loose pl-10 my-2.5"
              style={{ listStyleType: 'decimal' }}
            >
              <li>Click the link down below</li>
              <li>Get 3 free cases instantly</li>
              <li>Make your first deposit to unlock a 5% coin bonus</li>
              <li>Battle, try Crash and upgrade your inventory</li>
            </ol>

            <Section className="text-center py-2.5">
              <MarketingButton href="https://clash.gg/welcome/r/CLASH5">
                3 FREE CASES + 5% BONUS
              </MarketingButton>
            </Section>

            <MarketingGmailSafeHr className="my-5" />

            <Img
              src={`${imageUrl}/PROFIT_CASES.png`}
              className="max-w-full"
              alt="Profit Cases"
            />
          </Section>

          <Section>
            <Row>
              <Column align="center" className="w-1/2 p-1">
                <Heading as="h1" className="safe-text-white text-4xl">
                  Rags 2 Rags
                </Heading>
                <MarketingButton href="https://clash.gg/casescs2/Rags-2-Rags">
                  UNBOX NOW
                </MarketingButton>
              </Column>
              <Column align="center" className="w-1/2 p-1">
                <Img
                  src={`${baseImageUrl}/RAGS_TO_RAGS.png`}
                  width="218"
                  className="max-w-full"
                  alt="Rags to Rags"
                />
              </Column>
            </Row>
            <Row>
              <Column align="center" className="w-1/2 p-1">
                <Img
                  src={`${baseImageUrl}/10_SNOWBALL.png`}
                  width="204"
                  className="max-w-full"
                  alt="10% Snowball"
                />
              </Column>
              <Column align="center" className="w-1/2 p-1">
                <Heading as="h1" className="safe-text-white text-4xl">
                  10% SNOWBALL
                </Heading>
                <MarketingButton href="https://clash.gg/casescs2/10percent-Snowball">
                  UNBOX NOW
                </MarketingButton>
              </Column>
            </Row>
            <Row>
              <Column align="center" className="w-1/2 p-1">
                <Heading as="h1" className="safe-text-white text-4xl">
                  BLACK OPS
                </Heading>
                <MarketingButton href="https://clash.gg/casescs2/Black-Ops">
                  UNBOX NOW
                </MarketingButton>
              </Column>
              <Column align="center" className="w-1/2 p-1">
                <Img
                  src={`${baseImageUrl}/BLACK_OPS.png`}
                  width="218"
                  className="max-w-full"
                  alt="Black Ops"
                />
              </Column>
            </Row>
          </Section>

          <MarketingFooter {...props} />
        </MarketingBody>
      </Html>
    </BrandedTailwind>
  )
}

Welcome2Email.PreviewProps = {
  ...EXAMPLE_CONTEXT,
  name: 'John Doe',
  brand: EXAMPLE_BRAND,
  unsubscribeLink: 'https://example.com/unsubscribe',
} satisfies Welcome2Props

export default Welcome2Email
