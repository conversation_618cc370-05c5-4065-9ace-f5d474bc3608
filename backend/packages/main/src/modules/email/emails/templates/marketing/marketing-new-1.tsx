import React from 'react'
import {
  Column,
  Heading,
  Html,
  Img,
  Preview,
  Row,
  Section,
  Text,
} from '@react-email/components'
import { EmailProps } from '../../constants/types'
import { BrandedTailwind } from '../../components/branded-tailwind'
import { MarketingHead } from '../../components/marketing/marketing-head'
import { EXAMPLE_BRAND, EXAMPLE_CONTEXT } from '../../constants/example'
import { MarketingButton } from '../../components/marketing/marketing-button'
import { MarketingBody } from '../../components/marketing/marketing-body'
import { MarketingHeader } from '../../components/marketing/marketing-header'
import { MarketingFooter } from '../../components/marketing/marketing-footer'
import { MarketingGmailSafeHr } from '../../components/marketing/marketing-gmail-safe-hr'

export interface WelcomeNewProps extends EmailProps {
  name: string
  unsubscribeLink: string
}

type WelcomeNewEmailComponent = React.FC<WelcomeNewProps> & {
  PreviewProps?: WelcomeNewProps
}

export const WelcomeNewEmail: WelcomeNewEmailComponent = (props) => {
  const { brand, name, clientUrl } = props
  const imageUrl = `${clientUrl}/assets/${brand}/emails/welcome-1`

  return (
    <BrandedTailwind brand={brand}>
      <Html>
        <MarketingHead brand={brand} />
        <Preview>Welcome to Clash! Here are your free cases.</Preview>
        <MarketingBody>
          <MarketingHeader {...props} />
          <Section className="py-1">
            <Img
              src={`${imageUrl}/1ST_-_HEADER_IMAGE.png`}
              className="max-w-full"
              alt="Header"
            />

            <Heading
              as="h3"
              className="safe-text-white text-2xl font-bold px-2.5"
            >
              Welcome to Clash, {name}.
            </Heading>

            <Text className="text-base leading-tight px-2.5">
              <span className="safe-text-white">
                We're so glad that you're here!.
              </span>
              <br />
              <br />
              🎁{' '}
              <span className="safe-text-white">
                Get your 3 free cases no questions asked.
              </span>
              <br />
              <span className="safe-text-white">Try Your Luck. On Us.</span>
            </Text>

            <Section className="text-center py-2.5">
              <MarketingButton href="https://clash.gg/welcome/r/ads">
                TRY YOUR LUCK
              </MarketingButton>
            </Section>

            <MarketingGmailSafeHr className="my-5" />

            <Img
              src={`${imageUrl}/2ND_-_HOW_IT_WORKS.png`}
              className="max-w-full"
              alt="How it works"
            />

            <ol
              className="safe-text-white text-base leading-loose pl-10 my-2.5"
              style={{ listStyleType: 'decimal' }}
            >
              <li>Click the link down below</li>
              <li>Get 3 free cases instantly</li>
              <li>Open them to win CS2 Skins</li>
              <li>Deposit or continue playing with your free rewards!</li>
            </ol>

            <Section className="text-center py-2.5">
              <MarketingButton href="https://clash.gg/welcome/r/ads">
                GET 3 FREE CASES
              </MarketingButton>
            </Section>

            <MarketingGmailSafeHr className="my-5" />

            <Img
              src={`${imageUrl}/3RD_-_WHATS_INSIDE.jpg`}
              className="max-w-full"
              alt="What's inside"
            />

            <Text className="text-base leading-tight px-2.5">
              🎯{' '}
              <span className="safe-text-white">
                Guaranteed Drops: What you see is what you can get. You can sell
                or withdraw the skins that you get
              </span>
              <br />
              <br />
              💰 <span className="safe-text-white">Spin GEMS</span> 🔶{' '}
              <span className="safe-text-white">
                : Sell your unwanted skins for gems and try your luck on our
                other gamemodes.
              </span>
              <br />
              <br />
              🔓{' '}
              <span className="safe-text-white">
                Unlock Higher Tier Cases: Boost your rank and get access to
                better rewards.
              </span>
              <br />
              <br />
              💎{' '}
              <span className="safe-text-white">
                Get Free Rewards: Complete challenges, collect gems, and level
                up your account!.
              </span>
            </Text>

            <Section className="text-center py-2.5">
              <MarketingButton href="https://clash.gg/">
                PLAY NOW
              </MarketingButton>
            </Section>

            <MarketingGmailSafeHr className="my-5" />

            <Img
              src={`${imageUrl}/4TH_-_POPULAR_CASES.png`}
              className="max-w-full"
              alt="Popular Cases"
            />
          </Section>

          <Section>
            <Row>
              <Column align="center" className="w-1/2 p-1">
                <Heading
                  as="h1"
                  className="safe-text-white text-2xl sm:text-4xl"
                >
                  Rags 2 Rags
                </Heading>
                <MarketingButton href="https://clash.gg/casescs2/Rags-2-Rags">
                  UNBOX NOW
                </MarketingButton>
              </Column>
              <Column align="center" className="w-1/2 p-1">
                <Img
                  src={`${imageUrl}/RAGS_TO_RAGS.png`}
                  width="218"
                  className="max-w-full"
                  alt="Rags to Rags"
                />
              </Column>
            </Row>
            <Row>
              <Column align="center" className="w-1/2 p-1">
                <Img
                  src={`${imageUrl}/10_SNOWBALL.png`}
                  width="204"
                  className="max-w-full"
                  alt="10% Snowball"
                />
              </Column>
              <Column align="center" className="w-1/2 p-1">
                <Heading
                  as="h1"
                  className="safe-text-white text-2xl sm:text-4xl"
                >
                  10% SNOWBALL
                </Heading>
                <MarketingButton href="https://clash.gg/casescs2/10percent-Snowball">
                  UNBOX NOW
                </MarketingButton>
              </Column>
            </Row>
            <Row>
              <Column align="center" className="w-1/2 p-1">
                <Heading
                  as="h1"
                  className="safe-text-white text-2xl sm:text-4xl"
                >
                  BLACK OPS
                </Heading>
                <MarketingButton href="https://clash.gg/casescs2/Black-Ops">
                  UNBOX NOW
                </MarketingButton>
              </Column>
              <Column align="center" className="w-1/2 p-1">
                <Img
                  src={`${imageUrl}/BLACK_OPS.png`}
                  width="218"
                  className="max-w-full"
                  alt="Black Ops"
                />
              </Column>
            </Row>
          </Section>
          <Section>
            <Img
              src={`${imageUrl}/5TH_-_JOIN_THE_CLASH_COMMUNITY.png`}
              className="max-w-full"
              alt="Join the Clash Community"
            />

            <Text className="safe-text-white text-base leading-tight px-2.5">
              Connect with 200,000+ players in our Discord.
              <br />
              Get bonus codes, play with others, and flex your wins.
              <br />
              <br />
              You can withdraw any skin with multiple instant options such as
              Crypto, CS or even migrate your balance onto our IRL lootbox site:
              Cases.gg
            </Text>
          </Section>
          <MarketingFooter {...props} />
        </MarketingBody>
      </Html>
    </BrandedTailwind>
  )
}

WelcomeNewEmail.PreviewProps = {
  ...EXAMPLE_CONTEXT,
  name: 'John Doe',
  brand: EXAMPLE_BRAND,
  unsubscribeLink: 'https://example.com/unsubscribe',
} satisfies WelcomeNewProps

export default WelcomeNewEmail
