import React from 'react'
import { Heading, Html, Preview, Text } from '@react-email/components'
import { EmailProps } from '../../constants/types'
import { BrandedTailwind } from '../../components/branded-tailwind'
import { PlainMainCard } from '../../components/plain/plain-main-card'
import { EXAMPLE_CONTEXT } from '../../constants/example'
import { PlainHead } from '../../components/plain/plain-head'
import { PlainBody } from '../../components/plain/plain-body'

interface OrderUpdateProps extends EmailProps {
  name: string
  orderId: string
  status: string
  itemName: string
  itemImage?: string
  trackingId?: string
  note?: string
  orderDate: string
  firstName: string
  lastName: string
  address1: string
  address2?: string
  city: string
  state: string
  zipCode: string
  country: string
  email: string
  phoneNumber: string
  unsubscribeLink: string
}

export type OrderUpdateEmailComponent = React.FC<OrderUpdateProps> & {
  PreviewProps?: OrderUpdateProps
}

export const OrderUpdateEmail: OrderUpdateEmailComponent = (props) => {
  const {
    brand,
    name,
    orderId,
    status,
    itemName,
    trackingId,
    note,
    orderDate,
    firstName,
    lastName,
    address1,
    address2,
    city,
    state,
    zipCode,
    country,
    email,
    phoneNumber,
    unsubscribeLink,
    primaryColor,
  } = props

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  return (
    <BrandedTailwind brand={brand} primaryColor={primaryColor}>
      <Html>
        <PlainHead brand={brand} />

        <PlainBody>
          <Preview>Your order status has been updated</Preview>

          <PlainMainCard {...props} unsubscribeLink={unsubscribeLink}>
            <Heading className="text-xl mb-0">Hello {name},</Heading>

            <Text className="mt-0">Your order status has been updated.</Text>

            <Text className="mt-0 mb-2">
              Order {orderId} is now marked as <strong>{status}</strong>.
            </Text>

            <Text className="mt-0 mb-4">
              Below are the details of your order:
            </Text>

            {/* Item Details Section */}
            <div className="mb-6">
              <Heading className="text-lg mb-3 mt-0">Item Details:</Heading>

              <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <div className="mb-3">
                  <Text className="text-base font-semibold mb-2 mt-0">
                    {itemName}
                  </Text>
                </div>

                <div className="space-y-2 text-sm">
                  <div>
                    <strong>Tracking ID:</strong>{' '}
                    {trackingId || 'To be provided'}
                  </div>

                  {note && (
                    <div>
                      <strong>Note:</strong> {note}
                    </div>
                  )}

                  <div>
                    <strong>Order Date:</strong> {formatDate(orderDate)}
                  </div>

                  <div>
                    <strong>Status:</strong> {status}
                  </div>
                </div>
              </div>
            </div>

            {/* Shipping Details Section */}
            <div className="mb-4">
              <Heading className="text-lg mb-3 mt-0">Shipping address:</Heading>

              <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <div className="space-y-1 text-sm">
                  <div>
                    {firstName} {lastName}
                  </div>
                  <div>{address1}</div>
                  {address2 && <div>{address2}</div>}
                  <div>
                    {city}, {state} {zipCode} {country}
                  </div>
                  <div>{email}</div>
                  <div>{phoneNumber}</div>
                </div>
              </div>
            </div>

            <Text className="mt-4">
              If you have any questions, feel free to contact our support team.
            </Text>
          </PlainMainCard>
        </PlainBody>
      </Html>
    </BrandedTailwind>
  )
}

OrderUpdateEmail.PreviewProps = {
  ...EXAMPLE_CONTEXT,
  name: 'John Doe',
  orderId: 'ORD123456',
  status: 'Shipped',
  itemName: 'Gaming Headset Pro',
  trackingId: 'TRACK123456789',
  note: 'Handle with care',
  orderDate: '2024-01-15',
  firstName: 'John',
  lastName: 'Doe',
  address1: '123 Main Street',
  address2: 'Apt 4B',
  city: 'New York',
  state: 'NY',
  zipCode: '10001',
  country: 'United States',
  email: '<EMAIL>',
  phoneNumber: '+****************',
  unsubscribeLink: 'https://example.com/unsubscribe',
} satisfies OrderUpdateProps

export default OrderUpdateEmail
