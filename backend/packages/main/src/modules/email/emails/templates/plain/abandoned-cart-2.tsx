import React from 'react'
import { Heading, Html, Preview, Text } from '@react-email/components'
import { isBrand } from '../../constants/brand'
import { EmailProps } from '../../constants/types'
import { BrandedTailwind } from '../../components/branded-tailwind'
import { PlainArrowButton } from '../../components/plain/plain-arrow-button'
import { PlainMainCard } from '../../components/plain/plain-main-card'
import { PlainCode } from '../../components/plain/plain-code'
import { PlainHead } from '../../components/plain/plain-head'
import { PlainBody } from '../../components/plain/plain-body'
import { EXAMPLE_CONTEXT } from '../../constants/example'

export interface AbandonedCart2Props extends EmailProps {
  name: string
  unsubscribeLink: string
}

type AbandonedCart2EmailComponent = React.FC<AbandonedCart2Props> & {
  PreviewProps?: AbandonedCart2Props
}

export const AbandonedCart2Email: AbandonedCart2EmailComponent = (props) => {
  const { brand, name, unsubscribeLink, clientUrl, primaryColor } = props

  return (
    <BrandedTailwind brand={brand} primaryColor={primaryColor}>
      <Html>
        <PlainHead brand={brand} />

        <PlainBody>
          <Preview>Forgot to finish your Deposit?</Preview>

          <PlainMainCard
            {...props}
            imageFilename={
              isBrand(brand, 'csgo') ? 'abandoned-cart-2.png' : undefined
            }
            imageAlt="Forgot to finish your Deposit?"
            unsubscribeLink={unsubscribeLink}
          >
            <Heading className="text-xl mb-0">Hey {name},</Heading>

            <Text className="mt-0">
              It's been almost a day and we see that you haven't purchased any
              gems yet.
            </Text>

            <Text className="mt-0">
              Just a quick reminder that there is still time to finalize your
              deposit. We are also offering a 5% deposit bonus on all methods,
              simply use code <PlainCode>CLASH5</PlainCode> at checkout.
            </Text>

            <PlainArrowButton
              brand={brand}
              href={`${clientUrl}/deposit`}
              className="self-center mt-2 mb-2 px-6 rounded-md"
              style={{
                border: '1px solid #3D863F38',
              }}
            >
              Check it out
            </PlainArrowButton>
          </PlainMainCard>
        </PlainBody>
      </Html>
    </BrandedTailwind>
  )
}

AbandonedCart2Email.PreviewProps = {
  ...EXAMPLE_CONTEXT,
  name: 'John Doe',
  unsubscribeLink: 'https://example.com/unsubscribe',
} satisfies AbandonedCart2Props

export default AbandonedCart2Email
