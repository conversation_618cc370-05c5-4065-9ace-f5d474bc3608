import React from 'react'
import { Heading, Html, Link, Preview, Text } from '@react-email/components'
import { EmailProps } from '../../constants/types'
import { BrandedTailwind } from '../../components/branded-tailwind'
import { PlainArrowButton } from '../../components/plain/plain-arrow-button'
import { PlainMainCard } from '../../components/plain/plain-main-card'
import { PlainHead } from '../../components/plain/plain-head'
import { PlainBody } from '../../components/plain/plain-body'
import { EXAMPLE_CONTEXT } from '../../constants/example'

export interface ResetPasswordProps extends EmailProps {
  resetLink: string
  unsubscribeLink: string
}

type ResetPasswordEmailComponent = React.FC<ResetPasswordProps> & {
  PreviewProps?: ResetPasswordProps
}

export const ResetPasswordEmail: ResetPasswordEmailComponent = (props) => {
  const { brand, resetLink, unsubscribeLink, primaryColor } = props

  return (
    <BrandedTailwind brand={brand} primaryColor={primaryColor}>
      <Html>
        <PlainHead brand={brand} />

        <PlainBody>
          <Preview>Reset your password</Preview>

          <PlainMainCard {...props} unsubscribeLink={unsubscribeLink}>
            <Heading className="text-xl mb-0">Hello!</Heading>

            <Text className="mt-0">
              Please click the following button to reset your password:
            </Text>

            <PlainArrowButton
              brand={brand}
              href={resetLink}
              className="self-center mt-2 mb-2 px-6 rounded-md"
              style={{
                border: '1px solid #3D863F38',
              }}
            >
              Reset my password
            </PlainArrowButton>

            <Text className="mt-4 text-sm text-gray-600">
              If the button above does not work, paste this link into your web
              browser:
              <br />
              <Link
                href={resetLink}
                className="text-primary-600 underline break-all"
              >
                {resetLink}
              </Link>
            </Text>

            <Text className="mt-4 text-sm text-gray-600">
              If you did not attempt to reset your password, simply ignore this
              message. The link is only valid for 1 hour.
            </Text>
          </PlainMainCard>
        </PlainBody>
      </Html>
    </BrandedTailwind>
  )
}

ResetPasswordEmail.PreviewProps = {
  ...EXAMPLE_CONTEXT,
  resetLink: 'https://example.com/reset?token=abc123def456',
  unsubscribeLink: 'https://example.com/unsubscribe',
} satisfies ResetPasswordProps

export default ResetPasswordEmail
