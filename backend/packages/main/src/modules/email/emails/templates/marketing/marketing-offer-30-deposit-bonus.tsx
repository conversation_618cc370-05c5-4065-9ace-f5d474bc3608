import React from 'react'
import {
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components'
import { EmailProps } from '../../constants/types'
import { BrandedTailwind } from '../../components/branded-tailwind'
import { MarketingHead } from '../../components/marketing/marketing-head'
import { EXAMPLE_BRAND, EXAMPLE_CONTEXT } from '../../constants/example'
import { MarketingButton } from '../../components/marketing/marketing-button'
import { MarketingBody } from '../../components/marketing/marketing-body'
import { MarketingHeader } from '../../components/marketing/marketing-header'
import { MarketingFooter } from '../../components/marketing/marketing-footer'
import { MarketingGmailSafeHr } from '../../components/marketing/marketing-gmail-safe-hr'

export interface MarketingOffer30DepositBonusProps extends EmailProps {
  name: string
  unsubscribeLink: string
  promoCode: string
}

type MarketingOffer30DepositBonusEmailComponent =
  React.FC<MarketingOffer30DepositBonusProps> & {
    PreviewProps?: MarketingOffer30DepositBonusProps
  }

export const MarketingOffer30DepositBonusEmail: MarketingOffer30DepositBonusEmailComponent =
  (props) => {
    const { brand, name, clientUrl, siteName, promoCode } = props
    const imageUrl = `${clientUrl}/assets/${brand}/emails/welcome-3`
    const image30Url = `${clientUrl}/assets/${brand}/emails/offer-30-deposit-bonus`

    return (
      <BrandedTailwind brand={brand}>
        <Html>
          <MarketingHead brand={brand} />
          <Preview>Your limited-time offer from {siteName} is here!</Preview>
          <MarketingBody>
            <MarketingHeader {...props} />
            <Section className="py-1">
              <Img
                src={`${imageUrl}/limited_time.png`}
                className="max-w-full"
                alt="Limited Time Offer"
              />

              <Heading as="h3" className="text-2xl font-bold px-2.5">
                <span className="safe-text-white">Hey {name}, Your</span> 🔶{' '}
                <span className="safe-text-white">Bonus is waiting!</span>
              </Heading>

              <Text className="text-base leading-tight px-2.5">
                <span className="safe-text-white">
                  For a short time, new users get 3 free cases just for signing
                  up plus a 30% bonus when you make your first deposit.
                </span>
                <br />
                <br />
                <span className="safe-text-white">
                  This offer won't last long. Claim your free cases and bonus
                  coins before the timer runs out.
                </span>
                <br />
                <br />
                🎁 <span className="safe-text-white">3 Free Cases</span>
                <br />
                💰{' '}
                <span className="safe-text-white">
                  30% Extra gems on First Deposit
                </span>
                <br />⏰{' '}
                <span className="safe-text-white">
                  Limited Time Offer - 24 Hours from when you see this!
                </span>
              </Text>

              <Section className="text-center py-2.5">
                <MarketingButton
                  href={`https://clash.gg?bonus-code=${promoCode}`}
                >
                  CLAIM NOW
                </MarketingButton>
              </Section>

              <MarketingGmailSafeHr className="my-5" />

              <Img
                src={`${image30Url}/Clash-Marketing-Email-Banners-8Artboard-1_1.png`}
                className="max-w-full"
                alt="30% Deposit Bonus"
              />

              <Text className="text-base leading-tight px-2.5">
                <span className="safe-text-white">How does it work?:</span>
                <br />
                <br />
                <span className="safe-text-white">1. Deposit</span> 🔶{''}
                <span className="safe-text-white">50 You’ll get</span> 🔶{''}
                <span className="safe-text-white">65.</span>
                <span className="safe-text-white"></span>
                <br />
                <span className="safe-text-white">2. Deposit</span> 🔶 {''}
                <span className="safe-text-white">
                  100 You’re walking in with
                </span>{' '}
                🔶
                <span className="safe-text-white">130.</span>
                <br />
                <span className="safe-text-white">3. Go big with</span> 🔶 {''}
                <span className="safe-text-white">
                  300? You’ll start with a massive
                </span>{' '}
                🔶
                <span className="safe-text-white">390 to play with.</span>
              </Text>

              <Section className="text-center py-2.5">
                <MarketingButton
                  href={`https://clash.gg/welcome/r/ads?bonus-code=${promoCode}`}
                >
                  3 FREE CASES + 30% BONUS
                </MarketingButton>
              </Section>

              <MarketingGmailSafeHr className="my-5" />

              <Img
                src={`${imageUrl}/WHATS_INSIDE.png`}
                className="max-w-full"
                alt="What's Inside"
              />
            </Section>

            <Section className="py-1">
              <Text className="text-base leading-tight px-2.5 mb-4">
                🎯{' '}
                <span className="safe-text-white">
                  <strong>Guaranteed Drops</strong>
                  <br />
                  What you see is what you can win. No surprises.
                </span>
              </Text>
              <Text className="text-base leading-tight px-2.5 mb-4">
                💰{' '}
                <span className="safe-text-white">
                  <strong>Deposit with CS2 Skins</strong>
                  <br />
                  Top up your balance instantly using your CS2 skins.
                </span>
              </Text>
              <Text className="text-base leading-tight px-2.5 mb-4">
                🔓{' '}
                <span className="safe-text-white">
                  <strong>Unlock Higher Tier Cases</strong>
                  <br />
                  Level up to access premium cases and better rewards.
                </span>
              </Text>
              <Text className="text-base leading-tight px-2.5 mb-4">
                💎{' '}
                <span className="safe-text-white">
                  <strong>Get Free Rewards</strong>
                  <br />
                  Complete challenges, collect gems, and level up your account
                  to earn bonuses.
                </span>
              </Text>

              <Section className="text-center py-2.5">
                <MarketingButton href="https://clash.gg/">
                  PLAY NOW
                </MarketingButton>
              </Section>

              <MarketingGmailSafeHr className="my-5" />

              <Link href="https://discord.com/invite/clashgg">
                <Img
                  src={`${imageUrl}/3RD_-_SEE_WHAT_OTHERS_HAVE_WON.png`}
                  className="max-w-full"
                  alt="See what others have won"
                />
              </Link>

              <Text className="safe-text-white text-base leading-tight px-2.5">
                Check the live feed on site to see what players are winning in
                real time. From big pulls to unexpected wins, you'll get a live
                look at what's hitting across the platform.
                <br />
                <br />
                Want your name up there? Make your move.
              </Text>

              <Section className="text-center py-2.5">
                <MarketingButton href="https://clash.gg/welcome/r/ads">
                  MAKE YOUR MOVE
                </MarketingButton>
              </Section>
            </Section>
            <MarketingFooter {...props} />
          </MarketingBody>
        </Html>
      </BrandedTailwind>
    )
  }

MarketingOffer30DepositBonusEmail.PreviewProps = {
  ...EXAMPLE_CONTEXT,
  name: 'John Doe',
  brand: EXAMPLE_BRAND,
  unsubscribeLink: 'https://example.com/unsubscribe',
  promoCode: '81631be1-c78d-4c66-8c1b-54563aa1338a',
} satisfies MarketingOffer30DepositBonusProps

export default MarketingOffer30DepositBonusEmail
