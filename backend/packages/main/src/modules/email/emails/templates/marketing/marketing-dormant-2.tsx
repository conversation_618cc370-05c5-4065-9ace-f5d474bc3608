import React from 'react'
import {
  Heading,
  Html,
  Img,
  Preview,
  Section,
  Text,
} from '@react-email/components'
import { EmailProps } from '../../constants/types'
import { BrandedTailwind } from '../../components/branded-tailwind'
import { MarketingHead } from '../../components/marketing/marketing-head'
import { EXAMPLE_BRAND, EXAMPLE_CONTEXT } from '../../constants/example'
import { MarketingButton } from '../../components/marketing/marketing-button'
import { MarketingBody } from '../../components/marketing/marketing-body'
import { MarketingHeader } from '../../components/marketing/marketing-header'
import { MarketingFooter } from '../../components/marketing/marketing-footer'

export interface MarketingDormant2Props extends EmailProps {
  name: string
  unsubscribeLink: string
  promoCode: string
}

type MarketingDormant2EmailComponent = React.FC<MarketingDormant2Props> & {
  PreviewProps?: MarketingDormant2Props
}

export const MarketingDormant2Email: MarketingDormant2EmailComponent = (
  props
) => {
  const { brand, clientUrl, promoCode } = props
  const imageUrl = `${clientUrl}/assets/${brand}/emails/marketing-dormant-2`

  return (
    <BrandedTailwind brand={brand}>
      <Html>
        <MarketingHead brand={brand} />
        <Preview>Everyone has days off, But you dissapeared</Preview>
        <MarketingBody>
          <MarketingHeader {...props} />
          <Section className="py-1">
            <Img
              src={`${imageUrl}/2_HOWS_THINGS.png`}
              className="max-w-full"
              alt="Header"
            />

            <Heading as="h3" className="text-2xl font-bold px-2.5">
              <span className="safe-text-white">
                Everyone has days off, But you dissapeared
              </span>
            </Heading>

            <Text className="safe-text-white text-base leading-tight px-2.5">
              It’s been a little while since we last saw you on Clash.gg, and
              honestly, it feels like something’s been missing. <br />
              <br />
              The rain hasnt stopped pouring and the battles are as intense as
              ever - but your name hasn’t been in the mix.
              <br />
              <br />
              We don’t want you to miss out on the action, so we’re giving you a
              reason to jump back in. Right now, you can claim a{' '}
              <strong>50% deposit bonus</strong> to boost your balance and get
              straight back to cracking cases, spinning wheels, and chasing
              those top-tier wins.
            </Text>

            <Img
              src={`${imageUrl}/3_Clash-Marketing-Email-Banners-10Artboard-21.png`}
              className="max-w-full"
              alt="Bonus"
            />

            <Heading as="h3" className="text-2xl font-bold px-2.5">
              <span className="safe-text-white">A bonus fit for VIP's</span>
            </Heading>

            <Text className="safe-text-white text-base leading-tight px-2.5">
              When you top-up <strong>today</strong>, we’ll stack an extra{' '}
              <strong>50%</strong> onto your deposit instantly. That means more
              balance to play with and more chances to walk away with skins
              worth showing off.
              <br />
              <br />
              This isn’t just a small perk that we give to everyone - it’s your
              invitation back into the game.Don’t wait too long, though. Once
              this bonus is gone, it’s gone for good, and we’d hate for you to
              miss your chance to return on a high note.
            </Text>

            <Section className="text-center py-2.5">
              <MarketingButton
                href={`https://clash.gg?bonus-code=${promoCode}`}
              >
                CLAIM YOUR 50% DEPOSIT BONUS
              </MarketingButton>
            </Section>
          </Section>
          <MarketingFooter {...props} />
        </MarketingBody>
      </Html>
    </BrandedTailwind>
  )
}

MarketingDormant2Email.PreviewProps = {
  ...EXAMPLE_CONTEXT,
  name: 'John Doe',
  brand: EXAMPLE_BRAND,
  unsubscribeLink: 'https://example.com/unsubscribe',
  promoCode: '81631be1-c78d-4c66-8c1b-54563aa1338a',
} satisfies MarketingDormant2Props

export default MarketingDormant2Email
