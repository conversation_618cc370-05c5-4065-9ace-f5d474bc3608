import React from 'react'
import {
  Column,
  Container,
  Heading,
  Hr,
  Html,
  Link,
  Preview,
  Row,
  Text,
} from '@react-email/components'
import { EmailProps } from '../../constants/types'
import { BrandedTailwind } from '../../components/branded-tailwind'
import { PlainMainCard } from '../../components/plain/plain-main-card'
import { PlainHead } from '../../components/plain/plain-head'
import { PlainBody } from '../../components/plain/plain-body'
import { maskText } from '../../helpers/helpers'
import { EXAMPLE_CONTEXT } from '../../constants/example'

export interface WithdrawalProps extends EmailProps {
  name: string
  amountUsd: number
  provider: string
  additionalText?: string
  fireblocksExplorerLink?: string
  fireblocksReceiverAddress?: string
  fireblocksTransactionHash?: string
  paypalReceiver?: string
  transferBalanceReceiverId?: string
  masspayMethod?: 'NORMAL' | 'EXPRESS' | 'RTP' | 'ZELLE'
  bankAccountNumber?: string
  bankRoutingNumber?: string
  bankAccountType?: string
  zelleAccount?: string
  unsubscribeLink: string
}

export const WithdrawalEmail = (props: WithdrawalProps) => {
  const {
    brand,
    name,
    amountUsd,
    provider,
    additionalText,
    fireblocksExplorerLink,
    fireblocksReceiverAddress,
    fireblocksTransactionHash,
    paypalReceiver,
    transferBalanceReceiverId,
    masspayMethod,
    bankAccountNumber,
    bankRoutingNumber,
    bankAccountType,
    zelleAccount,
    unsubscribeLink,
    primaryColor,
  } = props

  const formatPrice = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount / 100)
  }

  return (
    <BrandedTailwind brand={brand} primaryColor={primaryColor}>
      <Html>
        <PlainHead brand={brand} />

        <PlainBody>
          <Preview>A withdrawal has been processed from your account</Preview>

          <PlainMainCard {...props} unsubscribeLink={unsubscribeLink}>
            <Heading className="text-xl mb-0">Hey, {name}!</Heading>

            <Text className="mt-0">
              A withdrawal has been processed from your account.
            </Text>

            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mt-4">
              <Text className="mt-0 mb-2">
                Your redemption request for{' '}
                <strong>{formatPrice(amountUsd)}</strong> via{' '}
                <strong>{provider}</strong> was completed successfully.
                {additionalText && ` ${additionalText}`}
                {fireblocksExplorerLink && (
                  <>
                    {' '}
                    <Link
                      href={fireblocksExplorerLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary-600 underline"
                    >
                      Click here
                    </Link>{' '}
                    to view the transaction on the blockchain.
                  </>
                )}
              </Text>

              <Hr className="my-4" />

              <Container>
                {fireblocksReceiverAddress && (
                  <Row className="mb-1">
                    <Column className="text-sm">
                      <strong>Your address:</strong> {fireblocksReceiverAddress}
                    </Column>
                  </Row>
                )}

                {fireblocksTransactionHash && (
                  <Row className="mb-1">
                    <Column className="text-sm">
                      <strong>Transaction hash:</strong>{' '}
                      {fireblocksTransactionHash}
                    </Column>
                  </Row>
                )}

                {paypalReceiver && (
                  <Row className="mb-1">
                    <Column className="text-sm">
                      <strong>Receiver:</strong> {paypalReceiver}
                    </Column>
                  </Row>
                )}

                {transferBalanceReceiverId && (
                  <Row className="mb-1">
                    <Column className="text-sm">
                      <strong>Receiver ID:</strong> {transferBalanceReceiverId}
                    </Column>
                  </Row>
                )}

                {masspayMethod && (
                  <Row className="mb-1">
                    <Column className="text-sm">
                      <strong>Method:</strong> {masspayMethod}
                    </Column>
                  </Row>
                )}

                {bankAccountNumber && (
                  <Row className="mb-1">
                    <Column className="text-sm">
                      <strong>Bank Account Number:</strong>{' '}
                      {maskText(bankAccountNumber, 4)}
                    </Column>
                  </Row>
                )}

                {bankRoutingNumber && (
                  <Row className="mb-1">
                    <Column className="text-sm">
                      <strong>Bank Routing Number:</strong> {bankRoutingNumber}
                    </Column>
                  </Row>
                )}

                {bankAccountType && (
                  <Row className="mb-1">
                    <Column className="text-sm">
                      <strong>Bank Account Type:</strong> {bankAccountType}
                    </Column>
                  </Row>
                )}

                {zelleAccount && (
                  <Row>
                    <Column className="text-sm">
                      <strong>Zelle Account ID:</strong> {zelleAccount}
                    </Column>
                  </Row>
                )}
              </Container>
            </div>
          </PlainMainCard>
        </PlainBody>
      </Html>
    </BrandedTailwind>
  )
}

WithdrawalEmail.PreviewProps = {
  ...EXAMPLE_CONTEXT,
  name: 'John Doe',
  amountUsd: 100_00,
  provider: 'paytech',
  additionalText: 'Processing time: 1-2 business days.',
  fireblocksExplorerLink: 'https://etherscan.io/tx/0x*********',
  fireblocksReceiverAddress: '0x*********0abcdef*********0abcdef12345678',
  fireblocksTransactionHash: '0x987654321fedcba0987654321fedcba0987654321',
  paypalReceiver: '<EMAIL>',
  transferBalanceReceiverId: 'RECV123456',
  masspayMethod: 'EXPRESS',
  bankAccountNumber: '****************',
  bankRoutingNumber: '*********',
  bankAccountType: 'Checking',
  zelleAccount: '<EMAIL>',
  unsubscribeLink: 'https://example.com/unsubscribe',
} satisfies WithdrawalProps

export default WithdrawalEmail
