import React from 'react'
import { Heading, Html, Preview, Text } from '@react-email/components'
import { isBrand } from '../../constants/brand'
import { EmailProps } from '../../constants/types'
import { BrandedTailwind } from '../../components/branded-tailwind'
import { PlainArrowButton } from '../../components/plain/plain-arrow-button'
import { PlainMainCard } from '../../components/plain/plain-main-card'
import { PlainCode } from '../../components/plain/plain-code'
import { PlainHead } from '../../components/plain/plain-head'
import { PlainBody } from '../../components/plain/plain-body'
import { EXAMPLE_CONTEXT } from '../../constants/example'

export interface AbandonedCart3Props extends EmailProps {
  name: string
  unsubscribeLink: string
}

type AbandonedCart3EmailComponent = React.FC<AbandonedCart3Props> & {
  PreviewProps?: AbandonedCart3Props
}

export const AbandonedCart3Email: AbandonedCart3EmailComponent = (props) => {
  const { brand, name, unsubscribeLink, clientUrl, primaryColor } = props

  return (
    <BrandedTailwind brand={brand} primaryColor={primaryColor}>
      <Html>
        <PlainHead brand={brand} />

        <PlainBody>
          <Preview>Forgot to finish your Deposit?</Preview>

          <PlainMainCard
            {...props}
            imageFilename={
              isBrand(brand, 'csgo') ? 'abandoned-cart-3.png' : undefined
            }
            imageAlt="Forgot to finish your Deposit?"
            unsubscribeLink={unsubscribeLink}
          >
            <Heading className="text-xl mb-0">Hey {name},</Heading>

            <Text className="mt-4">
              We won't be contacting you again but here is a 5% deposit bonus
              code, simply head over to "deposit" and enter the code{' '}
              <PlainCode>CLASH5</PlainCode> on the top right to claim your 5%
              deposit bonus!
            </Text>

            <PlainArrowButton
              brand={brand}
              href={`${clientUrl}/deposit`}
              className="self-center mt-2 mb-2 px-6 rounded-md"
              style={{
                border: '1px solid #3D863F38',
              }}
            >
              Check it out
            </PlainArrowButton>
          </PlainMainCard>
        </PlainBody>
      </Html>
    </BrandedTailwind>
  )
}

AbandonedCart3Email.PreviewProps = {
  ...EXAMPLE_CONTEXT,
  name: 'John Doe',
  unsubscribeLink: 'https://example.com/unsubscribe',
} satisfies AbandonedCart3Props

export default AbandonedCart3Email
