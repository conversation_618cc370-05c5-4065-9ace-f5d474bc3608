import React from 'react'
import { Heading, Html, Preview, Text } from '@react-email/components'
import { isBrand } from '../../constants/brand'
import { EmailProps } from '../../constants/types'
import { BrandedTailwind } from '../../components/branded-tailwind'
import { PlainButton } from '../../components/plain/plain-button'
import { PlainMainCard } from '../../components/plain/plain-main-card'
import { PlainRightArrow } from '../../components/plain/plain-right-arrow'
import { PlainHead } from '../../components/plain/plain-head'
import { EXAMPLE_CONTEXT } from '../../constants/example'
import { PlainBody } from '../../components/plain/plain-body'

export interface AbandonedCart1Props extends EmailProps {
  name: string
  unsubscribeLink: string
}

type AbandonedCart1EmailComponent = React.FC<AbandonedCart1Props> & {
  PreviewProps?: AbandonedCart1Props
}

export const AbandonedCart1Email: AbandonedCart1EmailComponent = (props) => {
  const { brand, name, unsubscribeLink, clientUrl, primaryColor } = props

  return (
    <BrandedTailwind brand={brand} primaryColor={primaryColor}>
      <Html>
        <PlainHead brand={brand} />

        <PlainBody>
          <Preview>Having difficulties finishing your deposit?</Preview>

          <PlainMainCard
            {...props}
            imageFilename={
              isBrand(brand, 'csgo') ? 'abandoned-cart-1.png' : undefined
            }
            imageAlt="Having difficulties?"
            unsubscribeLink={unsubscribeLink}
          >
            <Heading className="text-xl mb-0">Hey {name},</Heading>

            <Text className="mt-0">
              we noticed that you haven't finished your deposit!
            </Text>

            <Text className="mt-0">
              Did you run into any technical issues during the deposit process?
              Feel free to simply contact our live support or ask in chat and we
              will get back to you as fast as possible.
            </Text>

            <PlainButton
              brand={brand}
              href={`${clientUrl}/deposit`}
              className="mt-2 mb-2 px-6 rounded-md"
              style={{
                border: '1px solid #3D863F38',
              }}
            >
              <span>Continue Deposit</span>
              <PlainRightArrow style={{ verticalAlign: 'middle' }} />
            </PlainButton>
          </PlainMainCard>
        </PlainBody>
      </Html>
    </BrandedTailwind>
  )
}

AbandonedCart1Email.PreviewProps = {
  ...EXAMPLE_CONTEXT,
  name: 'John Doe',
  unsubscribeLink: 'https://example.com/unsubscribe',
} satisfies AbandonedCart1Props

export default AbandonedCart1Email
