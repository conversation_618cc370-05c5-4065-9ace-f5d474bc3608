import React from 'react'
import {
  Heading,
  Html,
  Img,
  Preview,
  Section,
  Text,
} from '@react-email/components'
import { EmailProps } from '../../constants/types'
import { BrandedTailwind } from '../../components/branded-tailwind'
import { MarketingHead } from '../../components/marketing/marketing-head'
import { EXAMPLE_BRAND, EXAMPLE_CONTEXT } from '../../constants/example'
import { MarketingButton } from '../../components/marketing/marketing-button'
import { MarketingBody } from '../../components/marketing/marketing-body'
import { MarketingHeader } from '../../components/marketing/marketing-header'
import { MarketingFooter } from '../../components/marketing/marketing-footer'

export interface MarketingOffer30DepositBonusProps extends EmailProps {
  name: string
  unsubscribeLink: string
  promoCode: string
}

type MarketingOffer30DepositBonusEmailComponent =
  React.FC<MarketingOffer30DepositBonusProps> & {
    PreviewProps?: MarketingOffer30DepositBonusProps
  }

export const MarketingOffer30DepositBonusEmail: MarketingOffer30DepositBonusEmailComponent =
  (props) => {
    const { brand, name, clientUrl, promoCode } = props
    const imageUrl = `${clientUrl}/assets/${brand}/emails/marketing-dormant-2`

    return (
      <BrandedTailwind brand={brand}>
        <Html>
          <MarketingHead brand={brand} />
          <Preview>This is your LAST chance</Preview>
          <MarketingBody>
            <MarketingHeader {...props} />
            <Section className="py-1">
              <Img
                src={`${imageUrl}/3_Clash-Marketing-Email-Banners-10Artboard-21.png`}
                className="max-w-full"
                alt="Bonus"
              />

              <Heading as="h3" className="text-2xl font-bold px-2.5">
                🔶{' '}
                <span className="safe-text-white">
                  This is your LAST chance to get a bonus
                </span>
              </Heading>

              <Text className="safe-text-white text-base leading-tight px-2.5">
                Hey {name},
                <br />
                <br />
                This is it. No second chances. For a very limited time, we're
                giving you <strong>50% extra gems</strong> on your next deposit.
                Once it's gone, it's gone.
                <br />
                <br />
                So what do you get?
              </Text>

              <ul
                className="text-base leading-loose pl-10 my-2.5"
                style={{ listStyleType: 'revert' }}
              >
                <li>
                  <span className="safe-text-white">Deposit</span> 🔶{' '}
                  <span className="safe-text-white">50 - You’ll get</span> 🔶{' '}
                  <span className="safe-text-white">75 in total</span>
                </li>
                <li>
                  <span className="safe-text-white">Deposit</span> 🔶{' '}
                  <span className="safe-text-white">
                    100 - You’ll walk in with
                  </span>{' '}
                  🔶 <span className="safe-text-white">150</span>
                </li>
                <li>
                  <span className="safe-text-white">Go big with</span> 🔶{' '}
                  <span className="safe-text-white">
                    300 - You’ll have a massive
                  </span>{' '}
                  🔶 <span className="safe-text-white">450 to play with</span>
                </li>
              </ul>

              <Text className="text-base leading-tight px-2.5">
                <span className="safe-text-white">
                  That’s extra gems to dominate Case Battles, boost your level,
                  and unlock even better free cases.
                </span>
                <br />
                <br />
                <span className="safe-text-white">
                  No code. No catch. Just click here, deposit, and we’ll top you
                  up instantly.
                </span>
                <br />
                <br />
                ⚠️{' '}
                <span className="safe-text-white">
                  But don’t wait. This offer is hours from disappearing. After
                  that, the 50% bonus is history.
                </span>
                <br />
                <br />
                <strong className="safe-text-white">
                  Don’t miss your shot.
                </strong>
                <br />
                <span className="safe-text-white">
                  Top up now, crush the site, and squeeze every last drop of
                  value out of your deposit.
                </span>
              </Text>

              <Section className="text-center py-2.5">
                <MarketingButton
                  href={`https://clash.gg?bonus-code=${promoCode}`}
                >
                  Deposit Now and Get 50% Extra
                </MarketingButton>
              </Section>
            </Section>
            <MarketingFooter {...props} />
          </MarketingBody>
        </Html>
      </BrandedTailwind>
    )
  }

MarketingOffer30DepositBonusEmail.PreviewProps = {
  ...EXAMPLE_CONTEXT,
  name: 'John Doe',
  brand: EXAMPLE_BRAND,
  unsubscribeLink: 'https://example.com/unsubscribe',
  promoCode: '81631be1-c78d-4c66-8c1b-54563aa1338a',
} satisfies MarketingOffer30DepositBonusProps

export default MarketingOffer30DepositBonusEmail
