import React from 'react'
import {
  Heading,
  Html,
  Img,
  Preview,
  Section,
  Text,
} from '@react-email/components'
import { EmailProps } from '../../constants/types'
import { BrandedTailwind } from '../../components/branded-tailwind'
import { MarketingHead } from '../../components/marketing/marketing-head'
import { EXAMPLE_BRAND, EXAMPLE_CONTEXT } from '../../constants/example'
import { MarketingButton } from '../../components/marketing/marketing-button'
import { MarketingBody } from '../../components/marketing/marketing-body'
import { MarketingHeader } from '../../components/marketing/marketing-header'
import { MarketingFooter } from '../../components/marketing/marketing-footer'

export interface MarketingWeeklyEmailBattleRainProps extends EmailProps {
  name: string
  unsubscribeLink: string
}

type MarketingWeeklyEmailBattleRainEmailComponent =
  React.FC<MarketingWeeklyEmailBattleRainProps> & {
    PreviewProps?: MarketingWeeklyEmailBattleRainProps
  }

export const MarketingWeeklyEmailBattleRainEmail: MarketingWeeklyEmailBattleRainEmailComponent =
  (props) => {
    const { brand, name, clientUrl } = props
    const imageUrl = `${clientUrl}/assets/${brand}/emails/weekly-email-battle-rain`

    return (
      <BrandedTailwind brand={brand}>
        <Html>
          <MarketingHead brand={brand} />
          <Preview>Yo! {name}, Did you Know about Battle Rains?</Preview>
          <MarketingBody>
            <MarketingHeader {...props} />
            <Section className="py-1">
              <Img
                src={`${imageUrl}/Clash-Marketing-Email-Banners-6Artboard-5.png`}
                className="max-w-full"
                alt="Header"
              />

              <Heading
                as="h3"
                className="safe-text-white text-2xl font-bold px-2.5"
              >
                Yo! {name}, Did you Know about Battle Rains?
              </Heading>

              <Text className="safe-text-white text-base leading-tight px-2.5">
                Join battles. Win More. Then share the pot.
                <br />
                <br />
                Every 30 minutes, you have a shot at winning a growing gem pool
                - just by playing the battles you already love.
              </Text>

              <Heading
                as="h3"
                className="safe-text-white text-2xl font-bold px-2.5"
              >
                How Does it work?
              </Heading>

              <ol
                className="safe-text-white text-base leading-loose pl-10 my-2.5"
                style={{ listStyleType: 'decimal' }}
              >
                <li>
                  <strong>Join or create any battle worth 20+ gems</strong>
                  <br />
                  You only need to enter one eligible battle within the
                  30-minute window.
                </li>
                <li>
                  <strong>You’re in the rain</strong>
                  <br />
                  Once you’ve played, you’re automatically entered into that
                  round of Battle Rain.
                </li>
                <li>
                  <strong>The pot grows</strong>
                  <br />
                  The rain pool increases by 25 gems every 30 minutes.
                </li>
                <li>
                  <strong>At the half-hour mark, we spin</strong>
                  <br />
                  There’s a 1 in 50 chance that the Clash logo hits, If it does,
                  the pot is triggered.
                </li>
                <li>
                  <strong>Everyone splits the payout</strong>
                  <br />
                  If it hits, all eligible users in that time window split the
                  entire gem pool equally.
                </li>
              </ol>

              <Img
                src={`${imageUrl}/Clash-Marketing-Email-Banners-4Artboard-8.png`}
                className="max-w-full"
                alt="Battle Rain Info"
              />

              <Text className="safe-text-white text-base leading-tight px-2.5">
                You don’t need to do anything special. Just join or create any
                battle over 20 gems - you’ll be included automatically. The more
                active you are, the more chances you have to get in on a payout.
              </Text>

              <Section className="text-center py-2.5">
                <MarketingButton href="https://clash.gg/csgo-case-battles/create">
                  CREATE A BATTLE
                </MarketingButton>
              </Section>
            </Section>
            <MarketingFooter {...props} />
          </MarketingBody>
        </Html>
      </BrandedTailwind>
    )
  }

MarketingWeeklyEmailBattleRainEmail.PreviewProps = {
  ...EXAMPLE_CONTEXT,
  name: 'John Doe',
  brand: EXAMPLE_BRAND,
  unsubscribeLink: 'https://example.com/unsubscribe',
} satisfies MarketingWeeklyEmailBattleRainProps

export default MarketingWeeklyEmailBattleRainEmail
