import React from 'react'
import {
  Heading,
  Html,
  Img,
  Preview,
  Section,
  Text,
} from '@react-email/components'
import { EmailProps } from '../../constants/types'
import { BrandedTailwind } from '../../components/branded-tailwind'
import { MarketingHead } from '../../components/marketing/marketing-head'
import { EXAMPLE_BRAND, EXAMPLE_CONTEXT } from '../../constants/example'
import { MarketingButton } from '../../components/marketing/marketing-button'
import { MarketingBody } from '../../components/marketing/marketing-body'
import { MarketingHeader } from '../../components/marketing/marketing-header'
import { MarketingFooter } from '../../components/marketing/marketing-footer'

export interface MarketingReminderDailyStreakProps extends EmailProps {
  name: string
  streakNumber: number
  unsubscribeLink: string
}

type MarketingReminderDailyStreakEmailComponent =
  React.FC<MarketingReminderDailyStreakProps> & {
    PreviewProps?: MarketingReminderDailyStreakProps
  }

export const MarketingReminderDailyStreakEmail: MarketingReminderDailyStreakEmailComponent =
  (props) => {
    const { brand, name, streakNumber, clientUrl } = props
    const imageUrl = `${clientUrl}/assets/${brand}/emails/reminder-daily-streak`

    return (
      <BrandedTailwind brand={brand}>
        <Html>
          <MarketingHead brand={brand} />
          <Preview>
            QUICK {name}, Don’t lose your {String(streakNumber)} day streak!
          </Preview>
          <MarketingBody>
            <MarketingHeader {...props} />
            <Section className="py-1">
              <Img
                src={`${imageUrl}/Clash-Marketing-Email-Banners-8Artboard-2.png`}
                className="max-w-full"
                alt="Header"
              />

              <Heading
                as="h3"
                className="safe-text-white text-2xl font-bold px-2.5"
              >
                QUICK {name}, Don’t lose your {streakNumber} day streak!
              </Heading>

              <Text className="safe-text-white text-base leading-tight px-2.5">
                You’ve been building up your daily streak and you’re earning
                more every day because of it.
                <br />
                <br />
                Right now, your level cases are getting{' '}
                <strong>extra value daily</strong>, up to{' '}
                <strong>10% more</strong>. But if you don’t claim a case{' '}
                <strong>before the day resets (UTC)</strong>, your streak{' '}
                <strong>ends</strong>, and you’re back to zero.
              </Text>

              <Img
                src={`${imageUrl}/Clash-Marketing-Email-Banners-8Artboard-1.png`}
                className="max-w-full"
                alt="Streak Info"
              />

              <ul
                className="safe-text-white text-base leading-loose pl-10 my-2.5"
                style={{ listStyleType: 'revert' }}
              >
                <li>Claim 1 level case per UTC day</li>
                <li>
                  After 3 days, your case value increases by{' '}
                  <strong>+1% daily</strong>
                </li>
                <li>
                  Streak maxes at <strong>+10% bonus value</strong>
                </li>
                <li>
                  <strong>Loans won’t reset your streak</strong>, but forgetting
                  to claim will
                </li>
              </ul>

              <Heading
                as="h3"
                className="safe-text-white text-2xl font-bold text-center px-2.5"
              >
                You’ve come this far, Why waste it?
              </Heading>

              <Text className="text-base leading-tight text-center px-2.5">
                💡{' '}
                <span className="safe-text-white">
                  Just open a level case now and your streak continues.
                </span>
                <br />
                <span className="safe-text-white">
                  Miss it, and you lose the bonus entirely.
                </span>
              </Text>

              <Section className="text-center py-2.5">
                <MarketingButton href="https://clash.gg/rewards">
                  OPEN FREE CASES NOW
                </MarketingButton>
              </Section>
            </Section>
            <MarketingFooter {...props} />
          </MarketingBody>
        </Html>
      </BrandedTailwind>
    )
  }

MarketingReminderDailyStreakEmail.PreviewProps = {
  ...EXAMPLE_CONTEXT,
  name: 'John Doe',
  streakNumber: 5,
  brand: EXAMPLE_BRAND,
  unsubscribeLink: 'https://example.com/unsubscribe',
} satisfies MarketingReminderDailyStreakProps

export default MarketingReminderDailyStreakEmail
