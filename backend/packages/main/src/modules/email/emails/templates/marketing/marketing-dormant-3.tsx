import React from 'react'
import {
  Heading,
  Html,
  Img,
  Preview,
  Section,
  Text,
} from '@react-email/components'
import { EmailProps } from '../../constants/types'
import { BrandedTailwind } from '../../components/branded-tailwind'
import { MarketingHead } from '../../components/marketing/marketing-head'
import { EXAMPLE_BRAND, EXAMPLE_CONTEXT } from '../../constants/example'
import { MarketingButton } from '../../components/marketing/marketing-button'
import { MarketingBody } from '../../components/marketing/marketing-body'
import { MarketingHeader } from '../../components/marketing/marketing-header'
import { MarketingFooter } from '../../components/marketing/marketing-footer'

export interface MarketingDormant3Props extends EmailProps {
  name: string
  unsubscribeLink: string
  promoCode: string
}

type MarketingDormant3EmailComponent = React.FC<MarketingDormant3Props> & {
  PreviewProps?: MarketingDormant3Props
}

export const MarketingDormant3Email: MarketingDormant3EmailComponent = (
  props
) => {
  const { brand, name, clientUrl, promoCode } = props
  const imageUrl = `${clientUrl}/assets/${brand}/emails/marketing-dormant-3`

  return (
    <BrandedTailwind brand={brand}>
      <Html>
        <MarketingHead brand={brand} />
        <Preview>Hey {name}, You're missing out on this....</Preview>
        <MarketingBody>
          <MarketingHeader {...props} />
          <Section className="py-1">
            <Img
              src={`${imageUrl}/2_Clash-Marketing-Email-Banners-145Artboard-11.png`}
              className="max-w-full"
              alt="Header"
            />

            <Heading as="h3" className="text-2xl font-bold px-2.5">
              <span className="safe-text-white">
                Hey {name}, You're missing out on this....
              </span>
            </Heading>

            <Text className="safe-text-white text-base leading-tight px-2.5">
              It’s been a little while since we last saw you on Clash.gg, and
              honestly, it feels like something’s been missing. <br />
              <br />
              The rain hasnt stopped pouring and the battles are as intense as
              ever - but your name hasn’t been in the mix.
              <br />
              <br />
              We don’t want you to miss out on the action, so we’re giving you a
              reason to jump back in. Right now, you can claim a{' '}
              <strong>50% deposit bonus</strong> to boost your balance and get
              straight back to cracking cases, spinning wheels, and chasing
              those top-tier wins.
            </Text>

            <Img
              src={`${imageUrl}/3_Clash-Marketing-Email-Banners-16Artboard-11.png`}
              className="max-w-full"
              alt="Bonus"
            />

            <Heading as="h3" className="text-2xl font-bold px-2.5">
              <span className="safe-text-white">
                We're not going anywhere, but it looks like you are:
              </span>
            </Heading>

            <Text className="safe-text-white text-base leading-tight px-2.5">
              This isn’t just another promo - it’s your last call. When you top
              up <strong>today</strong>, we’ll instantly add an extra{' '}
              <strong>50%</strong> to your deposit. That’s extra balance to take
              more shots at riskier cases, level up your free case, and flex
              with the kind of skins that make people jealous.
              <br />
              <br />
              We won’t be running this offer again, and once it’s gone, it’s
              gone for good. Don’t miss your chance to come back stronger and
              end the streak on a high note.
            </Text>

            <Section className="text-center py-2.5">
              <MarketingButton
                href={`https://clash.gg?bonus-code=${promoCode}`}
              >
                CLAIM YOUR 50% DEPOSIT BONUS
              </MarketingButton>
            </Section>
          </Section>
          <MarketingFooter {...props} />
        </MarketingBody>
      </Html>
    </BrandedTailwind>
  )
}

MarketingDormant3Email.PreviewProps = {
  ...EXAMPLE_CONTEXT,
  name: 'John Doe',
  brand: EXAMPLE_BRAND,
  unsubscribeLink: 'https://example.com/unsubscribe',
  promoCode: '81631be1-c78d-4c66-8c1b-54563aa1338a',
} satisfies MarketingDormant3Props

export default MarketingDormant3Email
