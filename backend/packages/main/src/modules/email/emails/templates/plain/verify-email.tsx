import React from 'react'
import { Heading, Html, Link, Preview, Text } from '@react-email/components'
import { EmailProps } from '../../constants/types'
import { BrandedTailwind } from '../../components/branded-tailwind'
import { PlainArrowButton } from '../../components/plain/plain-arrow-button'
import { PlainMainCard } from '../../components/plain/plain-main-card'
import { PlainHead } from '../../components/plain/plain-head'
import { PlainBody } from '../../components/plain/plain-body'
import { EXAMPLE_CONTEXT } from '../../constants/example'

export interface VerifyEmailProps extends EmailProps {
  verifyLink: string
  unsubscribeLink: string
}

type VerifyEmailEmailComponent = React.FC<VerifyEmailProps> & {
  PreviewProps?: VerifyEmailProps
}

export const VerifyEmailEmail: VerifyEmailEmailComponent = (props) => {
  const { brand, verifyLink, unsubscribeLink, primaryColor } = props

  return (
    <BrandedTailwind brand={brand} primaryColor={primaryColor}>
      <Html>
        <PlainHead brand={brand} />

        <PlainBody>
          <Preview>Please verify your e-mail address</Preview>

          <PlainMainCard {...props} unsubscribeLink={unsubscribeLink}>
            <Heading className="text-xl mb-0">Hello!</Heading>

            <Text className="mt-0">
              Please click the following button to verify your e-mail address:
            </Text>

            <PlainArrowButton
              brand={brand}
              href={verifyLink}
              className="self-center mt-2 mb-2 px-6 rounded-md"
              style={{
                border: '1px solid #3D863F38',
              }}
            >
              Verify my e-mail
            </PlainArrowButton>

            <Text className="mt-4 text-sm text-gray-600">
              If the button above does not work, paste this link into your web
              browser:
              <br />
              <Link
                href={verifyLink}
                className="text-primary-600 underline break-all"
              >
                {verifyLink}
              </Link>
            </Text>
          </PlainMainCard>
        </PlainBody>
      </Html>
    </BrandedTailwind>
  )
}

VerifyEmailEmail.PreviewProps = {
  ...EXAMPLE_CONTEXT,
  verifyLink: 'https://example.com/verify?token=abc123def456',
  unsubscribeLink: 'https://example.com/unsubscribe',
} satisfies VerifyEmailProps

export default VerifyEmailEmail
