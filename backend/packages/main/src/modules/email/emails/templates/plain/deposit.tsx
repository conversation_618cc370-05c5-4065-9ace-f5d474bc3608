import React from 'react'
import {
  Column,
  Container,
  Heading,
  Hr,
  Html,
  Preview,
  Row,
  Text,
} from '@react-email/components'
import { EmailProps } from '../../constants/types'
import { BrandedTailwind } from '../../components/branded-tailwind'
import { PlainMainCard } from '../../components/plain/plain-main-card'
import { PlainHead } from '../../components/plain/plain-head'
import { PlainBody } from '../../components/plain/plain-body'
import { EXAMPLE_CONTEXT } from '../../constants/example'
import { formatNumber, formatProviderName } from '../../helpers/helpers'

export interface DepositProps extends EmailProps {
  name: string
  amountUsd: number
  playAmount?: number
  provider: string
  transactionId?: string
  transactionHash?: string
  transferBalanceSenderId?: string
  giftcardCode?: string
  skinsbackOrderId?: string
  waxpeerItemNames?: string[]
  unsubscribeLink: string
}

type DepositEmailComponent = React.FC<DepositProps> & {
  PreviewProps?: DepositProps
}

export const DepositEmail: DepositEmailComponent = (props) => {
  const {
    brand,
    name,
    amountUsd,
    playAmount,
    provider,
    transactionId,
    transactionHash,
    transferBalanceSenderId,
    giftcardCode,
    skinsbackOrderId,
    waxpeerItemNames,
    unsubscribeLink,
    primaryColor,
  } = props

  return (
    <BrandedTailwind brand={brand} primaryColor={primaryColor}>
      <Html>
        <PlainHead brand={brand} />

        <PlainBody>
          <Preview>You've received a new deposit</Preview>

          <PlainMainCard {...props} unsubscribeLink={unsubscribeLink}>
            <Heading className="text-xl mb-0">Hey {name}!</Heading>

            <Text className="mt-0">You've received a new deposit.</Text>

            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mt-4">
              <Text className="mt-0 mb-4">
                {playAmount > 0 ? (
                  <>
                    Your purchase of <strong>{formatNumber(playAmount)}</strong>{' '}
                    gold coins was successful through{' '}
                    <strong>{formatProviderName(provider)}</strong> method. You
                    have also been gifted{' '}
                    <strong>{formatNumber(amountUsd)}</strong> bonus free gems.
                  </>
                ) : (
                  <>
                    Your purchase via{' '}
                    <strong>{formatProviderName(provider)}</strong> was
                    successful. We have credited{' '}
                    <strong>{formatNumber(amountUsd)}</strong> to your balance.
                  </>
                )}
              </Text>

              <Hr className="my-4" />

              <Container>
                {transactionId && (
                  <Row className="mb-1">
                    <Column className="text-sm">
                      <strong>Transaction ID:</strong> {transactionId}
                    </Column>
                  </Row>
                )}

                {transactionHash && (
                  <Row className="mb-1">
                    <Column className="text-sm">
                      <strong>Transaction Hash:</strong> {transactionHash}
                    </Column>
                  </Row>
                )}

                {transferBalanceSenderId && (
                  <Row className="mb-1">
                    <Column className="text-sm">
                      <strong>Sender ID:</strong> {transferBalanceSenderId}
                    </Column>
                  </Row>
                )}

                {giftcardCode && (
                  <Row className="mb-1">
                    <Column className="text-sm">
                      <strong>Used giftcard code:</strong> {giftcardCode}
                    </Column>
                  </Row>
                )}

                {skinsbackOrderId && (
                  <Row className="mb-1">
                    <Column className="text-sm">
                      <strong>Your Skinsback order id:</strong>{' '}
                      {skinsbackOrderId}
                    </Column>
                  </Row>
                )}

                {waxpeerItemNames && waxpeerItemNames.length > 0 && (
                  <Row>
                    <Column className="text-sm">
                      <strong>Items used in deposit:</strong>
                      <ul className="list-disc pl-5 mt-1 space-y-1">
                        {waxpeerItemNames.map((itemName, index) => (
                          <li key={index} className="text-sm">
                            {itemName}
                          </li>
                        ))}
                      </ul>
                    </Column>
                  </Row>
                )}
              </Container>
            </div>
          </PlainMainCard>
        </PlainBody>
      </Html>
    </BrandedTailwind>
  )
}

DepositEmail.PreviewProps = {
  ...EXAMPLE_CONTEXT,
  name: 'John Doe',
  amountUsd: 50_00,
  playAmount: 100_00,
  provider: 'paytech',
  transactionId: 'TXN123456789',
  transactionHash: '0x1234567890abcdef',
  transferBalanceSenderId: 'SENDER123',
  giftcardCode: 'GIFT50',
  skinsbackOrderId: 'SB123456',
  waxpeerItemNames: ['AK-47 | Redline', 'AWP | Dragon Lore'],
  unsubscribeLink: 'https://example.com/unsubscribe',
} satisfies DepositProps

export default DepositEmail
