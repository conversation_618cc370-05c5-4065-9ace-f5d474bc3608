import React from 'react'
import {
  Heading,
  Html,
  Img,
  Preview,
  Section,
  Text,
} from '@react-email/components'
import { EmailProps } from '../../constants/types'
import { BrandedTailwind } from '../../components/branded-tailwind'
import { MarketingHead } from '../../components/marketing/marketing-head'
import { EXAMPLE_BRAND, EXAMPLE_CONTEXT } from '../../constants/example'
import { MarketingButton } from '../../components/marketing/marketing-button'
import { MarketingBody } from '../../components/marketing/marketing-body'
import { MarketingHeader } from '../../components/marketing/marketing-header'
import { MarketingFooter } from '../../components/marketing/marketing-footer'
import { MarketingGmailSafeHr } from '../../components/marketing/marketing-gmail-safe-hr'

export interface MarketingWarm2Props extends EmailProps {
  name: string
  unsubscribeLink: string
}

type MarketingWarm2EmailComponent = React.FC<MarketingWarm2Props> & {
  PreviewProps?: MarketingWarm2Props
}

export const MarketingWarm2Email: MarketingWarm2EmailComponent = (props) => {
  const { brand, name, clientUrl } = props
  const imageUrl = `${clientUrl}/assets/${brand}/emails/marketing-warm-2`

  return (
    <BrandedTailwind brand={brand}>
      <Html>
        <MarketingHead brand={brand} />
        <Preview>
          Hey {name}, The big win was just around the corner....
        </Preview>
        <MarketingBody>
          <MarketingHeader {...props} />
          <Section className="py-1">
            <Img
              src={`${imageUrl}/2_Clash-Marketing-Email-Banners-6Artboard-21.png`}
              className="max-w-full"
              alt="Header"
            />

            <Heading as="h3" className="text-2xl font-bold px-2.5">
              <span className="safe-text-white">
                Hey {name}, The big win was just around the corner....
              </span>
            </Heading>

            <Text className="safe-text-white text-base leading-tight px-2.5">
              Looks like we haven’t seen you in a while, and we didn’t want you
              to miss out. Your journey on Clash.gg is just getting started, and
              to give you a boost, we’re still holding your 5% first deposit
              bonus.
              <br />
              <br />
              That’s extra balance right from the start - thats more cases, and
              a better shot at landing something big. No pressure, just a little
              push to help you get in on the action.
            </Text>

            <Section className="text-center py-2.5">
              <MarketingButton href="https://clash.gg/welcome/r/ads">
                CLAIM MY 5% BONUS
              </MarketingButton>
            </Section>

            <MarketingGmailSafeHr className="my-5" />

            <Img
              src={`${imageUrl}/3_2ND_-_HOW_IT_WORKS.png`}
              className="max-w-full"
              alt="How it works"
            />

            <Heading as="h3" className="text-2xl font-bold px-2.5">
              <span className="safe-text-white">
                Not sure how to start, Its really simple:
              </span>
            </Heading>

            <ol
              className="safe-text-white text-base leading-loose pl-10 my-2.5"
              style={{ listStyleType: 'decimal' }}
            >
              <li>Click the link down below</li>
              <li>Deposit to unlock a 5% gem bonus</li>
              <li>
                Battle, try Crash and say goodbye to those crusty blue skins
              </li>
            </ol>

            <Text className="safe-text-white text-base leading-tight px-2.5">
              It’s quick, easy, and you’ll be in the game with a little extra
              firepower to back you up.
            </Text>

            <Section className="text-center py-2.5">
              <MarketingButton href="https://clash.gg/welcome/r/ads">
                CLAIM 5% BONUS
              </MarketingButton>
            </Section>

            <MarketingGmailSafeHr className="my-5" />

            <Img
              src={`${imageUrl}/4_Clash-Marketing-Email-Banners-5Artboard-21.png`}
              className="max-w-full"
              alt="More rewards"
            />

            <Heading as="h3" className="text-2xl font-bold px-2.5">
              <span className="safe-text-white">
                Theres more rewards than you think:
              </span>
            </Heading>

            <Text className="safe-text-white text-base leading-tight px-2.5">
              Opening cases is just the beginning. On Clash.gg, every spin,
              every deposit, and every battle can unlock extra perks through our
              rewards page. From <strong>free daily cases</strong> to special
              missions, deposit bonuses, and exclusive player rewards, there’s
              always something waiting to boost your balance and keep the action
              exciting.
              <br />
              <br />
              The more you play, the more you earn - it’s that simple. Don’t
              leave your rewards unclaimed.
            </Text>

            <Section className="text-center py-2.5">
              <MarketingButton href="https://clash.gg/welcome/r/ads">
                GO TO REWARDS PAGE
              </MarketingButton>
            </Section>
          </Section>
          <MarketingFooter {...props} />
        </MarketingBody>
      </Html>
    </BrandedTailwind>
  )
}

MarketingWarm2Email.PreviewProps = {
  ...EXAMPLE_CONTEXT,
  name: 'John Doe',
  brand: EXAMPLE_BRAND,
  unsubscribeLink: 'https://example.com/unsubscribe',
} satisfies MarketingWarm2Props

export default MarketingWarm2Email
