import React from 'react'
import { Heading, Html, Preview, Text } from '@react-email/components'
import { EmailProps } from '../../constants/types'
import { BrandedTailwind } from '../../components/branded-tailwind'
import { PlainArrowButton } from '../../components/plain/plain-arrow-button'
import { PlainMainCard } from '../../components/plain/plain-main-card'
import { PlainHead } from '../../components/plain/plain-head'
import { PlainBody } from '../../components/plain/plain-body'
import { EXAMPLE_CONTEXT } from '../../constants/example'

export interface LevelCaseReminderProps extends EmailProps {
  name: string
  unsubscribeLink: string
}

type LevelCaseReminderEmailComponent = React.FC<LevelCaseReminderProps> & {
  PreviewProps?: LevelCaseReminderProps
}

export const LevelCaseReminderEmail: LevelCaseReminderEmailComponent = (
  props
) => {
  const { brand, name, unsubscribeLink, clientUrl, primaryColor } = props

  return (
    <BrandedTailwind brand={brand} primaryColor={primaryColor}>
      <Html>
        <PlainHead brand={brand} />

        <PlainBody>
          <Preview>Your daily level case is waiting!</Preview>

          <PlainMainCard {...props} unsubscribeLink={unsubscribeLink}>
            <Heading className="text-xl mb-0">Hey, {name}!</Heading>

            <Text className="mt-0">Your daily level case is waiting!</Text>

            <Text className="mt-0">
              You might have forgotten, but there's a daily case ready for you
              to open. Click below to unlock your case and claim your reward.
            </Text>

            <PlainArrowButton
              brand={brand}
              href={`${clientUrl}/rewards`}
              className="self-center mt-2 mb-2 px-6 rounded-md"
              style={{
                border: '1px solid #3D863F38',
              }}
            >
              Open Now
            </PlainArrowButton>
          </PlainMainCard>
        </PlainBody>
      </Html>
    </BrandedTailwind>
  )
}

LevelCaseReminderEmail.PreviewProps = {
  ...EXAMPLE_CONTEXT,
  name: 'John Doe',
  unsubscribeLink: 'https://example.com/unsubscribe',
} satisfies LevelCaseReminderProps

export default LevelCaseReminderEmail
