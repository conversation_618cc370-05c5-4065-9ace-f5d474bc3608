import React from 'react'
import { Heading, Html, Preview, Text } from '@react-email/components'
import { EmailProps } from '../../constants/types'
import { BrandedTailwind } from '../../components/branded-tailwind'
import { PlainMainCard } from '../../components/plain/plain-main-card'
import { PlainHead } from '../../components/plain/plain-head'
import { PlainBody } from '../../components/plain/plain-body'
import { EXAMPLE_CONTEXT } from '../../constants/example'

export interface AuthCodeProps extends EmailProps {
  token: string
  unsubscribeLink: string
}

type AuthCodeEmailComponent = React.FC<AuthCodeProps> & {
  PreviewProps?: AuthCodeProps
}

export const AuthCodeEmail: AuthCodeEmailComponent = (props) => {
  const { brand, token, unsubscribeLink, primaryColor } = props

  return (
    <BrandedTailwind brand={brand} primaryColor={primaryColor}>
      <Html>
        <PlainHead brand={brand} />

        <PlainBody>
          <Preview>Your authentication code</Preview>

          <PlainMainCard {...props} unsubscribeLink={unsubscribeLink}>
            <Heading className="text-xl mb-0">Hello!</Heading>

            <Text className="mt-0">Your authentication code is:</Text>

            <code className="text-2xl font-bold text-center mt-0 p-4 bg-gray-50 rounded-lg border-2 border-dashed border-primary-500 bg-primary-500/10 text-primary-900 tracking-widest block mb-4">
              {token}
            </code>

            <Text className="mt-0">
              This code is valid for 15 minutes. Do not share it with anyone, or
              they will get access to your account and funds. If you did not
              request this login, please ignore this e-mail.
            </Text>
          </PlainMainCard>
        </PlainBody>
      </Html>
    </BrandedTailwind>
  )
}

AuthCodeEmail.PreviewProps = {
  ...EXAMPLE_CONTEXT,
  token: '123456',
  unsubscribeLink: 'https://example.com/unsubscribe',
} satisfies AuthCodeProps

export default AuthCodeEmail
