export enum EEmailBrand {
  CSGO = 'csgo',
  RUST = 'rust',
  CASES = 'common',
}

export type EmailBrand = EEmailBrand | `${EEmailBrand}`

export const isBrand = (brand: string, brands: EmailBrand | EmailBrand[]) => {
  if (Array.isArray(brands)) {
    for (const arrBrand of brands) {
      if (brand === arrBrand) {
        return true
      }
    }

    return false
  }

  return brand === brands
}
