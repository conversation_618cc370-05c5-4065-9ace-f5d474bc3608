import { Module } from '@nestjs/common'
import { EmailService } from './email.service'
import { MailerModule, MailerOptions } from '@nestjs-modules/mailer'
import { ConfigService } from '@nestjs/config'
import { BullModule } from '@nestjs/bullmq'
import { EmailProcessor } from './email.processor'
import { PassModule } from '../auth/pass/pass.module'
import { EmailController } from './email.controller'
import { EMAIL_QUEUE } from './email.constants'
import { BullBackoffStrategy } from 'src/common/configurable-backoff-strategy'

@Module({
  imports: [
    MailerModule.forRootAsync({
      useFactory: (config: ConfigService) => {
        const { host, port, user, pass } = config.get('email.smtp')

        const options: MailerOptions = {
          transport: {
            host,
            port,
            secure: port === 465,
            auth: { user, pass },
          },
          defaults: {
            from: config.get('email.from'),
          },
          preview: false, // { open: true } to see preview after email is sent
        }

        return options
      },
      inject: [ConfigService],
    }),
    BullModule.registerQueue({
      name: EMAIL_QUEUE,
      defaultJobOptions: {
        attempts: 3,
        backoff: {
          type: BullBackoffStrategy.SLOW,
        },
        removeOnComplete: 1000,
      },
    }),
    PassModule,
  ],
  controllers: [EmailController],
  providers: [EmailService, EmailProcessor],
  exports: [EmailService],
})
export class EmailModule {}
