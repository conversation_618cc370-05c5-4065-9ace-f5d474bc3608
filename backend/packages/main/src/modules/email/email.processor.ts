import { ISendMailOptions, MailerService } from '@nestjs-modules/mailer'
import { Logger } from '@nestjs/common'
import { EMAIL_QUEUE } from './email.constants'
import { ConfigService } from '@nestjs/config'
import { Job } from 'bullmq'
import { ProcessMq, ProcessorMq } from 'src/common/decorators/bullmq.decorator'
import { render } from '@react-email/components'
import { EMAIL_TEMPLATES } from './emails'

@ProcessorMq(EMAIL_QUEUE, { concurrency: 10 })
export class EmailProcessor {
  private readonly logger = new Logger(EmailProcessor.name)
  constructor(
    private mailerService: MailerService,
    private config: ConfigService
  ) {}

  @ProcessMq('send')
  async send(job: Job<{ details: ISendMailOptions }>) {
    this.logger.log('Sending data=%o', job.data)

    const siteName = this.config.get('siteName') ?? ''
    const legalEntity = this.config.get('legalEntity') ?? ''
    job.data.details.context = {
      ...job.data.details.context,
      siteName,
      legalEntity,
    }

    const templateName = job.data.details.template
    const TemplateComponent = EMAIL_TEMPLATES[templateName]

    if (!TemplateComponent) {
      throw new Error(`Unknown email template: ${templateName}`)
    }

    job.data.details.html = await render(
      TemplateComponent({ ...job.data.details.context })
    )

    const result = await this.mailerService.sendMail(job.data.details)

    if (result.rejected.length) {
      throw new Error(`E-mail message rejected: ${JSON.stringify(result)}`)
    }
  }
}
