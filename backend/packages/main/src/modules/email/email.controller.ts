import { Body, Controller, Get, Post, Query } from '@nestjs/common'
import { assert } from 'src/utils/assert'
import { EmailService } from './email.service'

@Controller('email')
export class EmailController {
  constructor(private emailService: EmailService) {}

  @Get('unsubscribe')
  async unsubscribeGet(@Query('pass') pass: string) {
    assert(typeof pass === 'string', 'pass_required')
    return this.emailService.unsubscribe(pass)
  }

  @Post('unsubscribe')
  async unsubscribe(
    @Body('pass') bodyPass: string,
    @Query('pass') queryPass: string
  ) {
    const pass = bodyPass || queryPass
    assert(typeof pass === 'string', 'pass_required')

    return this.emailService.unsubscribe(pass)
  }
}
