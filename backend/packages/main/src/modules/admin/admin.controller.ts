import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common'
import { AccessGuard } from '../auth/guards/access.guard'
import {
  AdminGuard,
  ManagementGuard,
  ModGuard,
  StaffGuard,
} from '../auth/guards/role.guard'
import { AdminService } from './admin.service'
import { NotifyUpdateDto } from './dto/notify-update.dto'
import { SUCCESS } from 'src/common/constants'
import { IpDetailsDto } from './dto/ip-details.dto'
import { Role, UserId, UserRole } from 'src/common/user-id.decorator'
import { UserRoleDto } from './dto/user-role.dto'
import { AuthService } from '../auth/auth.service'
import { UserService } from '../user/user.service'
import { AdminNotificationsService } from './admin-notifications/admin-notifications.service'
import { SecurityLogsDto } from './dto/security-logs.dto'
import { Ip } from 'src/common/ip.decorator'

@Controller('admin')
export class AdminController {
  constructor(
    private adminService: AdminService,
    private readonly userService: UserService,
    private readonly authService: AuthService,
    private readonly adminNotifications: AdminNotificationsService
  ) {}

  @Get(['stats', 'stats/v2'])
  @UseGuards(AccessGuard, AdminGuard)
  async getStatsv2() {
    return this.adminService.getStatsv2()
  }

  @Get('top-withdrawals')
  async getTopWithdrawals() {
    return this.adminService.getTopWithdrawals()
  }

  @Post('/discord/:id/unlink')
  @UseGuards(AccessGuard, ModGuard)
  async unlinkDiscord(@Param('id') id: string) {
    await this.adminService.unlinkDiscord(id)

    return { success: true }
  }

  @Post('notify-update')
  @UseGuards(AccessGuard, StaffGuard)
  async notifyUpdate(@Body() body: NotifyUpdateDto) {
    return this.adminService.notifyUpdate(body)
  }

  @Post('user/:id/reset-password')
  @UseGuards(AccessGuard, ManagementGuard)
  async resetUserPassword(@Param('id', ParseIntPipe) userId: Int) {
    await this.adminService.resetUserPassword(userId)

    return SUCCESS
  }

  @Get('user/:userId/ip-details')
  @UseGuards(AccessGuard, ModGuard)
  async userIpDetails(
    @Param('userId', ParseIntPipe) userId: Int,
    @Query() { lastDays, limit }: IpDetailsDto
  ) {
    return this.adminService.getUserIpDetails(userId, lastDays, limit)
  }

  @Patch('user/:userId/role')
  @UseGuards(AccessGuard, ManagementGuard)
  async changeUserRole(
    @Ip() ip: string,
    @UserId() executorUserId: Int,
    @Param('userId', ParseIntPipe) userId: Int,
    @Body() body: UserRoleDto
  ) {
    await this.adminService.changeUserRole(
      executorUserId,
      userId,
      body.role,
      ip
    )
    return SUCCESS
  }

  @Post('user/:userId/force-logout')
  @UseGuards(AccessGuard, StaffGuard)
  async forceLogoutUser(@Param('userId', ParseIntPipe) userId: Int) {
    await this.authService.setJwtCutoff(userId)
    return SUCCESS
  }

  @Delete('user/:userId')
  @UseGuards(AccessGuard, ManagementGuard)
  async deleteUser(
    @UserId() userId: Int,
    @Param('userId', ParseIntPipe) userIdToDelete: Int
  ) {
    return this.userService.deleteAccount(userId, userIdToDelete)
  }

  @Get('security-logs/types')
  @UseGuards(AccessGuard, ModGuard)
  async getSecurityLogTypes() {
    return this.adminNotifications.getSecurityLogTypes()
  }

  @Get('security-logs')
  @UseGuards(AccessGuard, ModGuard)
  async getSecurityLogs(
    @UserRole() userRole: Role,
    @Query() query: SecurityLogsDto
  ) {
    return this.adminNotifications.getSecurityLogs(userRole, query)
  }
}
