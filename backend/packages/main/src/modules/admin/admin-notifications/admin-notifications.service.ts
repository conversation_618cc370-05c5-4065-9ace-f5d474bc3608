import { Injectable, Logger } from '@nestjs/common'
import axios from 'axios'
import { AdminNotificationsAccess } from './admin-notifications.access'
import {
  ADMIN_SECURITY_LOG_CATEGORIES,
  MOD_SECURITY_LOG_CATEGORIES,
  STAFF_SECURITY_LOG_CATEGORIES,
} from './admin-notifications.constants'
import { SecurityLogsDto } from '../dto/security-logs.dto'
import { Role } from 'src/common/user-id.decorator'
import { Prisma } from '@prisma/client'
import { paginated } from 'src/common/paginate'
import { NotificationEventName } from './admin-notifications.interface'

@Injectable()
export class AdminNotificationsService {
  private readonly logger = new Logger(AdminNotificationsService.name)
  constructor(
    private readonly adminNotificationsAccess: AdminNotificationsAccess
  ) {}

  async notify({
    text,
    channel = 'admin',
    eventName,
    executorId,
    executorIp,
  }: {
    text: string
    channel?: 'admin' | 'mod' | string
    eventName: NotificationEventName
    executorId?: Int
    executorIp?: string
  }) {
    const webhookUrl =
      {
        mod: process.env.DISCORD_WEBHOOK_MOD,
        admin: process.env.DISCORD_WEBHOOK,
        checkout: process.env.DISCORD_WEBHOOK_CHECKOUT,
        antifraud: process.env.DISCORD_WEBHOOK_ANTIFRAUD,
        'antifraud-block': process.env.DISCORD_WEBHOOK_ANTIFRAUD_BLOCK,
        'big-deposit':
          process.env.DISCORD_WEBHOOK_BIG_DEPOSIT ??
          process.env.DISCORD_WEBHOOK_FIAT,
        fiat: process.env.DISCORD_WEBHOOK_FIAT,
      }[channel] ?? channel
    this.logger.log(
      `Notify content=%o channel=%s webhookUrl=%s`,
      { eventName, text, executorId, executorIp },
      channel,
      webhookUrl
    )
    await this.adminNotificationsAccess
      .saveSecurityLog({
        category: channel,
        subcategory: eventName,
        text,
        executorId,
        executorIp,
      })
      .catch((err) => {
        this.logger.error({ err }, `Failed to save security log`)
      })

    if (webhookUrl) {
      axios
        .post(webhookUrl, {
          content: text,
          embeds: null,
          attachments: [],
        })
        .catch((err) => {
          this.logger.error({ err }, `Failed to notify ${text}`)
        })
    }
  }

  getSecurityLogTypes(role?: Role) {
    const types = {
      mod: {
        categories: MOD_SECURITY_LOG_CATEGORIES,
      },
      staff: {
        categories: STAFF_SECURITY_LOG_CATEGORIES,
      },
      management: {
        categories: STAFF_SECURITY_LOG_CATEGORIES,
      },
      admin: {
        categories: ADMIN_SECURITY_LOG_CATEGORIES,
      },
      subCategories: Object.values(NotificationEventName),
    }

    return role ? types[role] : types
  }

  async getSecurityLogs(userRole: Role, query: SecurityLogsDto) {
    const {
      categories,
      subCategories,
      dateFrom,
      dateTo,
      executorId,
      executorIp,
      text,
      page,
      pageSize = 100,
    } = query
    const { categories: allowedCategories } = this.getSecurityLogTypes(userRole)

    const searchCategories = categories
      ? categories.filter((c) => allowedCategories.includes(c))
      : allowedCategories

    const where: Prisma.SecurityLogWhereInput = {
      category: {
        in: searchCategories,
      },
      ...(subCategories && { subcategory: { in: subCategories } }),
      ...(dateFrom && { createdAt: { gte: new Date(dateFrom) } }),
      ...(dateTo && { createdAt: { lte: new Date(dateTo) } }),
      ...(text && {
        text: {
          contains: text,
          mode: 'insensitive',
        },
      }),
      executorId,
      executorIp,
    }

    const [data, count] = await Promise.all([
      this.adminNotificationsAccess.getSecurityLogs(where, {
        page,
        pageSize,
      }),
      this.adminNotificationsAccess.getSecurityLogsCount(where),
    ])

    return paginated({ data, count, pageSize })
  }
}
