import { Injectable } from '@nestjs/common'
import { Prisma } from '@prisma/client'
import { PaginatedDto } from 'src/common/dto/paginated.dto'
import { paginate } from 'src/common/paginate'
import { PrismaService } from 'src/modules/prisma/prisma.service'

@Injectable()
export class AdminNotificationsAccess {
  constructor(private readonly prisma: PrismaService) {}

  async saveSecurityLog(
    data: Prisma.SecurityLogCreateInput & { executorId?: Int }
  ) {
    const { executorId, ...createInput } = data
    return await this.prisma.securityLog.create({
      data: {
        ...createInput,
        ...(executorId && { executor: { connect: { id: executorId } } }),
      },
    })
  }

  async getSecurityLogs(
    where: Prisma.SecurityLogWhereInput,
    paginationOptions?: PaginatedDto
  ) {
    return this.prisma.securityLog.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      ...(paginationOptions && paginate(paginationOptions)),
    })
  }

  async getSecurityLogsCount(where: Prisma.SecurityLogWhereInput) {
    return this.prisma.securityLog.count({ where })
  }
}
