import {
  Controller,
  Get,
  ParseIntPipe,
  Query,
  SetMetadata,
  UseGuards,
} from '@nestjs/common'
import { ReportsService } from './reports.service'
import {
  ManagementGuard,
  ModGuard,
  StaffGuard,
} from 'src/modules/auth/guards/role.guard'
import { AccessGuard } from 'src/modules/auth/guards/access.guard'
import { ApiGuard } from 'src/modules/auth/guards/api.guard'
import { ParseDatePipe } from 'src/common/parse-date.pipe'

@Controller('admin/reports')
export class ReportsController {
  constructor(private reportsService: ReportsService) {}

  @Get('rain-claimers')
  @UseGuards(AccessGuard, StaffGuard)
  async getRainClaimers(@Query('days', ParseIntPipe) days: Int = 7) {
    return this.reportsService.getRainClaimers(`${days} days`)
  }

  @Get('rain-claimers-api')
  @SetMetadata('validator', (token) => token.scope === 'reports')
  @UseGuards(ApiGuard)
  async getRainClaimersApi(@Query('days', ParseIntPipe) days: Int = 7) {
    return this.reportsService.getRainClaimers(`${days} days`)
  }

  @Get('battle-rain-claimers-api')
  @SetMetadata('validator', (token) => token.scope === 'reports')
  @UseGuards(ApiGuard)
  async getBattleRainClaimersApi(@Query('days', ParseIntPipe) days: Int = 7) {
    return this.reportsService.getBattleRainClaimers(`${days} days`)
  }

  @Get('promo-claimers-api')
  @SetMetadata('validator', (token) => token.scope === 'reports')
  @UseGuards(ApiGuard)
  async getPromoClaimersApi(@Query('days', ParseIntPipe) days: Int = 7) {
    return this.reportsService.getPromoClaimers(`${days} days`)
  }

  @Get('suspicious-promo-users-api')
  @SetMetadata('validator', (token) => token.scope === 'reports')
  @UseGuards(ApiGuard)
  async getSuspiciousPromoUsersApi(
    @Query('activeLastDays', new ParseIntPipe({ optional: true }))
    activeLastDays: Int = 30,
    @Query('depositUnder', new ParseIntPipe({ optional: true }))
    depositUnder: Int = 10_00,
    @Query('withdrawOver', new ParseIntPipe({ optional: true }))
    withdrawOver: Int = 20_00,
    @Query('minPromoClaims', new ParseIntPipe({ optional: true }))
    minPromoClaims: Int = 1
  ) {
    return await this.reportsService.getSuspiciousPromoUsers({
      activeLastDays,
      depositUnder,
      withdrawOver,
      minPromoClaims,
    })
  }

  @Get('suspicious-rain-users-api')
  @SetMetadata('validator', (token) => token.scope === 'reports')
  @UseGuards(ApiGuard)
  async getSuspiciousRainUsersApi(
    @Query('activeLastDays', new ParseIntPipe({ optional: true }))
    activeLastDays: Int = 30,
    @Query('depositUnder', new ParseIntPipe({ optional: true }))
    depositUnder: Int = 10_00,
    @Query('withdrawOver', new ParseIntPipe({ optional: true }))
    withdrawOver: Int = 20_00,
    @Query('minRainClaims', new ParseIntPipe({ optional: true }))
    minRainClaims: Int = 1,
    @Query('minConsecutiveRainClaims', new ParseIntPipe({ optional: true }))
    minConsecutiveRainClaims: Int = 1
  ) {
    return await this.reportsService.getSuspiciousRainUsers({
      activeLastDays,
      depositUnder,
      withdrawOver,
      minRainClaims,
      minConsecutiveRainClaims,
    })
  }

  @Get('top-withdraws-day')
  @UseGuards(AccessGuard, StaffGuard)
  async getTopWithdraws() {
    return this.reportsService.getTopWithdrawsToday()
  }

  @Get('top-skin-withdraws-day')
  @UseGuards(AccessGuard, StaffGuard)
  async getTopSkinWithdraws() {
    return this.reportsService.getTopSkinWithdrawsToday()
  }

  @Get('admin-tips')
  @UseGuards(AccessGuard, StaffGuard)
  async getAdminTips() {
    return this.reportsService.getAdminTips()
  }

  @Get('admin-credits')
  @UseGuards(AccessGuard, ManagementGuard)
  async getAdminCredits(@Query('day', ParseDatePipe) day: Date | null) {
    if (!day) day = new Date()
    return await this.reportsService.getAdminCredits(day)
  }

  @Get('new-users')
  @UseGuards(AccessGuard, ModGuard)
  async getNewUsers() {
    const data = await this.reportsService.getNewUsers()
    return { data }
  }
}
