import { MINUTE_MS } from '@crashgg/common/dist'
import { Injectable } from '@nestjs/common'
import { Prisma, UserBadge } from '@prisma/client'
import { PUBLIC_USER_FIELDS, STAFF_ROLES } from 'src/common/constants'
import { Role } from 'src/common/user-id.decorator'
import { PrismaService } from 'src/modules/prisma/prisma.service'
import {
  SuspiciousPromoUsersOptions,
  SuspiciousRainUsersOptions,
} from './reports.types'
import { tsBefore } from 'src/common/utilities'
import { DAY_MS } from 'src/utils/constants'

@Injectable()
export class ReportsService {
  constructor(private prisma: PrismaService) {}

  async getRainClaimers(interval: string) {
    return await this.prisma
      .$queryRaw`SELECT COUNT(*)::int AS claims, SUM("balanceChange")::int AS amount, "userId" FROM "Transaction" t
      INNER JOIN "User" u ON u."id" = t."userId"
      WHERE message LIKE 'Rain reward (REAL)'
      AND t."createdAt" > NOW() - ${interval}::interval
      AND (u."bannedUntil" IS NULL OR u."bannedUntil" < NOW())
      GROUP BY "userId"
      ORDER BY 1 DESC
      LIMIT 1000`
  }

  async getBattleRainClaimers(interval: string) {
    return await this.prisma
      .$queryRaw`SELECT COUNT(*)::int AS claims, SUM("balanceChange")::int AS amount, "userId" FROM "Transaction" t
      INNER JOIN "User" u ON u."id" = t."userId"
      WHERE message LIKE 'Battle rain reward%'
      AND t."createdAt" > NOW() - ${interval}::interval
      AND (u."bannedUntil" IS NULL OR u."bannedUntil" < NOW())
      GROUP BY "userId"
      ORDER BY 1 DESC
      LIMIT 1000`
  }

  async getPromoClaimers(interval: string) {
    return await this.prisma
      .$queryRaw`SELECT COUNT(*)::int AS claims, SUM("balanceChange")::int AS amount, "userId" FROM "Transaction" t
      INNER JOIN "User" u ON u."id" = t."userId"
      WHERE message LIKE 'Claimed promo code%'
      AND t."createdAt" > NOW() - ${interval}::interval
      AND (u."bannedUntil" IS NULL OR u."bannedUntil" < NOW())
      GROUP BY "userId"
      ORDER BY 1 DESC
      LIMIT 1000`
  }

  async getSuspiciousPromoUsers(options: SuspiciousPromoUsersOptions) {
    const { activeLastDays, depositUnder, withdrawOver, minPromoClaims } =
      options
    const activeInterval = `${activeLastDays} days`

    return await this.prisma.$queryRaw`select
          count(*)::int AS claims,
          u.id,
          u."totalDeposits",
          u."totalWithdrawals"
        from "Transaction" t
        inner join "User" u on u.id = t."userId"
        inner join "UserRestrictions" ur on ur."userId" = u.id
        where t.message like 'Claimed promo code%'
          and t."createdAt" > now() - ${activeInterval}::interval
          and u."totalDeposits" < ${depositUnder}
          and u."totalWithdrawals" > ${withdrawOver}
          and ur."freebiesLocked" = false
          and (u."bannedUntil" is null or u."bannedUntil" < now())
        group by 2, 3, 4
        having count(*)::int > ${minPromoClaims}`
  }

  async getSuspiciousRainUsers(options: SuspiciousRainUsersOptions) {
    const {
      activeLastDays,
      depositUnder,
      withdrawOver,
      minRainClaims,
      minConsecutiveRainClaims,
    } = options

    const activeInterval = `${activeLastDays} days`

    const rainClaims: {
      userId: Int
      totalDeposits: Int
      totalWithdrawals: Int
      claimedAt: Date
    }[] = await this.prisma.$queryRaw`select
              u.id as "userId",
              u."totalDeposits",
              u."totalWithdrawals",
              t."createdAt" as "claimedAt"
          from "Transaction" t
          inner join "User" u on u.id = t."userId"
          inner join "UserRestrictions" ur on ur."userId" = u.id
          where t.message like 'Rain reward (REAL)'
              and t."createdAt" > now() - ${activeInterval}::interval
              and u."totalDeposits" < ${depositUnder}
              and u."totalWithdrawals" > ${withdrawOver}
              and ur."freebiesLocked" = false
              and (u."bannedUntil" is null or u."bannedUntil" < now())
         order by u.id, t."createdAt"`

    const userIdList = Array.from(
      new Set(rainClaims.map((claims) => claims.userId))
    )

    const result = userIdList.map((userId) => {
      const userClaims = rainClaims.filter((claim) => claim.userId === userId)
      if (userClaims.length < minRainClaims) return undefined
      const consecutiveClaims =
        this._calculateConsecutiveClaimsForUser(userClaims)
      if (consecutiveClaims.longestClaimStreak < minConsecutiveRainClaims)
        return undefined
      return {
        userId,
        totalDeposited: userClaims[0].totalDeposits,
        totalWithdrawals: userClaims[0].totalWithdrawals,
        numberOfClaims: userClaims.length,
        ...consecutiveClaims,
      }
    })

    return result.filter(Boolean)
  }

  _calculateConsecutiveClaimsForUser(claims: { claimedAt: Date }[]) {
    const groupedClaims: Date[][] = []

    for (const claim of claims) {
      const lastClaimSeries = groupedClaims.at(-1)
      const lastClaimInSeries = lastClaimSeries?.at(-1) || new Date(0)

      const diff = claim.claimedAt.getTime() - lastClaimInSeries.getTime()
      if (diff < 35 * MINUTE_MS) {
        lastClaimSeries.push(claim.claimedAt)
      } else {
        groupedClaims.push([claim.claimedAt])
      }
    }

    let longestClaimStreak = 0
    let shortestClaimStreak = Infinity
    for (const gc of groupedClaims) {
      const len = gc.length
      if (len > longestClaimStreak) longestClaimStreak = len
      if (len < shortestClaimStreak) shortestClaimStreak = len
    }

    return {
      groupedClaims,
      shortestClaimStreak,
      longestClaimStreak,
    }
  }

  async getTopWithdrawsToday() {
    return await this.prisma
      .$queryRaw`SELECT SUM("amountBalance")::int AS amount, "userId" FROM "Payment"
    WHERE TYPE='withdraw' AND DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())
    GROUP BY "userId"
    ORDER BY 1 DESC
    LIMIT 1000`
  }

  async getTopSkinWithdrawsToday() {
    return await this.prisma
      .$queryRaw`SELECT SUM("balanceChange")::int AS amount, "userId" FROM "Tradeoffer"
        WHERE "status"='accepted' AND TYPE='withdraw'
        AND DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())
        GROUP BY "userId"
        ORDER BY 1 DESC
        LIMIT 1000`
  }

  async getAdminTips() {
    interface RawTip {
      sender: Int
      message: string
      amount: Int
      createdAt: Date
      day: Date
    }
    const raw: RawTip[] = await this.prisma.$queryRaw`SELECT
      t."userId" AS sender,
      t.message,
      abs(t."balanceChange") AS amount,
      t."createdAt",
      DATE_TRUNC('day', t."createdAt") AS day
    FROM "Transaction" as t
    inner join "User" as u on u.id = t."userId"
    WHERE u.role in (${Prisma.join(STAFF_ROLES)})
      AND message LIKE 'Tip to%'
      AND t."createdAt" > NOW() - INTERVAL '3 day'
    ORDER BY DATE_TRUNC('day', t."createdAt") DESC, t."balanceChange" ASC`

    return raw.map((tip) => {
      const receiverId = Number(tip.message.match(/(\d+)/)[1])

      return {
        senderId: tip.sender,
        receiverId,
        amount: tip.amount,
        createdAt: tip.createdAt,
      }
    })
  }

  async getAdminCredits(day: Date) {
    interface RawCreditRecord {
      type: 'CREDIT' | 'TIP'
      senderId: Int
      senderName: string
      senderAvatar: string
      recipientId: Int
      recipientName: string
      recipientAvatar: string
      recipientRole: Role
      recipientBadge: UserBadge | null
      reason?: string
      message: string
      amount: Int
      createdAt: Date
    }

    const getCredits: Promise<RawCreditRecord[]> = this.prisma.$queryRaw`
    SELECT
      DATE_TRUNC('day', t."createdAt") AS day,
      abs(t."balanceChange") AS amount,
      'CREDIT' AS type,
      s.id AS "senderId",
      s.avatar as "senderAvatar",
      s.name as "senderName",
      r.id AS "recipientId",
      r.avatar AS "recipientAvatar",
      r.name AS "recipientName",
      r.role AS "recipientRole",
      r."badge" AS "recipientBadge",
      t.message,
      t."createdAt"
    FROM "Transaction" as t
    INNER JOIN "User" AS s ON s.id = split_part(t.message, ' ', 4)::INT
    INNER JOIN "User" AS r ON r.id = t."userId"
    WHERE
      t.message LIKE 'Credited by admin%'
      AND DATE_TRUNC('day', t."createdAt") = DATE_TRUNC('day', ${day})
    ORDER BY 1 DESC, 2 ASC`

    const getTips: Promise<RawCreditRecord[]> = this.prisma.$queryRaw`
    SELECT
      DATE_TRUNC('day', t."createdAt") AS day,
      abs(t."balanceChange") AS amount,
      'TIP' AS type,
      s.id AS "senderId",
      s.avatar as "senderAvatar",
      s.name as "senderName",
      r.id AS "recipientId",
      r.avatar AS "recipientAvatar",
      r.name AS "recipientName",
      r.role AS "recipientRole",
      r."badge" AS "recipientBadge",
      t.message,
      t."createdAt"
    FROM "Transaction" as t
    INNER JOIN "User" AS s ON s.id = t."userId"
    INNER JOIN "User" AS r ON r.id = split_part(t.message, ' ', 3)::INT
    WHERE s.role in (${Prisma.join(STAFF_ROLES)})
      AND t.message LIKE 'Tip to%'
      AND DATE_TRUNC('day', t."createdAt") = DATE_TRUNC('day', ${day})
    ORDER BY 1 DESC, 2 ASC`

    const raw = await Promise.all([getCredits, getTips])
    const data = raw.flat().map((credit) => {
      const reasonMatch = credit.message.match(/\(([^)]+)\)/)

      return {
        type: credit.type,
        sender: {
          id: credit.senderId,
          name: credit.senderAvatar,
          avatar: credit.senderAvatar,
        },
        recipient: {
          id: credit.recipientId,
          name: credit.recipientAvatar,
          avatar: credit.recipientAvatar,
          role: credit.recipientRole,
          badge: credit.recipientBadge,
        },
        amount: credit.amount,
        ...(reasonMatch && { reason: reasonMatch[1] }),
        message: credit.message,
        createdAt: credit.createdAt,
      }
    })
    return { data }
  }

  async getNewUsers() {
    const newUsers = await this.prisma.user.findMany({
      where: {
        createdAt: {
          gte: tsBefore(DAY_MS),
        },
      },
      select: {
        ...PUBLIC_USER_FIELDS,
        createdAt: true,
        email: true,
        balance: true,
        balanceVault: true,
        balanceCrypto: true,
      },
      orderBy: { id: 'desc' },
    })

    return newUsers
  }
}
