import { Modu<PERSON> } from '@nestjs/common'
import { AdminService } from './admin.service'
import { AdminController } from './admin.controller'
import { BullModule } from '@nestjs/bullmq'
import { AdminProcessor } from './admin.processor'
import { ReportsModule } from './reports/reports.module'
import { AlertsModule } from './alerts/alerts.module'
import { ADMIN_QUEUE } from './admin.constants'
import { MASTERMIND_SYNC_QUEUE } from '@crashgg/common/dist'
import { UserModule } from '../user/user.module'
import { AdminNotificationsModule } from './admin-notifications/admin-notifications.module'
import { AuthModule } from '../auth/auth.module'
import { IpLockModule } from '../ip-lock/ip-lock.module'

@Module({
  imports: [
    BullModule.registerQueue({
      name: ADMIN_QUEUE,
    }),
    BullModule.registerQueue({
      name: MASTERMIND_SYNC_QUEUE,
    }),
    ReportsModule,
    AlertsModule,
    AdminNotificationsModule,
    UserModule,
    AuthModule,
    IpLockModule,
  ],
  providers: [AdminService, AdminProcessor],
  controllers: [AdminController],
  exports: [AdminService],
})
export class AdminModule {}
