import { Transform, Type } from 'class-transformer'
import {
  IsArray,
  IsDateString,
  IsInt,
  IsOptional,
  IsString,
} from 'class-validator'
import { PaginatedDto } from 'src/common/dto/paginated.dto'

export class SecurityLogsDto extends PaginatedDto {
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',')
    }
    return value
  })
  @IsArray()
  @IsString({ each: true })
  categories?: string[]

  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.split(',')
    }
    return value
  })
  @IsArray()
  @IsString({ each: true })
  subCategories?: string[]

  @IsOptional()
  @IsDateString()
  dateFrom?: string

  @IsOptional()
  @IsDateString()
  dateTo?: string

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  executorId?: Int

  @IsOptional()
  @IsString()
  executorIp?: string

  @IsOptional()
  @IsString()
  text?: string
}
