import { Logger } from '@nestjs/common'
import { PrismaService } from '../prisma/prisma.service'
import { RedisService } from '../redis/redis.service'
import { AdminService } from './admin.service'
import { warn } from 'src/common/utilities'
import { ADMIN_QUEUE } from './admin.constants'
import { ProcessMq, ProcessorMq } from 'src/common/decorators/bullmq.decorator'
import { InjectQueue } from '@nestjs/bullmq'
import { Queue } from 'bullmq'
import { MINUTE_MS } from 'src/utils/constants'

@ProcessorMq(ADMIN_QUEUE, {
  lockDuration: 15 * MINUTE_MS,
  stalledInterval: 15 * MINUTE_MS,
})
export class AdminProcessor {
  private readonly logger = new Logger(AdminProcessor.name)

  constructor(
    private prisma: PrismaService,
    private redis: RedisService,
    private adminService: AdminService,
    @InjectQueue(ADMIN_QUEUE) queue: Queue
  ) {
    queue
      .add(
        'generate-stats/v2',
        {},
        {
          repeat: { pattern: '10,30,50 * * * *' },
        }
      )
      .catch(warn)
  }

  @ProcessMq('generate-stats/v2')
  async generateStats() {
    const lock = await this.redis.lock(['admin:generate-stats'])
    try {
      const start = Date.now()
      await this.adminService.generateDayStats()
      const elapsed = Date.now() - start
      return { elapsed }
    } catch (err) {
      this.logger.error({ err }, 'Failed to generate stats')
      return { err: err.message, stack: err.stack }
    } finally {
      await lock.release()
    }
  }
}
