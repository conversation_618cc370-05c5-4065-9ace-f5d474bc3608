import { ConfigService } from '@nestjs/config'
import { Injectable, Logger } from '@nestjs/common'
import { Alert, AlertType, ThresholdAlert } from './alerts.interface'
import axios, { AxiosError } from 'axios'
import { md5, warn } from 'src/common/utilities'
import { RedisService } from 'src/modules/redis/redis.service'
import { MINUTE_S } from 'src/utils/constants'

@Injectable()
export class AlertsService {
  private logger = new Logger(AlertsService.name)
  private PROCESS_KEY: string

  static TYPE_DESCRIPTIONS: Record<AlertType, string> = {
    [AlertType.OUTAGE]: 'System Outage',
    [AlertType.DEGRADED]: 'Degraded',
    [AlertType.THIRD_PARTY]: '3rd Party Outage',
    [AlertType.WARN]: 'Warning',
    [AlertType.INFO]: 'Info',
  }

  constructor(
    private config: ConfigService,
    private redis: RedisService
  ) {
    this.PROCESS_KEY = [
      config.get('siteCode'),
      config.get('serverId'),
      global.INSTANCE_ID,
    ].join('-')
  }

  #getKey(alert: Alert) {
    const identifiers = [
      alert.type,
      alert.message,
      alert.isProcessBound ? this.PROCESS_KEY : null,
    ]
    return md5(identifiers.join())
  }

  async alertOnThreshold(alert: ThresholdAlert) {
    alert.threshold = alert.threshold ?? 3
    alert.delaySeconds = alert.delaySeconds ?? 5 * MINUTE_S

    const key = this.#getKey(alert) + ':threshold'
    const [count] = await Promise.all([
      this.redis.client.incr(key),
      this.redis.client.expire(key, alert.delaySeconds),
    ]).catch((err) => {
      warn(err)
      return [alert.threshold]
    })

    if (count >= alert.threshold) {
      return await this.alert(alert)
    } else {
      this.logger.log(
        `Threshold alert %d/%d alert=%o`,
        count,
        alert.threshold,
        alert
      )
    }
  }

  async alert(alert: Alert) {
    const key = await this.#getKey(alert)
    const isDuplicate = await this.#handleDuplicates(key)
    if (isDuplicate) {
      this.logger.log('Discarding alert duplicate %o', alert)
      return
    }
    this.logger.log('Sending alert %o', alert)

    const message =
      `${alert.message}\n\n` +
      `${alert.description ?? 'No description'}\n\n` +
      `Process bound: ${alert.isProcessBound ? 'Yes' : 'No'}`

    await this.#sendPushover(alert.type, message)
  }

  async #handleDuplicates(key: string): Promise<boolean> {
    const isDupe = await this.redis.get(key).catch(() => false)
    if (isDupe) {
      return true
    }
    this.redis.set(key, 1, 15 * MINUTE_S).catch(warn)

    return false
  }

  async #sendPushover(type: AlertType, message: string) {
    const { appToken, group } = this.config.get('alerts.pushover')
    if (!appToken) {
      return
    }

    await axios
      .post('https://api.pushover.net/1/messages.json', {
        token: appToken,
        user: group,
        title: `[${this.PROCESS_KEY}] ${AlertsService.TYPE_DESCRIPTIONS[type]}`,
        message,
      })
      .catch((err) => {
        this.logger.error(
          `Failed to send pushover alert type=%s message=%s err=%s res=%o`,
          type,
          message,
          err.message,
          err.response?.data
        )
      })
  }

  axiosDescription(err: AxiosError) {
    return (
      `Message: ${err.message}\n` +
      `Response: ${JSON.stringify(err.response?.data ?? null, null, 2)}`
    )
  }
}
