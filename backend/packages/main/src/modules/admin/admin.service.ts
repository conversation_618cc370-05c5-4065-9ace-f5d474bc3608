import { BadRequestException, Injectable, Logger } from '@nestjs/common'
import { InventoryStatus, Prisma } from '@prisma/client'
import { PrismaService } from '../prisma/prisma.service'
import { NotifyUpdateDto } from './dto/notify-update.dto'
import { RedisService } from '../redis/redis.service'
import { STAFF_ROLES } from 'src/common/constants'
import { DAY_MS } from 'src/utils/constants'
import {
  Int,
  DayActiveUsersStat,
  DayGameBreakdownStat,
  DayNewUsersStat,
  DayStats,
  DayWagerStat,
  FtdStats,
  MASTERMIND_SYNC_ACP,
  MASTERMIND_SYNC_PAYMENTS,
  MASTERMIND_SYNC_QUEUE,
  MastermindSyncDayStats,
  MastermindSyncPayments,
  OutstandingBalancesStat,
  PremiumUserNumStat,
  REAL_CURRENCIES,
  RewardAndFillSpendStat,
  RewardBreakdownStat,
  safeBigintConversion,
  TotalXpLostStat,
  WithdrawableIntroducedStat,
} from '@crashgg/common/dist'
import { Queue } from 'bullmq'
import { InjectQueue } from '@nestjs/bullmq'
import { ConfigService } from '@nestjs/config'
import { assert } from 'src/utils/assert'
import { AuthService } from '../auth/auth.service'
import { UserService } from '../user/user.service'
import { AdminNotificationsService } from './admin-notifications/admin-notifications.service'
import { IpLockAccess } from '../ip-lock/ip-lock.access'
import { WITHDRAWABLE_INTRODUCED_KEY } from '../user/user.constants'
import { Role } from 'src/common/user-id.decorator'
import { NotificationEventName } from './admin-notifications/admin-notifications.interface'

@Injectable()
export class AdminService {
  private readonly logger = new Logger(AdminService.name)

  constructor(
    private prisma: PrismaService,
    private redis: RedisService,
    private readonly config: ConfigService,
    private readonly adminNotifications: AdminNotificationsService,
    private readonly userService: UserService,
    private readonly authService: AuthService,
    private readonly ipLockAccess: IpLockAccess,
    @InjectQueue(MASTERMIND_SYNC_QUEUE) private mastermindQueue: Queue
  ) {}

  async getTopWithdrawals() {
    const withdrawals = await this.prisma.tradeoffer.findMany({
      where: {
        type: 'withdraw',
        balanceChange: { lte: -100_00 },
      },
      select: {
        id: true,
        externalId: true,
        balanceChange: true,
        createdAt: true,
        status: true,
        user: {
          select: {
            name: true,
            id: true,
            steamId: true,
          },
        },
        items: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 50,
    })

    return withdrawals.map((withdrawal) => ({
      ...withdrawal,
      items: withdrawal.items.map((item) => item.name),
    }))
  }

  async unlinkDiscord(discordId: string) {
    await this.prisma.user
      .update({
        where: { discordId },
        data: { discordId: null },
      })
      .catch(() => {
        throw new BadRequestException('account_not_found')
      })

    return true
  }

  get notify() {
    return this.adminNotifications.notify.bind(this.adminNotifications)
  }

  async getStatsv2() {
    const days = await this.prisma.read.adminStats.findMany({
      orderBy: { day: 'desc' },
      take: 90,
    })

    return days
  }

  async generateDayStats() {
    const timed = async <T>(name: string, promise: Promise<T>) => {
      const now = Date.now()
      const result = await promise
      const time = Date.now() - now
      this.logger.log(`Stats: ${name} took ${time}ms`)

      return result
    }

    const start = new Date()
    const [
      wager,
      activeUsers,
      premiumUsers,
      users,
      gameBreakdown,
      outstandingBalances,
      rewardAndFillSpend,
      rewardBreakdown,
      totalXpLost,
      ftdStats,
      withdrawableIntroduced,
    ] = await Promise.all([
      timed('getDayWager', this.#getDayWager()),
      timed('getDayActiveUsers', this.#getDayActiveUsers()),
      timed('getPremiumUserNum', this.#getPremiumUserNum()),
      timed('getDayNewUsers', this.#getDayNewUsers()),
      timed('getDayGameBreakdown', this.#getDayGameBreakdown()),
      timed('getOutstandingBalances', this.#getOutstandingBalances()),
      timed('getRewardAndFillSpend', this.#getRewardAndFillSpend()),
      timed('getRewardBreakdown', this.#getRewardBreakdown()),
      timed('getTotalXpLost', this.#getTotalXpLost()),
      timed('getFtdStats', this.#getFtdStats()),
      timed('getWithdrawableIntroduced', this.#getWithdrawableIntroduced()),
    ])

    const data: DayStats = {
      wager,
      activeUsers,
      premiumUsers,
      ...users,
      gameBreakdown,
      outstandingBalances,
      ...rewardAndFillSpend,
      rewardBreakdown,
      totalXpLost,
      ...ftdStats,
      withdrawableIntroduced,
    }

    const day = new Date(start)
    day.setHours(0, 0, 0, 0)

    await this.prisma.direct.adminStats.upsert({
      where: { day },
      create: { day, data: data as any },
      update: { data: data as any },
    })
    const elapsed = Date.now() - start.getTime()
    this.logger.log(`Updated stats elapsed=%dms`, elapsed)

    const isTenant = this.config.get('isTenant')
    if (isTenant) {
      const tenantId = this.config.get('tenantId')

      await this.mastermindQueue.add(MASTERMIND_SYNC_ACP, {
        tenantId,
        stats: data,
        day,
      } satisfies MastermindSyncDayStats)

      await this.pushPaymentHistoryToMastermind()
    }
  }

  private async pushPaymentHistoryToMastermind() {
    const tenantId = this.config.get('tenantId')
    const last = await this.redis.getJSON<{ date: Date }>(
      `mastermind:payment-history:last:${tenantId}`
    )
    const to = new Date()

    await this.redis.set(`mastermind:payment-history:last:${tenantId}`, {
      date: to,
    })

    const payments = await this.prisma.payment.findMany({
      where: {
        createdAt: {
          ...(last && { gte: last.date }),
          lt: to,
        },
      },
      select: {
        id: true,
        type: true,
        createdAt: true,
        amountUsd: true,
        provider: true,
      },
    })

    await this.mastermindQueue.add(MASTERMIND_SYNC_PAYMENTS, {
      tenantId,
      payments,
    } satisfies MastermindSyncPayments)
  }

  async #getDayActiveUsers(): Promise<DayActiveUsersStat> {
    const res = await this.prisma.readSlow
      .$queryRaw`SELECT COUNT(DISTINCT("userId"))::int FROM "Transaction"
        WHERE DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())`
    return res[0].count
  }

  async #getDayWager(): Promise<DayWagerStat> {
    const res = await this.prisma.readSlow
      .$queryRaw`SELECT sum("betAmount")::int FROM "Bet"
    WHERE DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())
    AND "currency" IN (${Prisma.join(REAL_CURRENCIES)})`
    return res[0]?.sum ?? 0
  }

  async #getPremiumUserNum(): Promise<PremiumUserNumStat> {
    return await this.prisma.readSlow.user.count({
      where: {
        premiumUntil: { gte: new Date() },
      },
    })
  }

  async #getDayNewUsers(): Promise<DayNewUsersStat> {
    const getNewUsers = this.prisma.readSlow
      .$queryRaw`SELECT count(*)::int FROM "User"
      WHERE DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())`
    const getTotalUsers = this.prisma.user.count()
    const [newUsers, totalUsers] = await Promise.all([
      getNewUsers,
      getTotalUsers,
    ])

    return { newUsers: newUsers[0].count ?? 0, totalUsers }
  }

  async #getRewardAndFillSpend(): Promise<RewardAndFillSpendStat> {
    const getRewardSpend = this.prisma.readSlow
      .$queryRaw`SELECT sum("balanceChange")::int FROM "Transaction"
      WHERE category = 'reward'
      AND "message" LIKE '%(REAL)'
      AND DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())`

    const getAdminCredits = this.prisma.readSlow
      .$queryRaw`SELECT sum("balanceChange")::int FROM "Transaction"
      WHERE category = 'internal'
      AND message LIKE 'Credited by admin%(REAL)'
      AND DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())`

    const getAdminTips = this.prisma.readSlow
      .$queryRaw`SELECT abs(sum("balanceChange"))::int AS sum FROM "Transaction" AS t
        INNER JOIN "User" AS u ON u.id = t."userId"
        WHERE u.role IN (${Prisma.join(STAFF_ROLES)})
        AND message LIKE 'Tip to%'
        AND DATE_TRUNC('day', t."createdAt") = DATE_TRUNC('day', NOW())`

    const getWtfTransfer = this.prisma.readSlow
      .$queryRaw`SELECT sum("balanceChange")::int FROM "Transaction"
      WHERE category = 'internal'
      AND message LIKE 'Site transfer from CSGOLive/WTFSkins%'
      AND DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())`

    const [rewardSpend, adminCredits, adminTips, wtfTransfer] =
      await Promise.allSettled([
        getRewardSpend,
        getAdminCredits,
        getAdminTips,
        getWtfTransfer,
      ])

    const unwrap = <T>(res: PromiseSettledResult<T>, fn: (v: T) => any) => {
      if (res.status === 'fulfilled') {
        return fn(res.value)
      }
      return null
    }

    return {
      rewardSpend: unwrap(rewardSpend, (v) => v[0].sum) || -1,
      adminCredits: unwrap(adminCredits, (v) => v[0].sum) || -1,
      adminTips: unwrap(adminTips, (v) => v[0].sum) || -1,
      wtfTransfer: unwrap(wtfTransfer, (v) => v[0].sum || -1),
    }
  }

  async #getDayGameBreakdown(): Promise<DayGameBreakdownStat[]> {
    const breakdown: DayGameBreakdownStat[] = await this.prisma.readSlow
      .$queryRaw`SELECT game,
      SUM("betAmount")::int AS "betAmount",
      SUM("winningAmount")::int AS "winningAmount"
      FROM "Bet"
      WHERE DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())
      AND "currency" IN (${Prisma.join(REAL_CURRENCIES)})
      GROUP BY game
      ORDER BY 2 DESC`

    return breakdown
  }

  async #getOutstandingBalances(): Promise<OutstandingBalancesStat> {
    const userWhere: Prisma.UserWhereInput = {
      role: { notIn: STAFF_ROLES },
      OR: [
        { bannedUntil: null },
        { bannedUntil: { lte: new Date(Date.now() + 90 * DAY_MS) } },
      ],
    }

    const userResults = await this.prisma.readSlow.user.aggregate({
      _sum: {
        balance: true,
        balanceCrypto: true,
        balanceVault: true,
      },
      where: userWhere,
    })

    const inventoryResults = await this.prisma.readSlow.inventoryItem.aggregate(
      {
        where: { status: InventoryStatus.AVAILABLE, user: userWhere },
        _sum: { price: true },
      }
    )

    return safeBigintConversion(
      userResults._sum.balance +
        userResults._sum.balanceCrypto +
        userResults._sum.balanceVault +
        BigInt(inventoryResults._sum?.price || 0)
    )
  }

  async #getRewardBreakdown(): Promise<RewardBreakdownStat> {
    const depositBonus = this.config.get('payments.core.depositBonusLimit')

    const results = await this.prisma.readSlow.$queryRaw`SELECT
      (SELECT SUM("balanceChange")::int FROM "Transaction" WHERE message LIKE 'Rain reward (REAL)%'
        AND DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())) AS rain,
      (SELECT SUM("balanceChange")::int FROM "Transaction" WHERE message LIKE 'Battle rain reward (REAL)%'
        AND DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())) AS "battleRain",
      (SELECT SUM("balanceChange")::int FROM "Transaction" WHERE message LIKE 'Rakeback claim%'
        AND DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())) AS rakeback,
      (SELECT SUM("balanceChange")::int FROM "Transaction" WHERE message LIKE 'Claimed promo%'
        AND DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())) AS promocodes,
      (SELECT SUM(("item"->>'price')::int)::int FROM "CaseDrop"
        WHERE
          ("item"->>'isReward')::boolean = true
          AND DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())
       ) AS "freeCases",
      (SELECT SUM("balanceChange")::int FROM "Transaction" WHERE message LIKE 'Claimed reff%'
        AND DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())) AS affiliates,
      (SELECT SUM("balanceChange")::int FROM "Transaction" WHERE message LIKE 'Claimed case earnings%'
        AND DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())) AS "caseEarnings",
      (SELECT SUM("balanceChange")::int FROM "Transaction" WHERE message LIKE 'Race winnings%'
        AND DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())) AS "raceClaims",
      (SELECT SUM("balanceChange")::int FROM "Transaction" WHERE message LIKE 'Loaned%'
        AND DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())) AS "levelLoans-taken",
      (SELECT SUM("balanceChange")::int FROM "Transaction" WHERE message LIKE 'Repaid loan%'
        AND DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())) AS "levelLoans-repaid",
      (SELECT abs(sum(${depositBonus}-"depositBonusLeft")) FROM "User" WHERE "depositBonusLeft"<>${depositBonus})::int
        AS "depositBonus",
      (SELECT SUM("balanceChange")::int FROM "Transaction" WHERE message LIKE 'Completed mission%'
        AND DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())) AS "missions",
      (SELECT SUM("winningAmount")::int FROM "Bet" WHERE game='lossback-case'
        AND DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())) AS "lossback-case",
      (SELECT SUM("balanceChange")::int FROM "Transaction"
        WHERE (message LIKE 'Affiliate leaderboard creation%'
        OR message LIKE 'Affiliate giveaway creation%')
        AND DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())
      ) AS "creator-spent",
      (SELECT SUM((cd."item"->>'price')::int)::int
        FROM "CaseDrop" cd
        JOIN "Case" c ON c.id = cd."caseId"
        WHERE c.type = 'seasonal'
          AND DATE_TRUNC('day', cd."createdAt") = DATE_TRUNC('day', NOW())
      ) AS "seasonal-case",
      (
        SELECT SUM("balanceChange")::int FROM "Transaction"
        WHERE message LIKE 'Seasonal event reward%'
          AND DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())
      ) AS "seasonal",
      (
        SELECT SUM("balanceChangeSite")::int FROM "Transaction"
        WHERE message LIKE 'Exchange bonus currency to % (BON)'
          AND DATE_TRUNC('day', "createdAt") = DATE_TRUNC('day', NOW())
      ) AS "bonusBalanceExchange"`

    const isValid = Array.isArray(results) && results.length === 1
    if (!isValid) {
      throw new Error('Invalid reward breakdown: ' + JSON.stringify(results))
    }

    return results[0]
  }

  async notifyUpdate(body: NotifyUpdateDto) {
    return this.redis.wsBroadcast('app:update-available', body)
  }

  async #getTotalXpLost(): Promise<TotalXpLostStat> {
    const res = await this.redis.get('level-loans:total-xp-lost')
    return Number(res)
  }

  async #getFtdStats(): Promise<FtdStats> {
    const today = new Date(new Date().setUTCHours(0, 0, 0, 0))

    const stats = await this.prisma.readSlow.payment.aggregate({
      _sum: {
        amountUsd: true,
      },
      _count: {
        isFtd: true,
      },
      where: {
        isFtd: true,
        type: 'deposit',
        createdAt: {
          gte: today,
        },
      },
    })

    return {
      ftdCount: stats._count.isFtd,
      totalFtd: stats._sum?.amountUsd || 0,
    }
  }

  async #getWithdrawableIntroduced(): Promise<WithdrawableIntroducedStat> {
    return Number(await this.redis.get(WITHDRAWABLE_INTRODUCED_KEY)) || 0
  }

  async resetUserPassword(userId: Int) {
    const user = await this.userService.byId(userId)
    assert(user?.email, 'email_not_found')

    await this.authService.forgotPassword(user.email, '24h')
  }

  async getUserIpDetails(userId: Int, lastDays?: Int, limit?: Int) {
    const withinLastMs = lastDays ? lastDays * DAY_MS : Date.now()

    const userIps = await this.ipLockAccess.getUserIps(
      userId,
      withinLastMs,
      limit
    )
    const usersOnIps = await this.ipLockAccess.getUsersOnIps(
      userIps.map((ip) => ip.address),
      withinLastMs,
      limit
    )

    const userIpsWithDetails = userIps.map((userIp) => {
      const usersOnIp = usersOnIps.find((u) => u.address === userIp.address)

      return {
        ...userIp,
        users: usersOnIp?.userIds || [],
      }
    })

    return {
      userIps: userIpsWithDetails,
    }
  }

  async changeUserRole(
    executorUserId: Int,
    userId: Int,
    role: string,
    ip?: string
  ) {
    assert(executorUserId !== userId, 'cannot_change_own_role')

    const executor = await this.userService.byId(executorUserId)
    assert(executor?.role, 'executor_not_found')

    const accessibleRoles = {
      admin: ['staff', 'mod', 'helper', 'user'],
      management: ['mod', 'helper', 'user'],
    }[executor.role as Role]

    assert(accessibleRoles.includes(role), 'invalid_role_change')

    await this.userService.update(userId, {
      role,
    })

    this.adminNotifications.notify({
      text: `User ${userId} role changed to ${role} by ${executorUserId}`,
      eventName: NotificationEventName.USER_ROLE_CHANGE,
      executorId: executorUserId,
      executorIp: ip,
    })
  }
}
