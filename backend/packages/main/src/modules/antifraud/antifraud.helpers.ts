import {
  AntifraudResponse,
  AntifraudDecision,
  SiftScore,
  SiftWorkflow,
} from './antifraud.types'

export const convertAntifraudResponseToDecision = ({
  antifraudResponse,
  ignoreDecisionIds,
}: {
  antifraudResponse: AntifraudResponse
  ignoreDecisionIds?: string[]
}): AntifraudDecision[] => {
  if (!antifraudResponse?.data?.scores) {
    return []
  }

  const decisions: AntifraudDecision[] = []

  decisions.push(...extractSiftScores(antifraudResponse))
  decisions.push(...extractSiftWorkflows(antifraudResponse, ignoreDecisionIds))

  return decisions
}

export const extractSiftScores = (
  antifraudResponse: AntifraudResponse
): SiftScore[] => {
  if (!antifraudResponse?.data?.scores) {
    return []
  }

  return Object.entries(antifraudResponse.data.scores)
    .filter(([_, score]) => score.score >= 0.7)
    .map(
      ([scoreName, score]) =>
        ({
          type: 'SCORE',
          name: scoreName,
          value: score.score,
        }) satisfies SiftScore
    )
}

export const extractSiftWorkflows = (
  antifraudResponse: AntifraudResponse,
  ignoreDecisionIds: string[] = []
): SiftWorkflow[] => {
  if (!antifraudResponse?.data?.workflow_statuses) {
    return []
  }

  return antifraudResponse.data.workflow_statuses
    .filter((workflowStatus) => {
      const decision = workflowStatus.history.find((s) => s.app === 'decision')
      return (
        decision?.config &&
        !ignoreDecisionIds.includes(decision.config.decision_id)
      )
    })
    .map((workflowStatus) => {
      const decision = workflowStatus.history.find((s) => s.app === 'decision')
      return {
        type: 'WORKFLOW',
        decision: decision.config?.decision_id,
      } satisfies SiftWorkflow
    })
}
