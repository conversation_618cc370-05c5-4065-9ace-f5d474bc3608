import { Injectable } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import {
  USER_REGISTER_EVENT,
  UserRegisterEvent,
} from '../auth/events/user-register.event'
import { AntifraudService } from './antifraud.service'
import {
  USER_LOGIN_EVENT,
  UserLoginEvent,
} from '../auth/events/user-login.event'
import {
  USER_LOGOUT_EVENT,
  UserLogoutEvent,
} from '../auth/events/user-logout.event'
import {
  PLACE_BET_EVENT,
  PlaceBetEvent,
  FIAT_RELEASE_EVENT,
  FiatReleaseEvent,
} from '@crashgg/common'
import { KYC_EVENT, KycEvent } from '../payments/antifraud/kyc/events/kyc.event'
import {
  EMAIL_VERIFIED_EVENT,
  EmailVerifiedEvent,
} from '../auth/events/email-verified.event'
import {
  PASSWORD_CHANGE_EVENT,
  PasswordChangeEvent,
} from '../auth/events/password-change.event'
import { DEPOSIT_EVENT, DepositEvent } from '../payments/events/deposit.event'
import {
  WITHDRAWAL_EVENT,
  WithdrawalEvent,
} from '../payments/events/withdrawal.event'
import {
  PROMOTION_USE,
  PromotionUseEvent,
} from 'src/common/events/promotion-use.event'
import {
  TRANSFER_EVENT,
  TransferEvent,
} from '../payments/events/transfer.event'
import {
  USER_ZERO_BALANCE,
  ZeroBalanceEvent,
} from '../user/events/zero-balance.event'

@Injectable()
export class AntifraudListener {
  constructor(private antifraudService: AntifraudService) {}

  @OnEvent(USER_REGISTER_EVENT, { async: true })
  async onUserRegister(userRegisterEvent: UserRegisterEvent) {
    return this.antifraudService.sendRegisterEvent(userRegisterEvent)
  }

  @OnEvent(USER_LOGIN_EVENT, { async: true })
  async onUserLogin(userLoginEvent: UserLoginEvent) {
    return this.antifraudService.sendLoginEvent(userLoginEvent)
  }

  @OnEvent(USER_LOGOUT_EVENT, { async: true })
  async onUserLogout(userLogoutEvent: UserLogoutEvent) {
    return this.antifraudService.sendLogoutEvent(userLogoutEvent)
  }

  @OnEvent(PLACE_BET_EVENT, { async: true })
  async onBetPlaced(placeBetEvent: PlaceBetEvent) {
    return this.antifraudService.sendPlaceBetEvent(placeBetEvent)
  }

  @OnEvent(KYC_EVENT, { async: true })
  async onKyc(kycEvent: KycEvent) {
    return this.antifraudService.sendKycEvent(kycEvent)
  }

  @OnEvent(DEPOSIT_EVENT, { async: true })
  async onDeposit(depositEvent: DepositEvent) {
    return this.antifraudService.sendDepositEvent(depositEvent)
  }

  @OnEvent(WITHDRAWAL_EVENT)
  async onWithdrawal(withdrawalEvent: WithdrawalEvent) {
    return await this.antifraudService.sendWithdrawalEvent(withdrawalEvent)
  }

  @OnEvent(PROMOTION_USE, { async: true })
  async onPromotionUse(promotionEvent: PromotionUseEvent) {
    return this.antifraudService.sendPromotionUseEvent(promotionEvent)
  }

  @OnEvent(FIAT_RELEASE_EVENT, { async: true })
  async onFiatRelease(fiatReleaseEvent: FiatReleaseEvent) {
    return this.antifraudService.sendFiatReleaseEvent(fiatReleaseEvent)
  }

  @OnEvent(PASSWORD_CHANGE_EVENT, { async: true })
  async onPasswordChange(passwordChangeEvent: PasswordChangeEvent) {
    return this.antifraudService.sendPasswordChangeEvent(passwordChangeEvent)
  }

  @OnEvent(EMAIL_VERIFIED_EVENT, { async: true })
  async onEmailVerified(emailVerifiedEvent: EmailVerifiedEvent) {
    return this.antifraudService.sendEmailVerifiedEvent(emailVerifiedEvent)
  }

  @OnEvent(USER_ZERO_BALANCE, { async: true })
  async onZeroBalance(zeroBalanceEvent: ZeroBalanceEvent) {
    return this.antifraudService.sendZeroBalanceEvent(zeroBalanceEvent)
  }

  @OnEvent(TRANSFER_EVENT)
  async onTransfer(transferEvent: TransferEvent) {
    return await this.antifraudService.sendTransferEvent(transferEvent)
  }
}
