import { ScoreResponse } from './sift/sift-sdk/sift-sdk.types'

export interface AntifraudResponse {
  type: 'antifraud-score'
  data: ScoreResponse
}

export type AntifraudDecision = SiftScore | SiftWorkflow | BackendBlock

export interface SiftScore {
  type: 'SCORE'
  name: string
  value: Float
}

export interface SiftWorkflow {
  type: 'WORKFLOW'
  decision: string
}

export interface BackendBlock {
  type: 'BACKEND_BLOCK'
  name: string
}

export enum FraudNotificationType {
  DETECT = 'DETECT',
  BLOCK = 'BLOCK',
}

export interface FraudNotification {
  type: FraudNotificationType
  message?: string
  userId: Int
  actionName: string
  decision: AntifraudDecision
  withdrawal?: {
    amount: Int
    provider: string
  }
}
