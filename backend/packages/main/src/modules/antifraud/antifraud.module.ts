import { Module } from '@nestjs/common'
import { AntifraudService } from './antifraud.service'
import { AntifraudListener } from './antifraud.listener'
import { SiftModule } from './sift/sift.module'
import { AdminModule } from '../admin/admin.module'
import { UserModule } from '../user/user.module'

@Module({
  providers: [AntifraudService, AntifraudListener],
  imports: [SiftModule, AdminModule, UserModule],
  exports: [AntifraudService],
})
export class AntifraudModule {}
