import { Injectable } from '@nestjs/common'
import { SiftService } from './sift/sift.service'
import { UserRegisterEvent } from '../auth/events/user-register.event'
import { UserLoginEvent } from '../auth/events/user-login.event'
import { UserLogoutEvent } from '../auth/events/user-logout.event'
import { DepositEvent } from '../payments/events/deposit.event'
import {
  WithdrawalEvent,
  WithdrawalStatus,
} from '../payments/events/withdrawal.event'
import { KycEvent } from '../payments/antifraud/kyc/events/kyc.event'
import { UserLimitationEvent } from '../user/events/user-limitation.event'
import { PromotionUseEvent } from 'src/common/events/promotion-use.event'
import { GEMCENTS } from './sift/sift.constants'
import {
  DAY_S,
  FiatReleaseEvent,
  PlaceBetEvent,
  REAL_CURRENCIES,
  RedisService,
  sleep,
} from '@crashgg/common/dist'
import { EmailVerifiedEvent } from '../auth/events/email-verified.event'
import {
  PasswordChangeEvent,
  PasswordChangeType,
} from '../auth/events/password-change.event'
import {
  ANTIFRAUD_SCORE,
  isAutomaticallyBlockedKey,
} from './antifraud.constants'
import {
  AntifraudResponse,
  FraudNotification,
  FraudNotificationType,
} from './antifraud.types'
import { ScoreResponse } from './sift/sift-sdk/sift-sdk.types'
import { TransferEvent } from '../payments/events/transfer.event'
import { ConfigService } from '@nestjs/config'
import { AdminService } from '../admin/admin.service'
import { UserService } from '../user/user.service'
import { ZeroBalanceEvent } from '../user/events/zero-balance.event'
import { EventOptions } from './sift/sift.types'
import { NotificationEventName } from '../admin/admin-notifications/admin-notifications.interface'

@Injectable()
export class AntifraudService {
  constructor(
    private siftService: SiftService,
    private readonly config: ConfigService,
    private readonly adminService: AdminService,
    private readonly redis: RedisService,
    private readonly userService: UserService
  ) {}

  get centsToMicros() {
    return this.siftService.centsToMicros
  }

  async sendRegisterEvent(userRegisterEvent: UserRegisterEvent) {
    const signOnType = {
      google: '$google',
      steam: '$other',
    }[userRegisterEvent.oauthProvider]

    return await this.siftService.sendEvent({
      type: '$create_account',
      userId: userRegisterEvent.userId,
      payload: {
        $user_email: userRegisterEvent.email,
        $social_sign_on_type: signOnType,
      },
    })
  }

  async sendLoginEvent(userLoginEvent: UserLoginEvent) {
    return await this.siftService.sendEvent({
      type: '$login',
      userId: userLoginEvent.userId,
    })
  }

  async sendLogoutEvent(userLogoutEvent: UserLogoutEvent) {
    return await this.siftService.sendEvent({
      type: '$logout',
      userId: userLogoutEvent.userId,
    })
  }

  async sendPlaceBetEvent(placeBetEvent: PlaceBetEvent) {
    if (!REAL_CURRENCIES.includes(placeBetEvent.currency)) {
      return
    }

    // Right after rain, add jitter to not get ratelimited
    if (new Date().getMinutes() % 30 === 0) {
      await sleep(Math.random() * 30_000)
    }

    return await this.siftService.sendEvent({
      type: 'bet',
      userId: placeBetEvent.userId,
      payload: {
        amount: placeBetEvent.amount,
        game: placeBetEvent.game,
      },
    })
  }

  private _providerToPaymentMethod(
    provider: string,
    context?: Record<string, any>
  ) {
    return (
      {
        zen: { $payment_type: '$credit_card' },
        paytech: {
          $payment_type: '$credit_card',
          $card_bin: context?.cardInfo?.bin,
          $card_last4: context?.cardInfo?.lastFourDigits,
        },
        paycom: { $payment_type: '$credit_card' },
        fireblocks: { $payment_type: '$crypto_currency' },
        giftcard: { $payment_type: '$gift_card' },
      }[provider] || { $payment_type: '$digital_wallet' }
    )
  }

  async sendDepositEvent(depositEvent: DepositEvent) {
    const paymentMethod = this._providerToPaymentMethod(
      depositEvent.provider,
      depositEvent.context
    )

    return await this.siftService.sendEvent({
      type: '$transaction',
      userId: depositEvent.userId,
      payload: {
        $transaction_type: '$deposit',
        $amount: this.siftService.centsToMicros(depositEvent.amountUsd),
        $currency_code: 'USD',
        $transaction_id: this.siftService.toExternalId(
          depositEvent.transactionId
        ),
        $payment_method: {
          ...paymentMethod,
        },
      },
    })
  }

  async sendWithdrawalEvent(
    withdrawalEvent: WithdrawalEvent
  ): Promise<AntifraudResponse | null> {
    const paymentMethod = this._providerToPaymentMethod(
      withdrawalEvent.provider
    )

    const TRANSACTION_STATUSES: Record<WithdrawalStatus, string> = {
      [WithdrawalStatus.INTENT]: '$pending',
      [WithdrawalStatus.SUCCESS]: '$success',
      [WithdrawalStatus.FAILED]: '$failure',
    }
    const transactionStatus = TRANSACTION_STATUSES[withdrawalEvent.status]

    const isDecision = withdrawalEvent.status === WithdrawalStatus.INTENT
    const method = isDecision ? 'sendEventAndGetDecision' : 'sendEvent'

    const data = await this.siftService[method]({
      type: '$transaction',
      userId: withdrawalEvent.userId,
      payload: {
        $transaction_type: '$withdrawal',
        $amount: this.siftService.centsToMicros(withdrawalEvent.amountUsd),
        $currency_code: 'USD',
        $transaction_status: transactionStatus,
        $transaction_id: this.siftService.toExternalId(
          withdrawalEvent.transactionId
        ),
        $payment_method: {
          ...paymentMethod,
        },
      },
    })

    if (isDecision) {
      return {
        type: ANTIFRAUD_SCORE,
        data: data as ScoreResponse,
      }
    }

    return null
  }

  async sendEmailVerifiedEvent(emailVerifiedEvent: EmailVerifiedEvent) {
    return await this.siftService.sendEvent({
      type: '$verification',
      userId: emailVerifiedEvent.userId,
      payload: {
        $session_id: emailVerifiedEvent.sessionId,
        $status: '$success',
        $verified_event: '$create_account',
        $verification_type: '$email',
        $verified_value: emailVerifiedEvent.email,
      },
    })
  }

  async sendPasswordChangeEvent(passwordChangeEvent: PasswordChangeEvent) {
    const status =
      passwordChangeEvent.type === PasswordChangeType.INTENT
        ? '$pending'
        : '$success'

    return await this.siftService.sendEvent({
      type: '$update_password',
      userId: passwordChangeEvent.userId,
      payload: {
        $reason: '$forgot_password',
        $status: status,
      },
    })
  }

  async sendFiatReleaseEvent(fiatReleaseEvent: FiatReleaseEvent) {
    const { userId, ...payload } = fiatReleaseEvent
    return await this.siftService.sendEvent({
      type: 'fiat_release',
      userId,
      payload,
    })
  }

  // TODO: Rakeback, Deposit Bonus, Affiliate Claims
  async sendPromotionUseEvent(promotionEvent: PromotionUseEvent) {
    // Jitter to avoid rate limiting
    if (promotionEvent.isUseSpike) {
      await sleep(10_000 * Math.random())
    }

    const failureReason = this.siftService.getPromotionUseFailureReason(
      promotionEvent.failureReason
    )

    return await this.siftService.sendEvent({
      type: '$add_promotion',
      userId: promotionEvent.userId,
      payload: {
        $promotions: [
          {
            $promotion_id: promotionEvent.promotionId,
            $description: promotionEvent.promotionDescription,
            $status: promotionEvent.isSuccessful ? '$success' : '$failure',
            $failure_reason: failureReason,
            $credit_point: {
              $amount: this.siftService.centsToMicros(promotionEvent.amount),
              $credit_point_type: GEMCENTS,
            },
          },
        ],
      },
    })
  }

  async sendUserLimitationEvent(userLimitationEvent: UserLimitationEvent) {
    const { userId, ...payload } = userLimitationEvent

    return await this.siftService.sendEvent({
      type: 'user_limitation',
      userId,
      payload,
    })
  }

  async sendKycEvent(kycEvent: KycEvent) {
    const eventType = kycEvent.isSuccess ? 'kyc_completed' : 'kyc_failed'

    return await this.siftService.sendEvent({
      type: eventType,
      userId: kycEvent.userId,
      payload: {
        country: kycEvent.country,
        user_email: kycEvent.email,
        mobile_phone: kycEvent.phoneNumber,
        name: kycEvent.name,
      },
    })
  }

  async sendTransferEvent(
    transferEvent: TransferEvent
  ): Promise<AntifraudResponse | null> {
    const { isDecisionNeeded } = transferEvent
    const method = isDecisionNeeded ? 'sendEventAndGetDecision' : 'sendEvent'

    const getStatus = (isDecision: boolean, isSuccessful: boolean) => {
      if (isDecision) {
        return '$pending'
      }
      return isSuccessful ? '$success' : '$failure'
    }

    const data = await this.siftService[method]({
      type: '$transaction',
      userId: transferEvent.userId,
      payload: {
        $transaction_type: '$transfer',
        $amount: this.siftService.centsToMicros(transferEvent.amountUsd),
        $currency_code: 'USD',
        $transaction_status: getStatus(
          isDecisionNeeded,
          transferEvent.isSuccessful
        ),
        $transaction_id: this.siftService.toExternalId(
          transferEvent.transactionId
        ),
      },
    })

    if (isDecisionNeeded) {
      return {
        type: ANTIFRAUD_SCORE,
        data: data as ScoreResponse,
      }
    }

    return null
  }

  async isAutomaticallyBlocked(userId: Int) {
    const key = isAutomaticallyBlockedKey(userId)
    return !!(await this.redis.get(key))
  }

  async markUserAsAutomaticallyBlocked(userId: Int) {
    const key = isAutomaticallyBlockedKey(userId)
    await this.redis.set(key, new Date().getTime(), 30 * DAY_S)
  }

  async blockUser(userId: Int) {
    await this.markUserAsAutomaticallyBlocked(userId)
    await this.userService.withdrawLock(userId, 'on', 'automatic antifraud')
  }

  async fraudNotifyDiscord(notification: FraudNotification) {
    const messageTitle = {
      [FraudNotificationType.DETECT]: ':orange_square: **Risky user detected**',
      [FraudNotificationType.BLOCK]: `:red_square:  **Risky ${notification.actionName} blocked**`,
    }[notification.type]
    const siteCode = this.config.get('siteCode')

    let message =
      `${messageTitle}\n` + `User: **${siteCode}-${notification.userId}**\n`

    if (notification.withdrawal) {
      const amountHuman = (notification.withdrawal.amount / 100).toFixed(2)
      const provider = notification.withdrawal.provider.toUpperCase()

      message += `Withdrawal: **${amountHuman}** via **${provider}**\n`
    }

    const decision = notification.decision
    if (decision.type === 'SCORE') {
      notification.message = `:1234: Score ${decision.name} is ${decision.value}`
    } else if (decision.type === 'WORKFLOW') {
      notification.message = `:arrows_counterclockwise: Workflow decision`
    } else if (decision.type === 'BACKEND_BLOCK') {
      notification.type = FraudNotificationType.BLOCK
      notification.message =
        `:dart: Backend rule **${decision.name}**\n` + `<@&1205146807978430514>`
    }

    message += notification.message

    const shouldNotify = await this.shouldSendNotification(
      notification.userId,
      notification.type,
      message
    )
    if (!shouldNotify) return

    const channel =
      notification.type === FraudNotificationType.BLOCK
        ? 'antifraud-block'
        : 'antifraud'

    const eventName =
      notification.type === FraudNotificationType.BLOCK
        ? NotificationEventName.SIFT_BLOCK
        : NotificationEventName.SIFT_DETECT

    this.adminService.notify({
      text: message,
      channel,
      eventName,
      executorId: notification.userId,
    })
  }

  private async shouldSendNotification(
    userId: Int,
    type: FraudNotificationType,
    message: string
  ) {
    const key = `antifraud:notify:${userId}:${type}`
    const cooldownDays = this.config.get('antifraud.notificationCooldownDays')
    const lastNotify = await this.redis.get(key)

    if (lastNotify && lastNotify === message) {
      return false
    }

    await this.redis.set(key, message, cooldownDays * DAY_S)
    return true
  }

  async sendZeroBalanceEvent(zeroBalanceEvent: ZeroBalanceEvent) {
    this.siftService.sendEvent({
      type: 'zero_balance',
      userId: zeroBalanceEvent.userId,
    })
  }

  async sendCustomEvent(customEvent: EventOptions) {
    return await this.siftService.sendEvent(customEvent)
  }
}
