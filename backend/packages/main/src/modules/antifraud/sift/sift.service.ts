import { Injectable, Logger } from '@nestjs/common'
import { SiftSdkService } from './sift-sdk/sift-sdk.service'
import { ConfigService } from '@nestjs/config'
import { noop } from '@crashgg/common/dist'
import { EventOptions } from './sift.types'
import { PromotionUseFailureReason } from 'src/common/events/promotion-use.event'
import retry from 'async-retry'

@Injectable()
export class SiftService {
  private readonly logger = new Logger(SiftService.name)

  constructor(
    private sdk: SiftSdkService,
    private config: ConfigService
  ) {}

  async sendEvent(options: EventOptions) {
    if (!this.config.get('antifraud.sift.apiKey')) {
      return
    }

    const externalUserId = this.toExternalId(options.userId)

    return await retry(
      async (bail) => {
        await this.sdk
          .sendEvent({
            ...options,
            userId: externalUserId,
          })
          .catch((err) => {
            const res = err.response?.data
            if ([50, 51, 52, 53].includes(res?.status)) {
              bail(err)
              return
            }

            throw err
          })
      },
      { minTimeout: 2000, retries: 3 }
    ).catch(noop)
  }

  async sendEventAndGetDecision(options: EventOptions) {
    if (!this.config.get('antifraud.sift.apiKey')) {
      return
    }

    const externalUserId = this.toExternalId(options.userId)
    this.logger.log('Decision request options=%o', options)

    const res = await this.sdk.sendEventAndGetDecision({
      ...options,
      userId: externalUserId,
    })

    return res.score_response
  }

  toExternalId(id: Int | string): string {
    const tag = this.config.get('siteCode')

    return `${tag}:${id}`
  }

  centsToMicros(cents: Int): Int {
    return Math.round(cents * 10_000)
  }

  getPromotionUseFailureReason(failureReason: PromotionUseFailureReason) {
    const FAILURE_REASONS: Record<PromotionUseFailureReason, string> = {
      [PromotionUseFailureReason.ALREADY_USED]: '$already_used',
      [PromotionUseFailureReason.EXPIRED]: '$expired',
      [PromotionUseFailureReason.INVALID_CODE]: '$invalid_code',
      [PromotionUseFailureReason.NOT_APPLICABLE]: '$not_applicable',
    }
    return FAILURE_REASONS[failureReason]
  }
}
