export type SiftPayload = Record<string, any>

export interface SiftEventOptions {
  type: string
  userId: string
  payload?: SiftPayload
}

export interface WorkflowDecisionRes {
  status: number
  error_message: string
  request: string
  time: number
  score_response: ScoreResponse
}

export interface ScoreResponse {
  status: number
  error_message: string
  user_id: string
  scores: Scores
  latest_labels: LatestLabels
  workflow_statuses: WorkflowStatusesItem[]
}

interface Scores {
  payment_abuse: PaymentAbuse
  promotion_abuse: PromotionAbuse
}

interface PaymentAbuse {
  score?: number
  percentiles?: Percentiles
  reasons?: ReasonsItem[]
  is_fraud?: boolean
  time?: number
  description?: string
}

interface Percentiles {
  last_1_day: number
  last_5_days: number
  last_7_days: number
  last_10_days: number
}

interface ReasonsItem {
  name: string
  value: number
  details: Details
}

interface Details {
  users: string
}

interface PromotionAbuse {
  score?: number
  percentiles?: Percentiles
  reasons?: any[]
  is_fraud?: boolean
  time?: number
}

interface LatestLabels {
  payment_abuse: PaymentAbuse
  promotion_abuse: PromotionAbuse
}

interface WorkflowStatusesItem {
  id: string
  state: string
  config: Config
  config_display_name: string
  abuse_types: string[]
  entity: Entity
  history: HistoryItem[]
  route: Route
}

interface Config {
  id?: string
  version?: string
  decision_id?: string
}

interface Entity {
  type: string
  id: string
}

interface HistoryItem {
  app: string
  name: string
  state: string
  config?: Config
}

interface Route {
  name: string
}
