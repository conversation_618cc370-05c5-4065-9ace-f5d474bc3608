import { Injectable, Logger, ServiceUnavailableException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import axios, { Method } from 'axios'
import {
  SiftEventOptions,
  SiftPayload,
  WorkflowDecisionRes,
} from './sift-sdk.types'

@Injectable()
export class SiftSdkService {
  private logger = new Logger(SiftSdkService.name)

  constructor(private config: ConfigService) {}

  async sendEvent(eventOptions: SiftEventOptions) {
    const { type, userId, payload } = eventOptions

    return await this._request<any>('POST', 'events', {
      $type: type,
      $user_id: userId,
      ...payload,
    })
  }

  async sendEventAndGetDecision(eventOptions: SiftEventOptions) {
    const { type, userId, payload } = eventOptions

    return await this._request<WorkflowDecisionRes>(
      'POST',
      'events?return_workflow_status=true',
      {
        $type: type,
        $user_id: userId,
        ...payload,
      }
    )
  }

  private async _request<T>(
    method: Method,
    path: string,
    data: SiftPayload = {}
  ): Promise<T> {
    data.$api_key = this.config.get('antifraud.sift.apiKey')

    const res = await axios
      .request({
        method,
        url: `https://api.sift.com/v205/${path}`,
        data,
      })
      .catch((err) => {
        this.logger.warn(
          `Sift request failed err=%o data=%o req=%o`,
          err.message,
          err.response?.data,
          data
        )
        throw new ServiceUnavailableException('sift_down')
      })

    this.logger.log('Sift res=%o', res.data)

    return res.data
  }
}
