import { Module } from '@nestjs/common'
import { AffiliatesService } from './affiliates.service'
import { AffiliatesController } from './affiliates.controller'
import { UserModule } from '../user/user.module'
import { NotificationsModule } from '../user/notifications/notifications.module'
import { MissionsModule } from '../rewards/missions/missions.module'
import { AFFILIATES_QUEUE } from './affiliates.constants'
import { AffiliatesProcessor } from './affiliates.processor'
import { ScaleoModule } from '../conversion/scaleo/scaleo.module'
import { AntifraudModule } from '../antifraud/antifraud.module'
import { PassModule } from '../auth/pass/pass.module'
import { Feature } from '@crashgg/common/dist'
import { AffiliateLeaderboardModule } from './affiliate-leaderboard/affiliate-leaderboard.module'
import { ConditionalModule } from '../config/conditional-module'
import { AffiliateGiveawayModule } from './affiliate-giveaway/affiliate-giveaway.module'
import { BullModule } from '@nestjs/bullmq'
import { AffiliatesListener } from './affiliates.listener'

@Module({
  imports: [
    UserModule,
    MissionsModule,
    NotificationsModule,
    AntifraudModule,
    PassModule,
    ScaleoModule,
    BullModule.registerQueue({ name: AFFILIATES_QUEUE }),
    ConditionalModule.forFeature(
      AffiliateLeaderboardModule,
      Feature.AffiliateLeaderboard
    ),
    ConditionalModule.forFeature(
      AffiliateGiveawayModule,
      Feature.AffiliateGiveaway
    ),
  ],
  providers: [AffiliatesService, AffiliatesProcessor, AffiliatesListener],
  controllers: [AffiliatesController],
  exports: [AffiliatesService],
})
export class AffiliatesModule {}
