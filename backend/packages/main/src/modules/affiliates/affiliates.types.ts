import { Prisma } from '@prisma/client'
import { PUBLIC_USER_FIELDS } from 'src/common/constants'

export interface TimeResetPayload {
  userId: Int
}

export enum GraphVariant {
  Week = 'week',
  Month = 'month',
  Year = 'year',
}

export interface EarningsToAccumulate {
  day: Date
  userId: Int
  referredById: Int
  count: Int
  wager: Int
  deposit: Int
  ftd: Int
  earned: Float
}

export interface UserSummaryOptions {
  userId: Int
  since?: Date
  until?: Date
  orderByKey?: 'wager' | 'deposit' | 'earned'
  take?: Int
}

type UserSummaryResultUser = Prisma.UserGetPayload<{
  select: Omit<typeof PUBLIC_USER_FIELDS, 'id'>
}>

export type UserSummaryResult = UserSummaryResultUser & {
  userId: Int
  active: boolean
  wagered: Int
  deposited: Int
  earned: Int
}
