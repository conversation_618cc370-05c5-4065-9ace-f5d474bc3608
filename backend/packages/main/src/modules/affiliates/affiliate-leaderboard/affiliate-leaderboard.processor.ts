import {
  AFFILIATE_LEADERBOARD_QUEUE,
  FINALIZE_LEADERBOARD_JOB,
  START_SCHEDULED_LEADERBOARDS_JOB,
  UPDATE_LEADERBOARD_JOB,
} from './affiliate-leaderboard.constants'
import { AffiliateLeaderboardAccess } from './affiliate-leaderboard.access'

import { AffiliateLeaderboardService } from './affiliate-leaderboard.service'
import { ProcessMq, ProcessorMq } from 'src/common/decorators/bullmq.decorator'
import { InjectQueue } from '@nestjs/bullmq'
import { Job, Queue } from 'bullmq'
import { warn } from 'src/common/utilities'
import { Logger } from '@nestjs/common'

@ProcessorMq(AFFILIATE_LEADERBOARD_QUEUE)
export class AffiliateLeaderboardProcessor {
  private logger = new Logger(AffiliateLeaderboardProcessor.name)

  constructor(
    private alAccess: AffiliateLeaderboardAccess,
    private alService: AffiliateLeaderboardService,
    @InjectQueue(AFFILIATE_LEADERBOARD_QUEUE) private queue: Queue
  ) {
    this.queue
      .add(START_SCHEDULED_LEADERBOARDS_JOB, null, {
        attempts: 1,
        repeat: { pattern: '0 * * * *' },
      })
      .catch(warn)
  }

  @ProcessMq(START_SCHEDULED_LEADERBOARDS_JOB)
  async startScheduled() {
    await this.alService.startScheduledLeaderboards()
  }

  @ProcessMq(FINALIZE_LEADERBOARD_JOB)
  async finalize(job: Job<Int>) {
    const id = job.data
    await this.alService.finalizeLeaderboard(id)
  }

  @ProcessMq(UPDATE_LEADERBOARD_JOB)
  async update(job: Job<Int>) {
    const id = job.data
    this.logger.log('Updating leaderboard=%d', id)
    await this.alService.updateLeaderboard(id)
    this.logger.log('Updating leaderboard=%d done', id)
  }
}
