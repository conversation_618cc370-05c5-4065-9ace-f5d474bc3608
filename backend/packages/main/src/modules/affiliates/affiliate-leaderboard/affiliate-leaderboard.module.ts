import { Module, forwardRef } from '@nestjs/common'
import { AffiliateLeaderboardController } from './affiliate-leaderboard.controller'
import { AffiliateLeaderboardService } from './affiliate-leaderboard.service'
import { UserModule } from 'src/modules/user/user.module'
import { AffiliatesModule } from '../affiliates.module'
import { AffiliateLeaderboardProcessor } from './affiliate-leaderboard.processor'
import { AffiliateLeaderboardAccess } from './affiliate-leaderboard.access'
import { BullModule } from '@nestjs/bullmq'
import { AFFILIATE_LEADERBOARD_QUEUE } from './affiliate-leaderboard.constants'
import { NotificationsModule } from 'src/modules/user/notifications/notifications.module'
import { KycModule } from 'src/modules/payments/antifraud/kyc/kyc.module'
import { PassModule } from 'src/modules/auth/pass/pass.module'

@Module({
  imports: [
    UserModule,
    KycModule,
    BullModule.registerQueue({ name: AFFILIATE_LEADERBOARD_QUEUE }),
    NotificationsModule,
    PassModule,
    forwardRef(() => AffiliatesModule),
  ],
  providers: [
    AffiliateLeaderboardService,
    AffiliateLeaderboardProcessor,
    AffiliateLeaderboardAccess,
  ],
  controllers: [AffiliateLeaderboardController],
})
export class AffiliateLeaderboardModule {}
