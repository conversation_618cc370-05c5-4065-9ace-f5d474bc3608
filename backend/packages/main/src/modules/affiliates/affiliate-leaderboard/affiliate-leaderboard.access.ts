import { PrismaAny, PrismaService, PrismaTx } from '@crashgg/common/dist'
import { BadRequestException, Injectable } from '@nestjs/common'
import { AffiliateLeaderboardStatus as AfStatus, Prisma } from '@prisma/client'
import { PUBLIC_USER_FIELDS } from 'src/common/constants'
import { PaginatedDto } from 'src/common/dto/paginated.dto'
import { paginate } from 'src/common/paginate'

@Injectable()
export class AffiliateLeaderboardAccess {
  constructor(private prisma: PrismaService) {}

  async getLeaderboards(
    where: Prisma.AffiliateLeaderboardWhereInput,
    prisma?: PrismaAny,
    paginationOptions?: PaginatedDto
  ) {
    const client = prisma || this.prisma
    const leaderboards = await client.affiliateLeaderboard.findMany({
      omit: { createdAt: true, updatedAt: true },
      include: {
        code: {
          select: { user: { select: PUBLIC_USER_FIELDS }, code: true },
        },
      },
      where,
      ...(paginationOptions && paginate(paginationOptions)),
    })

    return leaderboards
  }

  async updateMany(
    where: Prisma.AffiliateLeaderboardWhereInput,
    data: Prisma.AffiliateLeaderboardUncheckedUpdateManyInput
  ) {
    return await this.prisma.affiliateLeaderboard.updateManyAndReturn({
      where,
      data,
    })
  }

  async createLeaderboard(
    userId: Int,
    data: Omit<Prisma.AffiliateLeaderboardCreateInput, 'code' | 'status'>,
    prisma?: PrismaTx
  ) {
    try {
      return await (prisma || this.prisma).affiliateLeaderboard.create({
        data: {
          ...data,
          status: AfStatus.SCHEDULED,
          code: { connect: { userId } },
        },
      })
    } catch (err) {
      if (err.code === 'P2002') {
        throw new BadRequestException('leaderboard_already_exists')
      }

      throw err
    }
  }

  async updateLeaderboard(
    id: Int,
    data: Prisma.AffiliateLeaderboardUpdateInput,
    prisma?: PrismaTx
  ) {
    return await (prisma || this.prisma).affiliateLeaderboard.update({
      where: { id },
      data,
    })
  }
}
