import { Expose, Transform } from 'class-transformer'
import { LeaderboardPlayer } from '../affiliate-leaderboard.types'
import { AffiliateLeaderboardType, UserBadge } from '@prisma/client'
import { PUBLIC, STAFF } from 'src/common/constants'
import { censor } from 'src/common/utilities'

export class PublicLeaderboardPlayer implements LeaderboardPlayer {
  @Transform(({ value }) => censor(value), { groups: [PUBLIC] })
  name: string

  role: string
  avatar: string
  xp: bigint
  badge: UserBadge | null
  isPrivate: boolean
  premiumUntil: Date
  active: boolean

  @Expose({ groups: [STAFF] }) userId: Int
  @Expose({ groups: [AffiliateLeaderboardType.WAGER] }) wagered: Int
  @Expose({ groups: [AffiliateLeaderboardType.DEPOSIT] }) deposited: Int
  @Expose({ groups: [STAFF] }) earned: Float

  constructor(partial: Partial<LeaderboardPlayer>) {
    Object.assign(this, partial)
  }
}
