import {
  AffiliateLeaderboard,
  AffiliateLeaderboardStatus,
  AffiliateLeaderboardType,
} from '@prisma/client'
import { JsonValue } from '@prisma/client/runtime/library'
import { Exclude, Expose, Type } from 'class-transformer'
import { PublicLeaderboardPlayer } from './public-leaderboard-player.entity'
import { ValidateNested } from 'class-validator'
import { STAFF } from 'src/common/constants'

export class PublicAffiliateLeaderboard implements AffiliateLeaderboard {
  name: string
  id: number
  creatorId: number
  startDate: Date
  durationDays: number
  type: AffiliateLeaderboardType
  isRecurring: boolean
  status: AffiliateLeaderboardStatus
  rewards: JsonValue

  @ValidateNested({ each: true })
  @Type(() => PublicLeaderboardPlayer)
  topPlayers: JsonValue

  @Expose({ groups: [STAFF] }) winnerIds: number[]
  @Exclude() createdAt: Date
  @Exclude() updatedAt: Date

  constructor(partial: Partial<AffiliateLeaderboard>) {
    Object.assign(this, partial)
  }
}
