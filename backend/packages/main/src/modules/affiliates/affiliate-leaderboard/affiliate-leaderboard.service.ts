import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>_MS,
  HOUR_MS,
  PrismaService,
  PrismaTx,
  RedisService,
  REWARD_RECEIVE_EVENT,
  RewardReceiveEvent,
  RewardType,
} from '@crashgg/common/dist'
import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common'
import { UserService } from 'src/modules/user/user.service'
import { CreateDto } from './dto/create.dto'
import { AffiliatesService } from '../affiliates.service'
import { AffiliateLeaderboardAccess } from './affiliate-leaderboard.access'
import {
  AffiliateLeaderboard,
  AffiliateLeaderboardStatus as AfStatus,
  Prisma,
  UserBadge,
} from '@prisma/client'
import { SUCCESS } from 'src/common/constants'
import {
  AFFILIATE_LEADERBOARD_QUEUE,
  FINALIZE_LEADERBOARD_JOB,
  UPDATE_LEADERBOARD_JOB,
} from './affiliate-leaderboard.constants'
import { LeaderboardPlayer, Reward } from './affiliate-leaderboard.types'
import { retryableAllSettled, warn } from 'src/common/utilities'
import { InjectQueue } from '@nestjs/bull'
import { Queue } from 'bull'
import { assert } from 'src/utils/assert'
import { UpdateDto } from './dto/update.dto'
import { NotificationsService } from 'src/modules/user/notifications/notifications.service'
import { USD } from 'src/utils/conversion'
import { SearchDto } from './dto/search.dto'
import { paginated } from 'src/common/paginate'
import { EventEmitter2 } from '@nestjs/event-emitter'

@Injectable()
export class AffiliateLeaderboardService {
  private logger = new Logger(AffiliateLeaderboardService.name)

  constructor(
    private prisma: PrismaService,
    private redis: RedisService,
    private userService: UserService,
    private alAccess: AffiliateLeaderboardAccess,
    private affiliatesService: AffiliatesService,
    private notificationsService: NotificationsService,
    @InjectQueue(AFFILIATE_LEADERBOARD_QUEUE) private queue: Queue,
    private readonly eventEmitter: EventEmitter2
  ) {}

  async create(userId: Int, data: CreateDto) {
    await this.affiliatesService.findCodeByUserId(userId)

    const startDate = new Date(data.startDate)
    startDate.setUTCMinutes(0, 0, 0)
    const now = new Date()
    now.setUTCMinutes(0, 0, 0)
    assert(startDate >= now, 'min_date_hour_now')

    const rewards = data.rewards
    const sum = rewards.reduce((acc, reward) => acc + reward.amount, 0)

    const user = await this.userService.byId(userId)
    if (user?.badge !== UserBadge.VERIFIED) {
      await this.affiliatesService.emitTransferByAffiliateEvent({
        userId,
        amountUsd: sum,
        transactionId: `affiliate-leaderboard-${userId}-${Date.now()}`,
      })
    }

    const lock = await this.redis.lock([`charge:${userId}`])
    try {
      return await this.prisma.$transaction(async (tx) => {
        await this.userService.charge(
          {
            userId,
            amount: sum,
            transaction: {
              category: 'reward',
              message: `Affiliate leaderboard creation`,
            },
            currency: Currency.REAL,
          },
          tx
        )

        let leaderboard = await this.alAccess.createLeaderboard(
          userId,
          {
            ...data,
            startDate,
          },
          tx
        )
        if (startDate.getTime() === now.getTime()) {
          leaderboard = await this.startLeaderboard(leaderboard.id, tx)
        }

        return leaderboard
      })
    } finally {
      lock.release()
    }
  }

  async update(userId: Int, id: Int, data: UpdateDto) {
    const [leaderboard] = await this.alAccess.getLeaderboards({
      creatorId: userId,
      id,
    })

    assert(leaderboard, 'leaderboard_not_found', NotFoundException)
    assert(leaderboard.status !== AfStatus.ENDED, 'wont_change_past')

    if (leaderboard.status === AfStatus.LIVE) {
      const hasImmutableField = !!(
        data.startDate ||
        data.durationDays ||
        data.type
      )
      if (hasImmutableField) {
        throw new BadRequestException('cant_change_live_leaderboard')
      }
    }

    await this.alAccess.updateLeaderboard(id, data)

    return SUCCESS
  }

  async finalizeLeaderboard(id: Int) {
    const [leaderboard] = await this.alAccess.getLeaderboards({ id })
    assert(this._isEnding(leaderboard))

    const winners = this._mapWinners(
      leaderboard.topPlayers as unknown as LeaderboardPlayer[],
      leaderboard.rewards as Reward[]
    )

    await this._creditWinners(winners)
    await this.alAccess.updateLeaderboard(id, {
      winnerIds: winners.map((winner) => winner[0]),
      status: AfStatus.ENDED,
    })

    if (leaderboard.isRecurring) await this.recreateRecurring(leaderboard)
  }

  private _isEnding(
    leaderboard: Pick<
      AffiliateLeaderboard,
      'status' | 'startDate' | 'durationDays'
    >
  ) {
    if (leaderboard.status !== AfStatus.LIVE) return false
    return this._getEndDate(leaderboard) <= new Date()
  }

  private _getEndDate(
    leaderboard: Pick<AffiliateLeaderboard, 'startDate' | 'durationDays'>
  ) {
    const { startDate, durationDays } = leaderboard
    const endDate = new Date(startDate)
    endDate.setDate(startDate.getDate() + durationDays)

    return endDate
  }

  async recreateRecurring(
    lb: Omit<AffiliateLeaderboard, 'createdAt' | 'updatedAt'>
  ) {
    const startDate = new Date()
    startDate.setUTCMinutes(0, 0, 0)

    try {
      await this.create(lb.creatorId, {
        name: lb.name,
        durationDays: lb.durationDays,
        type: lb.type,
        isRecurring: lb.isRecurring,
        rewards: lb.rewards as any,
        startDate,
      })
    } catch (err) {
      if (err.message === 'insufficient_balance') {
        await this.notificationsService.add({
          userId: lb.creatorId,
          category: 'reward',
          title: 'Leaderboard creation failed',
          text: `Your leaderboard ${lb.name} could not be recreated due to \
                 insufficient balance. Please top up your account and try again.`,
        })
        return
      }
      this.logger.warn(
        `Failed to recreate recurring affiliate leaderboard=%o err=%o`,
        lb,
        err
      )
    }
  }

  async startLeaderboard(id: Int, prisma?: PrismaTx) {
    const leaderboard = await this.alAccess.updateLeaderboard(
      id,
      {
        status: AfStatus.LIVE,
      },
      prisma
    )
    await this.queue.add(UPDATE_LEADERBOARD_JOB, id)

    return leaderboard
  }

  async startScheduledLeaderboards() {
    const started = await this.alAccess.updateMany(
      { startDate: { lte: new Date() }, status: AfStatus.SCHEDULED },
      { status: AfStatus.LIVE }
    )

    if (started.length === 0) return

    await this.queue.addBulk(
      started.map((lb) => ({
        name: UPDATE_LEADERBOARD_JOB,
        data: lb.id,
        opts: { delay: HOUR_MS },
      }))
    )
  }

  private _mapWinners(topPlayers: LeaderboardPlayer[], rewards: Reward[]) {
    return rewards.slice(0, topPlayers.length).map((reward, position) => {
      const entry = topPlayers[position]
      return [entry.userId, reward.amount] as [number, number]
    })
  }

  private async _creditWinners(winners: [Int, Int][]) {
    const creditPerUser = async (userId: Int, amount: Int) => {
      await this.prisma.user.update({
        where: { id: userId },
        data: { unclaimedLottery: { increment: amount } },
      })

      this.eventEmitter.emit(
        REWARD_RECEIVE_EVENT,
        new RewardReceiveEvent({
          userId,
          reward: RewardType.AF_LEADERBOARD,
          amount,
        })
      )

      const amountHuman = USD.toString(amount)
      await this.notificationsService
        .add({
          userId,
          category: 'reward',
          title: 'Affiliate leaderboard winnings',
          text:
            `Congratulations! You have won ${amountHuman} gems from affilaite leaderboard!` +
            ' Claim it on the leaderboard page.',
        })
        .catch(warn)
    }

    const creditsFactory = winners.map(([userId, amount]) => {
      return () => creditPerUser(userId, amount)
    })

    return await retryableAllSettled(creditsFactory)
  }

  async updateTopPlayers(
    leaderboard: Pick<
      AffiliateLeaderboard,
      'id' | 'creatorId' | 'startDate' | 'type' | 'durationDays'
    >
  ): Promise<LeaderboardPlayer[]> {
    const { id, creatorId, startDate, type } = leaderboard
    const endDate = this._getEndDate(leaderboard)
    this.logger.log('Updating leaderboard=%d endDate=%s', id, endDate)

    const orderByKey = type === 'WAGER' ? 'wager' : 'deposit'
    const topPlayers = await this.affiliatesService.getUserSummary({
      userId: creatorId,
      since: startDate,
      until: endDate,
      orderByKey,
      take: 100,
    })
    this.logger.log('Updating leaderboard=%d found=%d', id, topPlayers.length)

    await this.alAccess.updateLeaderboard(id, {
      topPlayers: topPlayers as any,
    })

    return topPlayers
  }

  async updateLeaderboard(id: Int) {
    const [leaderboard] = await this.alAccess.getLeaderboards({ id })
    await this.updateTopPlayers(leaderboard)

    if (this._isEnding(leaderboard)) {
      this.queue.add(FINALIZE_LEADERBOARD_JOB, id)
    } else {
      this.queue.add(UPDATE_LEADERBOARD_JOB, id, { delay: HOUR_MS })
    }
  }

  async getParticipating(userId: Int) {
    const use = await this.affiliatesService.findUse(userId)
    if (!use?.referredById) return []

    const leaderboards = await this.alAccess.getLeaderboards(
      { creatorId: use.referredById },
      this.prisma.read
    )

    return leaderboards.filter((leaderboard) => {
      if (leaderboard.status !== AfStatus.ENDED) return true

      const endDate = this._getEndDate(leaderboard)
      return Date.now() - endDate.getTime() < 7 * DAY_MS
    })
  }

  async searchLeaderboards(query: SearchDto) {
    const { status, creatorId, value, startDate, endDate, page, pageSize } =
      query
    const paginationOptions = { pageSize: pageSize ?? 100, page }

    const where: Prisma.AffiliateLeaderboardWhereInput = {
      ...(status && { status }),
      ...(creatorId && { creatorId }),
      ...(value && {
        rewards: { array_contains: [{ amount: Number(value) }] },
      }),
      ...(startDate && { startDate: { gte: new Date(startDate) } }),
      ...(endDate && { startDate: { lte: new Date(endDate) } }),
    }

    const leaderboards = await this.alAccess.getLeaderboards(
      where,
      this.prisma.read,
      paginationOptions
    )
    const count = await this.prisma.read.affiliateLeaderboard.count({ where })

    return paginated({
      data: leaderboards,
      count,
      pageSize: paginationOptions.pageSize,
    })
  }
}
