import {
  ArrayMaxSize,
  ArrayNotEmpty,
  IsArray,
  IsBoolean,
  IsDate,
  IsIn,
  IsInt,
  IsString,
  Max,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>idateNested,
} from 'class-validator'
import { Reward } from '../affiliate-leaderboard.types'
import { AffiliateLeaderboardType } from '@prisma/client'
import { Type } from 'class-transformer'
import { RewardDto } from './reward.dto'

export class CreateDto {
  @IsIn(Object.keys(AffiliateLeaderboardType))
  type: AffiliateLeaderboardType

  @IsDate()
  @Type(() => Date)
  startDate: Date

  @IsInt()
  @Min(1)
  @Max(30)
  durationDays: Int

  @IsArray()
  @ArrayNotEmpty()
  @ArrayMaxSize(30)
  @ValidateNested({ each: true })
  @Type(() => RewardDto)
  rewards: Reward[]

  @IsBoolean()
  isRecurring: boolean

  @IsString()
  @MinLength(4)
  @Max<PERSON>ength(64)
  name: string
}
