import {
  Body,
  Controller,
  Get,
  NotFoundException,
  Param,
  ParseBoolPipe,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common'
import { AffiliateLeaderboardService } from './affiliate-leaderboard.service'
import { AccessGuard } from 'src/modules/auth/guards/access.guard'
import { CreateDto } from './dto/create.dto'
import { AffiliateLeaderboardAccess } from './affiliate-leaderboard.access'
import { Role, UserId, UserRole } from 'src/common/user-id.decorator'
import { MemoryCache } from 'src/common/decorators/memory-cache.decorator'
import { UpdateDto } from './dto/update.dto'
import { assert } from 'src/utils/assert'
import { minutes, seconds } from '@nestjs/throttler'
import { Currency, PrismaService } from '@crashgg/common/dist'
import { WithdrawLockGuard } from 'src/modules/user/withdraw-lock.guard'
import { AccessOptionalGuard } from 'src/modules/auth/guards/access-optional.guard'
import { PUBLIC, STAFF, STAFF_ROLES } from 'src/common/constants'
import { instanceToPlain } from 'class-transformer'
import { PublicAffiliateLeaderboard } from './entities/public-affiliate-leaderboard.entity'
import {
  CurrenciesUsed,
  CurrencyUsed,
  VaryCurrency,
} from 'src/common/currency.decorator'
import { ApiGuard } from 'src/modules/auth/guards/api.guard'
import { AffiliatesService } from '../affiliates.service'
import { StaffGuard } from 'src/modules/auth/guards/role.guard'
import { SearchDto } from './dto/search.dto'

@Controller('affiliates/leaderboards')
export class AffiliateLeaderboardController {
  constructor(
    private prisma: PrismaService,
    private affiliatesService: AffiliatesService,
    private alService: AffiliateLeaderboardService,
    private alAccess: AffiliateLeaderboardAccess
  ) {}

  @Post('create')
  @UseGuards(AccessGuard, WithdrawLockGuard())
  async create(
    @UserId() userId: Int,
    @Body() body: CreateDto,
    @CurrencyUsed([Currency.REAL]) _currency: Currency
  ) {
    return await this.alService.create(userId, body)
  }

  @Get('participating')
  @MemoryCache({ perUser: true, ttl: seconds(30) })
  @UseGuards(AccessGuard)
  @VaryCurrency()
  async getParticipating(
    @UserId() userId: Int,
    @CurrenciesUsed([Currency.REAL]) _currencies: Currency[]
  ) {
    const data = await this.alService.getParticipating(userId)
    return { data }
  }

  @Get('my-leaderboards')
  @MemoryCache({ perUser: true, ttl: seconds(30) })
  @UseGuards(AccessGuard)
  @VaryCurrency()
  async getLeaderboards(
    @UserId() userId: Int,
    @CurrenciesUsed([Currency.REAL]) _currencies: Currency[]
  ) {
    const data = await this.alAccess.getLeaderboards(
      { creatorId: userId },
      this.prisma.read
    )
    return { data }
  }

  @Get('my-leaderboards-api')
  @UseGuards(ApiGuard)
  async getLeaderboardsApi(@Req() req: any) {
    const data = await this.alAccess.getLeaderboards(
      { creatorId: req.api.userId },
      this.prisma.read
    )
    return { data }
  }

  @Get('for-code/:code')
  @MemoryCache({ ttl: minutes(2) })
  async getLeaderboardsForCode(@Param('code') code: string) {
    const codeDetails = await this.affiliatesService.findCode(
      code.toUpperCase()
    )
    assert(codeDetails, 'code_not_found', NotFoundException)

    return await this.alAccess.getLeaderboards(
      { creatorId: codeDetails.userId },
      this.prisma.read
    )
  }

  @Get('search')
  @UseGuards(AccessGuard, StaffGuard)
  async searchLeaderboards(@Query() query: SearchDto) {
    return this.alService.searchLeaderboards(query)
  }

  @Patch(':id')
  @UseGuards(AccessGuard)
  async update(
    @UserId() userId: Int,
    @Param('id', ParseIntPipe) id: Int,
    @Body() body: UpdateDto
  ) {
    return await this.alService.update(userId, id, body)
  }

  @Get(':id')
  @UseGuards(AccessOptionalGuard)
  @MemoryCache({ ttl: minutes(2) })
  async getLeaderboard(
    @Param('id', ParseIntPipe) id: Int,
    @UserRole() role: Role,
    @Query('fresh', new ParseBoolPipe({ optional: true })) fresh = false
  ) {
    const isStaff = STAFF_ROLES.includes(role)
    const [leaderboard] = await this.alAccess.getLeaderboards(
      { id },
      fresh ? this.prisma : this.prisma.read
    )
    assert(leaderboard, 'leaderboard_not_found', NotFoundException)

    return instanceToPlain(new PublicAffiliateLeaderboard(leaderboard), {
      groups: [isStaff ? STAFF : PUBLIC, leaderboard.type],
    })
  }
}
