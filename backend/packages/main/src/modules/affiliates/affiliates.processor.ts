import { Logger } from '@nestjs/common'
import { PrismaService } from '../prisma/prisma.service'
import { TimeResetPayload } from './affiliates.types'
import {
  ACCUMULATE_HISTORY_JOB,
  AFFILIATES_QUEUE,
  TIME_RESET_JOB,
} from './affiliates.constants'
import { AffiliatesService } from './affiliates.service'
import { warn } from 'console'
import { chunk } from 'src/common/utilities'
import { HOUR_MS } from 'src/utils/constants'
import { UserService } from '../user/user.service'
import { ProcessMq, ProcessorMq } from 'src/common/decorators/bullmq.decorator'
import { InjectQueue } from '@nestjs/bullmq'
import { Job, Queue } from 'bullmq'

@ProcessorMq(AFFILIATES_QUEUE)
export class AffiliatesProcessor {
  private readonly logger = new Logger(AffiliatesProcessor.name)

  constructor(
    private prisma: PrismaService,
    private affiliatesService: AffiliatesService,
    private userService: UserService,
    @InjectQueue(AFFILIATES_QUEUE)
    private queue: Queue
  ) {
    this.queue
      .add(
        ACCUMULATE_HISTORY_JOB,
        {},
        {
          repeat: { pattern: '0 7 * * *' },
        }
      )
      .catch(warn)
  }

  @ProcessMq(TIME_RESET_JOB)
  async timeReset(job: Job<TimeResetPayload>) {
    const { userId } = job.data
    this.logger.log(`Time reset for user ${userId}`)

    const cooldown = await this.affiliatesService.getCodeCooldown(userId)
    const oneHourFromNow = new Date(Date.now() + HOUR_MS)
    if (cooldown > oneHourFromNow) {
      return { isReset: false }
    }

    const latestUse = await this.prisma.affiliateUse.findUnique({
      where: { userId },
    })

    const updateAffiliate = this.prisma.affiliateUse.update({
      where: { userId },
      data: { referredById: null },
    })

    const addLog = this.userService.addLog({
      userId,
      category: 'affiliates',
      message: `No longer referred by id=${latestUse.referredById}`,
    })

    await Promise.all([updateAffiliate, addLog])
    return { isReset: true }
  }

  @ProcessMq(ACCUMULATE_HISTORY_JOB)
  async accumulateHistory(job: Job<any>) {
    const earnings = await this.affiliatesService.getEarningsToAccumulate()
    if (!earnings.length) {
      return { empty: true }
    }

    // TODO: remove
    if (Math.random() < 1) {
      return {
        wouldProcess: earnings.slice(0, 10).map((e) => {
          e.count = Number(e.count)
          return e
        }),
      }
    }

    const batches = chunk(earnings, 250)

    for (const [index, batch] of batches.entries()) {
      await this.affiliatesService.handleAccumulateBatch(batch)

      const batchProgress = Math.floor((index / batches.length) * 100)
      job.updateProgress(batchProgress)
    }

    const recordsDeleted = earnings.reduce(
      (acc, e) => acc + Number(e.count) - 1,
      0
    )

    const isNextBatch = earnings.length > 1000
    if (isNextBatch) {
      await this.queue.add(ACCUMULATE_HISTORY_JOB, {}).catch(warn)
    }

    return {
      recordsDeleted,
      isNextBatch,
    }
  }
}
