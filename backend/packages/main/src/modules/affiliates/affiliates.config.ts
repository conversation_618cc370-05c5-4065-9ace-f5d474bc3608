import { registerAs } from '@nestjs/config'
import { DAY_MS } from 'src/utils/constants'

export default registerAs('affiliates', () => ({
  blacklistCodes: (process.env.BLACKLIST_CODES || 'CRASHGG').split(';'),
  restrictedUsers: (process.env.AFFILIATES_RESTRICTED_USERS || '0').split(';'),
  stickyAffiliateIds: (process.env.STICKY_AFFILIATE_IDS || '0').split(';'),
  primaryAffiliateId: Number(process.env.PRIMARY_AFFILIATE_ID || 1),
  codeCooldownMs: Number(process.env.CODE_COOLDOWN_MS || 7 * DAY_MS),
  freeCasesIds: process.env.FREE_CASES_IDS
    ? process.env.FREE_CASES_IDS.split(';').map(Number)
    : [],
  freeCasesWager: Number(process.env.FREE_CASES_WAGER || 5_00),
}))
