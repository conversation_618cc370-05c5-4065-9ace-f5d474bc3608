import {
  AFFILIATE_GIVEAWAY_QUEUE,
  DRAW_GIVEAWAY_JOB,
  DRAW_NOTIFICATION_JOB,
} from './affiliate-giveaway.constants'
import { AffiliateGiveawayService } from './affiliate-giveaway.service'
import { ProcessMq, ProcessorMq } from 'src/common/decorators/bullmq.decorator'
import { Job } from 'bullmq'

@ProcessorMq(AFFILIATE_GIVEAWAY_QUEUE)
export class AffiliateGiveawayProcessor {
  constructor(private agService: AffiliateGiveawayService) {}

  @ProcessMq(DRAW_NOTIFICATION_JOB)
  async sendDrawNotification(job: Job<Int>) {
    const id = job.data
    await this.agService.sendDrawNotification(id)
  }

  @ProcessMq(DRAW_GIVEAWAY_JOB)
  async draw(job: Job<Int>) {
    const id = job.data
    await this.agService.drawGiveaway(id)
  }
}
