import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>_MS,
  MINUTE_MS,
  PrismaService,
  RedisService,
  REWARD_RECEIVE_EVENT,
  RewardReceiveEvent,
  RewardType,
} from '@crashgg/common/dist'
import { Injectable, Logger, NotFoundException } from '@nestjs/common'
import { NotificationsService } from 'src/modules/user/notifications/notifications.service'
import { UserService } from 'src/modules/user/user.service'
import { AffiliatesService } from '../affiliates.service'
import { AffiliateGiveawayAccess } from './affiliate-giveaway.access'
import {
  AFFILIATE_GIVEAWAY_QUEUE,
  DRAW_GIVEAWAY_JOB,
  DRAW_NOTIFICATION_JOB,
} from './affiliate-giveaway.constants'
import { CreateDto } from './dto/create.dto'
import { GiveawayEntry, Reward } from './affiliate-giveaway.types'
import { mapAsync, warn } from 'src/common/utilities'
import { plainToInstance } from 'class-transformer'
import {
  AffiliateGiveaway,
  AffiliateGiveawayStatus as Ag<PERSON><PERSON><PERSON>,
  <PERSON>rism<PERSON>,
  UserBadge,
} from '@prisma/client'
import { FairnessService } from 'src/modules/fairness/fairness.service'
import seedrandom from 'seedrandom'
import { GetEosHashArgs } from 'src/modules/fairness/eos/eos.types'
import { UpdateDto } from './dto/update.dto'
import { PublicUserEntity } from 'src/modules/user/user.entity'
import { assert } from 'src/utils/assert'
import { RewardDto } from './dto/reward.dto'
import { PUBLIC_USER_FIELDS, SUCCESS } from 'src/common/constants'
import { InjectQueue } from '@nestjs/bullmq'
import { Queue } from 'bullmq'
import { USD } from 'src/utils/conversion'
import { paginated } from 'src/common/paginate'
import { SearchDto } from './dto/search.dto'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { ConfigService } from 'src/modules/config/config.service'

@Injectable()
export class AffiliateGiveawayService {
  private logger = new Logger(AffiliateGiveawayService.name)

  constructor(
    private prisma: PrismaService,
    private config: ConfigService,
    private redis: RedisService,
    private userService: UserService,
    private agAccess: AffiliateGiveawayAccess,
    private affiliatesService: AffiliatesService,
    private fairnessService: FairnessService,
    private notificationsService: NotificationsService,
    @InjectQueue(AFFILIATE_GIVEAWAY_QUEUE) private queue: Queue,
    private readonly eventEmitter: EventEmitter2
  ) {}

  async create(userId: Int, body: CreateDto) {
    const [reward] = await Promise.all([
      this._getRewardDetails(body.reward),
      this.affiliatesService.findCodeByUserId(userId),
    ])

    const user = await this.userService.byId(userId)
    if (user?.badge !== UserBadge.VERIFIED) {
      await this.affiliatesService.emitTransferByAffiliateEvent({
        userId,
        amountUsd: reward.price,
        transactionId: `affiliate-giveaway-${userId}-${Date.now()}`,
      })
    }

    const lock = await this.redis.lock([`charge:${userId}`])
    try {
      const giveaway = await this.prisma.$transaction(async (tx) => {
        await this.userService.charge(
          {
            userId,
            amount: reward.price,
            transaction: {
              category: 'reward',
              message: `Affiliate giveaway creation`,
            },
            currency: Currency.REAL,
          },
          tx
        )

        return await this.agAccess.create(
          userId,
          {
            ...body,
            startDate: new Date(),
            reward: reward as any,
          },
          tx
        )
      })

      await this._startJobs(giveaway)

      return giveaway
    } finally {
      lock.release()
    }
  }

  private async _startJobs(
    giveaway: Pick<AffiliateGiveaway, 'id' | 'startDate' | 'durationDays'>
  ) {
    const drawsAt = this._getDrawsAt(giveaway)
    const drawDelay = drawsAt.getTime() - Date.now()

    this.queue.addBulk([
      {
        name: DRAW_NOTIFICATION_JOB,
        data: giveaway.id,
        opts: {
          delay: drawDelay - MINUTE_MS,
        },
      },
      {
        name: DRAW_GIVEAWAY_JOB,
        data: giveaway.id,
        opts: {
          delay: drawDelay,
        },
      },
    ])
  }

  async update(userId: Int, id: Int, body: UpdateDto) {
    await this.agAccess.findOne({ creatorId: userId, id })
    return await this.agAccess.update(id, body)
  }

  async sendDrawNotification(id: Int) {
    const giveaway = await this.agAccess.findOne({ id })

    const entries = giveaway.entries as unknown as GiveawayEntry[]

    const sendNotfications = entries.map(async (entry) => {
      await this.notificationsService.add({
        userId: entry.userId,
        category: 'reward',
        title: 'Giveaway draw',
        text: `The ${giveaway.name} giveaway you've entered is about to be drawn!\
               Don't miss the results - your entry could be the winning one!`,
      })
    })

    await Promise.allSettled(sendNotfications)
  }

  async join(userId: Int, id: Int) {
    const use = await this.affiliatesService.findUse(userId)
    assert(use?.referredById, 'not_affiliated')

    const giveaway = await this.agAccess.findOne({
      id,
      creatorId: use.referredById,
    })

    const entries = giveaway.entries as unknown as GiveawayEntry[]
    const userStats = await this._fetchGiveawayUserStats(id, userId)
    const { hasMeetEntryRequirement } = this._checkJoinStatus(
      giveaway,
      userStats
    )

    assert(giveaway.status === AgStatus.LIVE, 'giveaway_not_live')
    assert(entries.length < giveaway.maxEntries, 'max_entries_reached')
    assert(!entries.some((entry) => entry.userId === userId), 'already_joined')
    assert(hasMeetEntryRequirement, 'doesnt_meet_entry_requirement')

    entries.push(userStats)

    await this.agAccess.update(id, { entries: entries as any })

    return SUCCESS
  }

  async cancel(userId: Int, id: Int) {
    const giveaway = await this.agAccess.findOne({
      id,
      creatorId: userId,
    })

    const entries = giveaway.entries as unknown as GiveawayEntry[]
    const reward = giveaway.reward as unknown as Reward

    assert(giveaway.status !== AgStatus.CANCELED, 'already_canceled')
    assert(entries.length <= 2, 'too_many_entries')

    const updatedGiveaway = await this.agAccess.update(id, {
      status: AgStatus.CANCELED,
    })

    await this.userService.credit(
      userId,
      reward.price,
      {
        category: 'reward',
        message: `Affiliate giveaway cancellation refund`,
      },
      Currency.REAL
    )

    return updatedGiveaway
  }

  async getGiveawayStatsForUser(userId: Int, id: Int) {
    const [use, giveaway] = await Promise.all([
      this.affiliatesService.findUse(userId),
      this.agAccess.findOne({ id }),
    ])

    assert(
      use?.referredById === giveaway.creatorId,
      'giveaway_not_found',
      NotFoundException
    )

    const userStats = await this._fetchGiveawayUserStats(id, userId)
    const joinStatus = this._checkJoinStatus(giveaway, userStats)

    return {
      ...userStats,
      ...joinStatus,
    }
  }

  async getGiveawaysForCode(code: string) {
    const codeDetails = await this.affiliatesService.findCode(
      code.toUpperCase()
    )
    assert(codeDetails, 'code_not_found', NotFoundException)

    const giveaways = await this.agAccess.find(
      { creatorId: codeDetails.userId },
      this.prisma.read
    )

    return giveaways.map((giveaway) => {
      const entries = giveaway.entries as unknown as GiveawayEntry[]
      const newEntries = entries.map((entry) => ({
        userId: entry.userId,
        name: entry.name,
        avatar: entry.avatar,
      }))

      return { ...giveaway, entries: newEntries }
    })
  }

  private _checkJoinStatus(
    giveaway: Omit<AffiliateGiveaway, 'createdAt' | 'updatedAt'>,
    userStats: GiveawayEntry
  ) {
    const entries = giveaway.entries as unknown as GiveawayEntry[]

    const hasJoined = entries
      .map((entry) => entry.userId)
      .includes(userStats.userId)

    const metricKey = giveaway.type === 'WAGER' ? 'wagered' : 'deposited'
    const metric = userStats[metricKey]
    const hasMeetEntryRequirement = metric >= giveaway.entryRequirementAmount
    const remainingAmountToJoin = Math.max(
      0,
      giveaway.entryRequirementAmount - metric
    )

    const canJoin =
      !hasJoined &&
      entries.length < giveaway.maxEntries &&
      giveaway.status === AgStatus.LIVE &&
      hasMeetEntryRequirement

    return {
      hasJoined,
      canJoin,
      hasMeetEntryRequirement,
      remainingAmountToJoin,
    }
  }

  async drawGiveaway(id: Int) {
    const lock = await this.redis.lock([`affiliate-giveaway:${id}:draw`])

    try {
      const giveaway = await this.agAccess.findOne({ id })
      assert(giveaway.status === AgStatus.LIVE)

      this.logger.log('Drawing giveaway=%o', giveaway)

      const entries = giveaway.entries as unknown as GiveawayEntry[]
      if (entries.length === 0) {
        await this.agAccess.update(id, {
          status: AgStatus.ENDED,
        })
        return
      }

      const { winnerId, fairness } = await this._determineWinner(id, entries)
      const user = await this._creditWinner(
        winnerId,
        giveaway.reward as unknown as Reward
      )

      await this.agAccess.update(id, {
        winnerId,
        status: AgStatus.ENDED,
        fairness: fairness as any,
      })

      const publicWinner = plainToInstance(PublicUserEntity, user)
      await this.agAccess.publishPublic('result', {
        giveawayId: id,
        winner: publicWinner,
        fairness,
      })

      if (giveaway.isRecurring) await this.recreateRecurring(giveaway)
    } finally {
      lock.release()
    }
  }

  async recreateRecurring(
    giveaway: Omit<AffiliateGiveaway, 'createdAt' | 'updatedAt'>
  ) {
    try {
      await this.create(giveaway.creatorId, {
        name: giveaway.name,
        durationDays: giveaway.durationDays,
        lookbackDays: giveaway.lookbackDays,
        maxEntries: giveaway.maxEntries,
        entryRequirementAmount: giveaway.entryRequirementAmount,
        type: giveaway.type,
        isRecurring: giveaway.isRecurring,
        reward: giveaway.reward as any,
      })
    } catch (err) {
      if (err.message === 'insufficient_balance') {
        await this.notificationsService.add({
          userId: giveaway.creatorId,
          category: 'reward',
          title: 'Giveaway creation failed',
          text: `Your giveaway ${giveaway.name} could not be recreated due to \
                 insufficient balance. Please top up your account and try again.`,
        })
        return
      }
      this.logger.warn(
        `Failed to recreate recurring affiliate giveaway=%o err=%o`,
        giveaway,
        err
      )
    }
  }

  private async _determineWinner(id: Int, entries: GiveawayEntry[]) {
    const onAwaiting: GetEosHashArgs['onAwaiting'] = async (payload) => {
      await this.agAccess.publishPublic('awaiting-eos', {
        giveawayId: id,
        proof: payload,
      })
    }

    const fairness = await this.fairnessService.getRandomSeed(
      {
        giveawayId: id,
      },
      onAwaiting
    )

    const total = entries.length
    const winningIndex = Math.floor(seedrandom(fairness.seed)() * total)
    const winnerId = entries[winningIndex].userId

    return { winnerId, fairness: { ...fairness, winningIndex } }
  }

  private _getDrawsAt(
    giveaway: Pick<AffiliateGiveaway, 'startDate' | 'durationDays'>
  ) {
    const { startDate, durationDays } = giveaway
    return new Date(startDate.getTime() + durationDays * DAY_MS)
  }

  private async _getRewardDetails(rewardDto: RewardDto): Promise<Reward> {
    if (rewardDto.type !== 'item') throw new Error('Invalid reward type')

    const creatorItems = await this.agAccess.getCreatorItems()
    const foundItem = creatorItems.find((item) => item.name === rewardDto.name)
    assert(foundItem, 'item_not_found')

    return { type: 'item', ...foundItem }
  }

  private async _creditWinner(userId: Int, reward: Reward) {
    const amount = reward.price
    const user = await this.prisma.user.update({
      where: { id: userId },
      data: { unclaimedLottery: { increment: amount } },
    })

    this.eventEmitter.emit(
      REWARD_RECEIVE_EVENT,
      new RewardReceiveEvent({
        userId,
        reward: RewardType.AF_GIVEAWAY,
        amount,
      })
    )

    await this.userService.addWagerReq(userId, amount).catch(warn)

    const amountHuman = USD.toString(amount)
    await this.notificationsService
      .add({
        userId,
        category: 'reward',
        title: 'Affiliate giveaway winnings',
        text:
          `Congratulations! You have won ${amountHuman} from affiliate giveaway!` +
          ' Claim it on the leaderboard page.',
      })
      .catch(warn)

    return user
  }

  async getParticipating(userId: Int) {
    const use = await this.affiliatesService.findUse(userId)
    if (!use?.referredById) return []

    const giveaways = await this.agAccess.find(
      { creatorId: use.referredById },
      this.prisma.read
    )

    const filtered = giveaways.filter((giveaway) => {
      if (giveaway.status === AgStatus.LIVE) return true

      const drawsAt = this._getDrawsAt(giveaway)
      return Date.now() - drawsAt.getTime() < 7 * DAY_MS
    })

    return await mapAsync(filtered, async (giveaway) => {
      const userStats = await this._fetchGiveawayUserStats(giveaway.id, userId)
      const joinStatus = this._checkJoinStatus(giveaway, userStats)
      return { ...giveaway, ...joinStatus }
    })
  }

  private async _fetchGiveawayUserStats(
    id: Int,
    userId: Int
  ): Promise<GiveawayEntry> {
    const { creatorId, startDate, lookbackDays } = await this.agAccess.findOne({
      id,
    })

    const effectiveStartDate = new Date(
      startDate.getTime() - lookbackDays * DAY_MS
    )

    const [user, aggregate] = await Promise.all([
      this.prisma.read.user.findUnique({
        select: {
          ...PUBLIC_USER_FIELDS,
          affiliateUse: {
            select: { referredById: true },
          },
        },
        where: { id: userId },
      }),
      this.prisma.affiliateEarnings.aggregate({
        _sum: { deposit: true, wager: true, earned: true },
        where: {
          referredById: creatorId,
          userId,
          createdAt: { gt: effectiveStartDate },
        },
      }),
    ])

    const { id: _, affiliateUse, ...publicUser } = user
    const depositUsd = aggregate._sum?.deposit || 0
    const depositBalance = Math.ceil(
      depositUsd / this.config.get('balanceUsdRate')
    )

    return {
      userId,
      ...publicUser,
      active: affiliateUse?.referredById === creatorId,
      wagered: aggregate._sum?.wager || 0,
      deposited: depositBalance,
      earned: aggregate._sum?.earned || 0,
    }
  }

  async searchGiveaways(query: SearchDto) {
    const { status, creatorId, minValue, startDate, endDate, page, pageSize } =
      query
    const paginationOptions = { pageSize: pageSize ?? 100, page }

    const where: Prisma.AffiliateGiveawayWhereInput = {
      ...(status && { status }),
      ...(creatorId && { creatorId }),
      ...(minValue && {
        reward: { path: ['price'], gte: minValue },
      }),
      ...(startDate && { startDate: { gte: new Date(startDate) } }),
      ...(endDate && { startDate: { lte: new Date(endDate) } }),
    }

    const leaderboards = await this.agAccess.find(
      where,
      this.prisma.read,
      paginationOptions
    )
    const count = await this.prisma.read.affiliateGiveaway.count({ where })

    return paginated({
      data: leaderboards,
      count,
      pageSize: paginationOptions.pageSize,
    })
  }
}
