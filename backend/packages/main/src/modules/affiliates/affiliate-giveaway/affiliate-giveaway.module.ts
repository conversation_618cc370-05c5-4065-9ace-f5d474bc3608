import { BullModule } from '@nestjs/bullmq'
import { Module, forwardRef } from '@nestjs/common'
import { NotificationsModule } from 'src/modules/user/notifications/notifications.module'
import { UserModule } from 'src/modules/user/user.module'
import { AffiliatesModule } from '../affiliates.module'
import { AffiliateGiveawayAccess } from './affiliate-giveaway.access'
import { AffiliateGiveawayController } from './affiliate-giveaway.controller'
import { AffiliateGiveawayService } from './affiliate-giveaway.service'
import { AFFILIATE_GIVEAWAY_QUEUE } from './affiliate-giveaway.constants'
import { AffiliateGiveawayProcessor } from './affiliate-giveaway.processor'
import { FairnessModule } from 'src/modules/fairness/fairness.module'
import { KycModule } from 'src/modules/payments/antifraud/kyc/kyc.module'

@Module({
  imports: [
    UserModule,
    BullModule.registerQueue({ name: AFFILIATE_GIVEAWAY_QUEUE }),
    NotificationsModule,
    FairnessModule,
    KycModule,
    forwardRef(() => AffiliatesModule),
  ],
  providers: [
    AffiliateGiveawayAccess,
    AffiliateGiveawayService,
    AffiliateGiveawayProcessor,
  ],
  controllers: [AffiliateGiveawayController],
})
export class AffiliateGiveawayModule {}
