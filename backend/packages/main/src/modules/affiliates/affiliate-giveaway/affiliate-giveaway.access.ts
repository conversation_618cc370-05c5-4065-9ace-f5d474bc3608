import {
  B<PERSON><PERSON>tem,
  PrismaAny,
  PrismaService,
  PrismaTx,
  RedisService,
} from '@crashgg/common/dist'
import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common'
import { AffiliateGiveawayStatus as AgStatus, Prisma } from '@prisma/client'
import { PUBLIC_USER_FIELDS } from 'src/common/constants'
import { PaginatedDto } from 'src/common/dto/paginated.dto'
import { paginate } from 'src/common/paginate'
import { LAST_CREATOR_ITEMS_CACHE_KEY } from 'src/modules/games/cases/cases.constants'
import { assert } from 'src/utils/assert'

@Injectable()
export class AffiliateGiveawayAccess {
  constructor(
    private prisma: PrismaService,
    private redis: RedisService
  ) {}

  async create(
    userId: Int,
    data: Omit<Prisma.AffiliateGiveawayCreateInput, 'code' | 'status'>,
    prisma?: PrismaTx
  ) {
    try {
      return await (prisma || this.prisma).affiliateGiveaway.create({
        data: {
          ...data,
          status: AgStatus.LIVE,
          code: { connect: { userId } },
        },
      })
    } catch (err) {
      if (err.code === 'P2002') {
        throw new BadRequestException('giveaway_already_exists')
      }

      throw err
    }
  }

  async findOne(where: Prisma.AffiliateGiveawayWhereInput, prisma?: PrismaAny) {
    const giveaway = await (prisma || this.prisma).affiliateGiveaway.findFirst({
      omit: { createdAt: true, updatedAt: true },
      include: {
        code: {
          select: { user: { select: PUBLIC_USER_FIELDS }, code: true },
        },
      },
      where,
    })
    assert(giveaway, 'giveaway_not_found', NotFoundException)

    return giveaway
  }

  async find(
    where: Prisma.AffiliateGiveawayWhereInput,
    prisma?: PrismaAny,
    paginationOptions?: PaginatedDto
  ) {
    return await (prisma || this.prisma).affiliateGiveaway.findMany({
      omit: { createdAt: true, updatedAt: true },
      include: {
        code: {
          select: { user: { select: PUBLIC_USER_FIELDS }, code: true },
        },
        winner: {
          select: PUBLIC_USER_FIELDS,
        },
      },
      where,
      ...(paginationOptions && paginate(paginationOptions)),
    })
  }

  async update(id: Int, data: Prisma.AffiliateGiveawayUncheckedUpdateInput) {
    return await this.prisma.affiliateGiveaway.update({
      omit: { createdAt: true, updatedAt: true },
      where: { id },
      data,
    })
  }

  async publishPublic(key: string, payload: any) {
    await this.redis.publishPublic(`affiliate-giveaway:${key}`, payload)
  }

  async getCreatorItems() {
    return await this.redis.getJSON<BuildItem[]>(LAST_CREATOR_ITEMS_CACHE_KEY)
  }
}
