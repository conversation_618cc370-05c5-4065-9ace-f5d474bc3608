import { Currency, PrismaService, RedisService } from '@crashgg/common/dist'
import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common'
import { AccessGuard } from 'src/modules/auth/guards/access.guard'
import { WithdrawLockGuard } from 'src/modules/user/withdraw-lock.guard'
import { AffiliateGiveawayAccess } from './affiliate-giveaway.access'
import { AffiliateGiveawayService } from './affiliate-giveaway.service'
import { CreateDto } from './dto/create.dto'
import { UserId } from 'src/common/user-id.decorator'
import { MemoryCache } from 'src/common/decorators/memory-cache.decorator'
import { minutes, seconds } from '@nestjs/throttler'
import { UpdateDto } from './dto/update.dto'
import {
  CurrenciesUsed,
  CurrencyUsed,
  VaryCurrency,
} from 'src/common/currency.decorator'
import { StaffGuard } from 'src/modules/auth/guards/role.guard'
import { SearchDto } from './dto/search.dto'

@Controller('affiliates/giveaways')
export class AffiliateGiveawayController {
  constructor(
    private prisma: PrismaService,
    private redis: RedisService,
    private agAccess: AffiliateGiveawayAccess,
    private agService: AffiliateGiveawayService
  ) {}

  @Post('create')
  @UseGuards(AccessGuard, WithdrawLockGuard())
  async create(
    @UserId() userId: Int,
    @Body() body: CreateDto,
    @CurrencyUsed([Currency.REAL]) _currency: Currency
  ) {
    return await this.agService.create(userId, body)
  }

  @Get('my-giveaways')
  @MemoryCache({ perUser: true, ttl: seconds(30) })
  @UseGuards(AccessGuard)
  @VaryCurrency()
  async getMyGiveaways(
    @UserId() userId: Int,
    @CurrenciesUsed([Currency.REAL]) _currencies: Currency[]
  ) {
    const data = await this.agAccess.find(
      { creatorId: userId },
      this.prisma.read
    )

    return { data }
  }

  @Get('participating')
  @MemoryCache({ perUser: true, ttl: seconds(30) })
  @UseGuards(AccessGuard)
  @VaryCurrency()
  async getParticipating(
    @UserId() userId: Int,
    @CurrenciesUsed([Currency.REAL]) _currencies: Currency[]
  ) {
    const data = await this.agService.getParticipating(userId)
    return { data }
  }

  @Get('search')
  @UseGuards(AccessGuard, StaffGuard)
  async searchGiveaways(@Query() query: SearchDto) {
    return this.agService.searchGiveaways(query)
  }

  @Get('for-code/:code')
  @MemoryCache({ ttl: minutes(2) })
  async getGiveawaysForCode(@Param('code') code: string) {
    return await this.agService.getGiveawaysForCode(code)
  }

  @Put(':id/join')
  @UseGuards(AccessGuard)
  async join(
    @UserId() userId: Int,
    @Param('id', ParseIntPipe) id: Int,
    @CurrencyUsed([Currency.REAL]) _currency: Currency
  ) {
    const lock = await this.redis.lock([`affiliate-giveaway:${id}:join`])

    return await this.agService.join(userId, id).finally(lock.release)
  }

  @Put(':id/cancel')
  @UseGuards(AccessGuard)
  async cancel(@UserId() userId: Int, @Param('id', ParseIntPipe) id: Int) {
    const lock = await this.redis.lock([
      `affiliate-giveaway:${id}:join`,
      `affiliate-giveaway:${id}:draw`,
    ])

    return await this.agService.cancel(userId, id).finally(lock.release)
  }

  @Get(':id/my-stats')
  @MemoryCache({ ttl: minutes(2), perUser: true })
  @UseGuards(AccessGuard)
  async getGiveawayStatsForUser(
    @UserId() userId: Int,
    @Param('id', ParseIntPipe) id: Int
  ) {
    return await this.agService.getGiveawayStatsForUser(userId, id)
  }

  @Patch(':id')
  @UseGuards(AccessGuard)
  async update(
    @UserId() userId: Int,
    @Param('id', ParseIntPipe) id: Int,
    @Body() body: UpdateDto
  ) {
    return await this.agService.update(userId, id, body)
  }

  @Get(':id')
  @MemoryCache({ ttl: minutes(2) })
  async getGiveaway(@Param('id', ParseIntPipe) id: Int) {
    return await this.agAccess.findOne({ id }, this.prisma.read)
  }
}
