import { AffiliateGiveawayStatus } from '@prisma/client'
import { Transform } from 'class-transformer'
import { IsDateString, IsEnum, IsInt, IsOptional } from 'class-validator'
import { PaginatedDto } from 'src/common/dto/paginated.dto'
import { transformInt } from 'src/common/utilities'

export class SearchDto extends PaginatedDto {
  @IsOptional()
  @IsEnum(AffiliateGiveawayStatus)
  status?: AffiliateGiveawayStatus

  @IsOptional()
  @Transform(transformInt)
  @IsInt()
  creatorId?: Int

  @IsOptional()
  @Transform(transformInt)
  @IsInt()
  minValue?: Int

  @IsOptional()
  @IsDateString()
  startDate?: string

  @IsOptional()
  @IsDateString()
  endDate?: string
}
