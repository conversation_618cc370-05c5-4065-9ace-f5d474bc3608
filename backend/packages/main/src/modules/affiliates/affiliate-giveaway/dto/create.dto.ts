import { AffiliateGiveawayType } from '@prisma/client'
import { Type } from 'class-transformer'
import {
  IsBoolean,
  IsIn,
  IsInt,
  IsObject,
  IsString,
  Max,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Length,
} from 'class-validator'
import { RewardDto } from './reward.dto'
import { Reward } from '../affiliate-giveaway.types'

export class CreateDto {
  @IsIn(Object.keys(AffiliateGiveawayType))
  type: AffiliateGiveawayType

  @IsInt()
  @Min(1)
  @Max(90)
  durationDays: Int

  @IsInt()
  @Min(0)
  @Max(30)
  lookbackDays: Int

  @IsBoolean()
  isRecurring: boolean

  @IsInt()
  @Min(1)
  @Max(10_000_00)
  entryRequirementAmount: Int

  @IsObject()
  @Type(() => RewardDto)
  reward: Reward

  @IsString()
  @MinLength(4)
  @MaxLength(64)
  name: string

  @IsInt()
  @Min(1)
  @Max(1000)
  maxEntries: Int
}
