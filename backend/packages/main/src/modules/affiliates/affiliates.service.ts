import {
  User,
  Prisma,
  User<PERSON>adge,
  AffiliateUse,
  RewardEarnings,
} from '@prisma/client'
import { Currency, Int } from '@crashgg/types/dist'
import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  Logger,
} from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import axios from 'axios'
import { PUBLIC_USER_FIELDS } from 'src/common/constants'
import { assert } from 'src/utils/assert'
import { PrismaService } from '../prisma/prisma.service'
import { UserService } from '../user/user.service'
import { MissionsService } from '../rewards/missions/missions.service'
import { AFFILIATES_QUEUE, TIME_RESET_JOB } from './affiliates.constants'
import {
  GraphVariant,
  EarningsToAccumulate,
  UserSummaryOptions,
  UserSummaryResult,
} from './affiliates.types'
import {
  noop,
  PrismaTx,
  REWARD_CLAIM_EVENT,
  RewardClaimEvent,
  RewardType,
} from '@crashgg/common/dist'
import { DAY_MS, MINUTE_MS } from 'src/utils/constants'
import { chunk, mapAsync, warn } from 'src/common/utilities'
import { ScaleoService } from '../conversion/scaleo/scaleo.service'
import { CASE_SUMMARY, TAGS_SUMMARY } from '../games/cases/cases.constants'
import {
  PROMOTION_USE,
  PromotionUseEvent,
} from 'src/common/events/promotion-use.event'
import { EventEmitter2 } from '@nestjs/event-emitter'
import { AntifraudService } from '../antifraud/antifraud.service'
import { InjectQueue } from '@nestjs/bullmq'
import { Queue } from 'bullmq'
import { ANTIFRAUD_SCORE } from '../antifraud/antifraud.constants'
import {
  AntifraudResponse,
  FraudNotificationType,
} from '../antifraud/antifraud.types'
import { extractSiftWorkflows } from '../antifraud/antifraud.helpers'
import {
  TRANSFER_EVENT,
  TransferEvent,
} from '../payments/events/transfer.event'
import { REWARD_DECREMENT } from '../rewards/reward-earnings/reward-earnings.constants'
import { MissionEvent } from '../rewards/missions/missions.interface'

@Injectable()
export class AffiliatesService {
  private logger = new Logger(AffiliatesService.name)
  private BLACKLIST_CODES = []
  private RESTRICTED_USERS = []

  constructor(
    private prisma: PrismaService,
    private config: ConfigService,
    private userService: UserService,
    private missionsService: MissionsService,
    private scaleoService: ScaleoService,
    @InjectQueue(AFFILIATES_QUEUE) private queue: Queue,
    private eventEmitter: EventEmitter2,
    private antifraudService: AntifraudService
  ) {
    this.BLACKLIST_CODES = config.get('affiliates.blacklistCodes')
    this.RESTRICTED_USERS = config.get('affiliates.restrictedUsers')
  }

  async getInfov2(userId: Int) {
    const getUserDetails = this.prisma.user.findUnique({
      where: { id: userId },
      select: {
        affiliateUse: {
          select: {
            code: true,
            _count: { select: { freeCases: true } },
          },
        },
        affiliateCode: {
          include: {
            _count: { select: { uses: true } },
          },
        },
      },
    })

    // WARN: This is expensive for top users
    type TotalWagered = { totalWagered: bigint; totalDeposited: bigint }
    const getTotals = this.prisma.$queryRaw<TotalWagered[]>`SELECT
        SUM(wager) as "totalWagered",
        SUM(deposit) as "totalDeposited"
      FROM "AffiliateEarnings" WHERE "referredById" = ${userId}`

    type Count = { count: number }[]
    const getFirstTimeDepositors = this.prisma
      .$queryRaw<Count>`SELECT COUNT(DISTINCT ae."userId")
          FROM "AffiliateEarnings" ae
          INNER JOIN "AffiliateUse" au ON au."userId" = ae."userId"
          WHERE ae."referredById" = ${userId} AND ae.ftd > 0`

    const [userDetails, totals, firstTimeDepositors] = await Promise.all([
      getUserDetails,
      getTotals,
      getFirstTimeDepositors,
    ])

    return {
      code: userDetails.affiliateCode?.code ?? null,
      usedCode: userDetails.affiliateUse?.code?.code ?? null,
      activeUsers: userDetails.affiliateCode?._count.uses ?? 0,
      totalEarnings: userDetails.affiliateCode?.totalEarnings ?? 0,
      unclaimedEarnings: userDetails.affiliateCode?.unclaimedEarnings ?? 0,
      totalWagered: Number(totals[0]?.totalWagered ?? 0),
      totalDeposited: Number(totals[0]?.totalDeposited ?? 0),
      firstTimeDepositors: firstTimeDepositors[0]?.count ?? 0,
      affiliateCasesReedeemed: !!userDetails.affiliateUse?._count?.freeCases,
    }
  }

  async getSummary(search?: string) {
    const where: Prisma.AffiliateCodeWhereInput = {}
    if (search) {
      where.code = { contains: search }
    }

    const codes = await this.prisma.readSlow.affiliateCode.findMany({
      where,
      include: {
        user: {
          select: {
            ...PUBLIC_USER_FIELDS,
            totalDeposits: true,
            totalWithdrawals: true,
          },
        },
        _count: {
          select: { uses: true },
        },
      },
      orderBy: { totalEarnings: 'desc' },
      take: 250,
    })

    if (!codes.length) return []

    const userIds = Prisma.join(codes.map((user) => user.userId))

    const extensions = await this.prisma.readSlow.$queryRaw<any[]>`
        SELECT
          ae."referredById",
          SUM(ae.ftd) AS ftd,
          COUNT(DISTINCT CASE
            WHEN ae.ftd > 0
            THEN ae."userId"
            ELSE NULL
          END) AS "ftdUsers",
          SUM(ae.wager) AS wager,
          SUM(ae.deposit) AS deposit,
          SUM(CASE WHEN ae."createdAt" > NOW() - INTERVAL '30 days' THEN ae.ftd ELSE 0 END) AS "ftdMonth",
          COUNT(DISTINCT CASE
            WHEN ae.ftd > 0
            AND ae."createdAt" > NOW() - INTERVAL '30 days'
            THEN ae."userId"
            ELSE NULL
          END) AS "ftdUsersMonth",
          SUM(CASE WHEN ae."createdAt" > NOW() - INTERVAL '30 days' THEN ae.wager ELSE 0 END) AS "wagerMonth",
          SUM(CASE WHEN ae."createdAt" > NOW() - INTERVAL '30 days' THEN ae.deposit ELSE 0 END) AS "depositMonth"
        FROM "AffiliateEarnings" ae
        LEFT JOIN "AffiliateUse" au ON au."userId" = ae."userId"
        WHERE ae."referredById" IN (${userIds})
        GROUP BY ae."referredById"
        `

    const affiliateRate = Number(process.env.AFFILIATE_RATE ?? 0.005)

    return codes.map((code) => {
      const extraFields = extensions.find((e) => e.referredById === code.userId)

      for (const key in extraFields) {
        if (typeof extraFields[key] === 'bigint') {
          extraFields[key] = Number(extraFields[key])
        }
      }

      return {
        id: code.user.id,
        name: code.user.name,
        avatar: code.user.avatar,
        code: code.code,
        cost: code.user.totalWithdrawals - code.user.totalDeposits,
        wager: Math.floor((100 * code.totalEarnings) / affiliateRate) / 100,
        referrals: code._count.uses,
        clicks: code.clicks,
        ...extraFields,
      }
    })
  }

  async hasPlayedRust(steamId: string) {
    if (!steamId) return false

    const apiKey = this.config.get('auth.steam.apiKey')
    const res = await axios.get(
      `https://api.steampowered.com/IPlayerService/GetOwnedGames/v0001/` +
        `?key=${apiKey}&steamid=${steamId}&format=json`
    )

    if (!res.data.response.games) {
      this.logger.warn('Cannot contact steam response=%o', res.data)
      return false
    }

    return res.data.response.games.some((game) => {
      return game.appid === 252490 && game.playtime_forever >= 5 * 60
    })
  }

  async hasSteamLevel(steamId: string) {
    if (!steamId) return false

    const apiKey = this.config.get('auth.steam.apiKey')
    const res = await axios.get(
      `https://api.steampowered.com/IPlayerService/GetSteamLevel/v1/` +
        `?key=${apiKey}&steamid=${steamId}&format=json`
    )

    return res.data.response.player_level >= 20
  }

  async findCode(code: string) {
    // Blacklisted codes are owned by a single user
    if (this.BLACKLIST_CODES.includes(code)) {
      return this.prisma.affiliateCode.findUnique({
        where: { userId: this.config.get('affiliates.primaryAffiliateId') },
      })
    }

    const res = await this.prisma.affiliateCode.findUnique({
      where: { code },
    })
    assert(res, 'code_not_found')

    return res
  }

  async findCodeByUserId(userId: Int) {
    const res = await this.prisma.affiliateCode.findUnique({
      where: { userId },
    })
    assert(res, 'code_not_found')

    return res
  }

  async findUse<T extends Prisma.AffiliateUseSelect | undefined = undefined>(
    userId: Int,
    select?: T
  ): Promise<
    T extends undefined
      ? AffiliateUse
      : Prisma.AffiliateUseGetPayload<{ select: T }>
  > {
    return this.prisma.affiliateUse.findUnique({
      where: { userId },
      select,
    }) as any
  }

  async getCodeCooldown(userId: Int) {
    const use = await this.findUse(userId)

    return use?.lockedUntil
  }

  async getCodeInfo(code: string) {
    const codeInfo = await this.findCode(code)
    const user = await this.userService.byId(codeInfo.userId)

    return {
      code: codeInfo.code,
      user: {
        name: user.name,
        avatar: user.avatar,
      },
      isFreeCases: codeInfo.isFreeCases,
    }
  }

  async useCode(user: User, code: string, ip: string) {
    const userRestricted = this.RESTRICTED_USERS.includes(user.id)
    assert(!userRestricted, 'cannot_use_code')

    const [currentUse, foundCode] = await Promise.all([
      this.findUse(user.id),
      this.findCode(code),
    ])
    assert(foundCode?.userId !== user.id, 'cannot_refer_yourself')

    if (currentUse) {
      assert(currentUse.lockedUntil < new Date(), 'cannot_change_code')
    }

    const cooldownMs = this.config.get('affiliates.codeCooldownMs')

    const isCodeFreelyChangeable = foundCode.code.includes('CLASH')
    const updatedUseData = {
      lockedUntil: isCodeFreelyChangeable
        ? new Date()
        : new Date(Date.now() + cooldownMs),
      code: { connect: { userId: foundCode.userId } },
      user: { connect: { id: user.id } },
    } satisfies Partial<Prisma.AffiliateUseCreateInput>

    const useAffiliate = this.prisma.affiliateUse.upsert({
      where: { userId: user.id },
      create: {
        user: { connect: { id: user.id } },
        codeFirst: { connect: { userId: foundCode.userId } },
        isFirstSticky: foundCode.isSticky,
        ...updatedUseData,
      },
      update: updatedUseData,
    })

    const addLog = this.userService.addLog({
      userId: user.id,
      category: 'affiliates',
      message: `Referred by id=${foundCode.userId}`,
    })

    await Promise.all([useAffiliate, addLog])

    await this.queue
      .add(TIME_RESET_JOB, { userId: user.id }, { delay: cooldownMs })
      .catch(warn)

    if (foundCode.isScaleo && !currentUse) {
      const siteCode = this.config.get('siteCode')
      this.scaleoService
        .usePromoCode({
          userId: user.id,
          code: `${siteCode}A${foundCode.code}`,
          ip,
        })
        .catch(warn)
    }

    return true
  }

  async setCode(userId: number, code: string) {
    if (this.BLACKLIST_CODES.includes(code))
      throw new BadRequestException('code_already_in_use')

    return this.prisma.affiliateCode
      .upsert({
        where: { userId },
        create: { userId, code },
        update: { code },
      })
      .catch((err) => {
        if (err.code === 'P2002')
          throw new BadRequestException('code_already_in_use')

        throw err
      })
  }

  async forceSetCode(userId: number, code: string) {
    await this.prisma.affiliateCode
      .update({
        where: { code },
        data: { code: null },
      })
      .catch(noop)

    return await this.setCode(userId, code)
  }

  /**
   * Needs `affiliate:claim:${userId}` lock
   */
  async chargeEarnings(userId: Int, amount: Int, tx?: PrismaTx) {
    const code = await tx.affiliateCode.update({
      where: { userId },
      data: { unclaimedEarnings: { decrement: amount } },
    })
    assert(code?.unclaimedEarnings >= 0, 'insufficient_balance')
  }

  async hasRedeemedFreeCases(userId?: Int) {
    if (!userId) return false

    const ownedFreeCases = await this.prisma.casesOnAffiliateUse.count({
      where: { useId: userId },
    })
    return ownedFreeCases > 0
  }

  async claimFreeCases(userId: Int) {
    const freeCasesIds = this.config.get('affiliates.freeCasesIds')
    const use = await this.findUse(userId)
    assert(use, 'affiliate_code_not_used')
    const code = await this.findCodeByUserId(use.referredById)
    const user = await this.userService.byId(userId)
    assert(code.isFreeCases && freeCasesIds.length, 'promotion_not_available')
    const ownedFreeCases = await this.hasRedeemedFreeCases(userId)
    assert(!ownedFreeCases, 'already_claimed')

    const claimedCases = await this.prisma.affiliateUse.update({
      where: { userId },
      data: {
        freeCases: {
          createMany: { data: freeCasesIds.map((caseId) => ({ caseId })) },
        },
      },
      select: {
        freeCases: {
          select: {
            case: {
              select: {
                ...CASE_SUMMARY,
                ...TAGS_SUMMARY,
              },
            },
          },
        },
      },
    })

    if (user.balance === 0n && user.totalDeposits === 0n) {
      const freeCasesWager = await this.config.get('affiliates.freeCasesWager')
      await this.userService.addWagerReq(userId, freeCasesWager)
      await this.userService.restrictions.updateRestriction(userId, {
        wagerFromGift: { increment: freeCasesWager },
      })
    }

    this.eventEmitter.emit(
      PROMOTION_USE,
      new PromotionUseEvent({
        userId,
        isSuccessful: true,
        amount: 30,
        promotionId: 'AffiliateFreeCases',
        promotionDescription:
          'User can claim free cases, once in a lifetime, ' +
          'after using a special affiliate code.',
      })
    )

    const balance = Number(
      user.balance + user.balanceVault + user.balanceCrypto
    )
    await this.antifraudService.sendCustomEvent({
      userId,
      type: 'balance_before_claim_amount',
      payload: {
        balance_before_claim_amount: balance / 100,
      },
    })

    return claimedCases.freeCases
  }

  async toggleCases(userId: Int, isFreeCases: boolean) {
    return await this.prisma.affiliateCode.update({
      where: { userId },
      data: { isFreeCases },
    })
  }

  async expireEarnings(expiredCommission: RewardEarnings) {
    const { userId, amount } = expiredCommission
    const code = await this.prisma.affiliateCode.update({
      where: { userId },
      data: {
        unclaimedEarnings: { decrement: amount },
      },
    })

    const amountHuman = (amount / 100).toFixed(2)
    this.userService.addLog({
      userId,
      category: 'reward',
      message: `Affiliate earnings of ${amountHuman} has expired.`,
    })

    return {
      type: REWARD_DECREMENT,
      data: code,
    }
  }

  async claimEarnings(userId: Int, currency: Currency) {
    const user = await this.userService.byId(userId)
    const code = await this.findCodeByUserId(userId)
    const toClaim = Math.floor(code.unclaimedEarnings)
    assert(toClaim > 0, 'no_unclaimed_earnings')

    const addWagerReq = user.badge !== UserBadge.VERIFIED

    const updateUnclaimedEarnings = this.prisma.affiliateCode.update({
      where: { userId },
      data: {
        unclaimedEarnings: { decrement: toClaim },
      },
    })

    await this.prisma.$transaction([
      ...this.userService.creditParts(
        null,
        userId,
        toClaim,
        { category: 'reward', message: 'Claimed refferal rewards' },
        currency
      ),
      updateUnclaimedEarnings,
    ])

    this.eventEmitter.emit(
      REWARD_CLAIM_EVENT,
      new RewardClaimEvent({
        userId,
        reward: RewardType.AFFILIATE,
      })
    )

    if (addWagerReq) {
      await this.userService.addWagerReq(userId, toClaim)
    }

    if (toClaim >= 50) {
      this.missionsService.registerEvent(
        userId,
        MissionEvent.AFFILIATE_50_CLAIM
      )
    }

    return { claimedEarnings: toClaim }
  }

  async registerClick(code: string) {
    return this.prisma.affiliateCode.update({
      where: { code },
      data: {
        clicks: { increment: 1 },
      },
    })
  }

  async linkScaleo(userId: Int, isScaleo: boolean) {
    return await this.prisma.affiliateCode.update({
      where: { userId },
      data: { isScaleo },
    })
  }

  async getUserSummary(
    summaryOptions: UserSummaryOptions
  ): Promise<UserSummaryResult[]> {
    this.logger.log('getUserSummary options=%o', summaryOptions)
    const { userId, since, until, orderByKey, take } = summaryOptions
    const where: Prisma.AffiliateEarningsGroupByArgs['where'] & {
      createdAt?: Prisma.DateTimeFilter
    } = {
      referredById: userId,
    }

    if (since || until) {
      where.createdAt = {}
    }

    if (since) {
      where.createdAt.gt = since
    }

    if (until) {
      where.createdAt.lt = until
    }

    const summary = await this.prisma.affiliateEarnings.groupBy({
      by: ['userId'],
      _sum: { wager: true, deposit: true, earned: true },
      where,
      take,
      ...(orderByKey && {
        orderBy: {
          _sum: {
            [orderByKey]: 'desc',
          },
        },
      }),
    })

    const batchedUserIds = chunk(
      summary.map((s) => s.userId),
      1000
    )
    this.logger.log(
      'getUserSummary userId=%d queries=%d',
      userId,
      batchedUserIds.length + 1
    )
    const userBatches = await mapAsync(batchedUserIds, async (userIds) => {
      return await this.prisma.user.findMany({
        select: {
          ...PUBLIC_USER_FIELDS,
          affiliateUse: {
            select: { referredById: true },
          },
        },
        where: { id: { in: userIds } },
      })
    })
    const users = userBatches.flatMap((batch) => batch)

    const usersLookup = users.reduce(
      (obj, user) => {
        obj[user.id] = user
        return obj
      },
      {} as Record<Int, (typeof users)[number]>
    )

    return summary.map((entry) => {
      const { id: _, affiliateUse, ...user } = usersLookup[entry.userId]

      return {
        userId: entry.userId,
        ...user,
        active: affiliateUse?.referredById === userId,
        wagered: entry._sum.wager,
        deposited: entry._sum.deposit,
        earned: entry._sum.earned,
      }
    })
  }

  async getGraph(userId: Int, variant: GraphVariant) {
    const days = {
      [GraphVariant.Week]: 7,
      [GraphVariant.Month]: 30,
      [GraphVariant.Year]: 365,
    }[variant]

    const frequency = {
      [GraphVariant.Week]: 'day',
      [GraphVariant.Month]: 'day',
      [GraphVariant.Year]: 'month',
    }[variant]

    const response = await this.prisma.read.$queryRaw<any[]>`SELECT
        DATE_TRUNC(${frequency}, "createdAt") AS date,
        SUM(wager) AS wager, SUM(deposit)::int AS deposit, SUM(ftd)::int AS ftd
      FROM "AffiliateEarnings"
      WHERE
        "referredById"=${userId}
        AND "createdAt" > NOW() - INTERVAL ${Prisma.raw(`'${days} days'`)}
      GROUP BY 1
      ORDER BY 1 DESC
      LIMIT 365`

    return response.map((entry) => {
      entry.wager = Number(entry.wager)
      return entry
    })
  }

  async getCurrentlyAffiliated(userId: Int) {
    return this.prisma.user.findMany({
      where: {
        affiliateUse: { referredById: userId },
      },
      select: { id: true, steamId: true },
    })
  }

  async isCurrentlyAffiliated(referredById: Int, where: Prisma.UserWhereInput) {
    const user = await this.prisma.user.findFirst({
      where: {
        affiliateUse: { referredById },
        ...where,
      },
    })

    return Boolean(user)
  }

  async getEarningsToAccumulate() {
    const earnings: EarningsToAccumulate[] = await this.prisma.$queryRaw`SELECT
      DATE_TRUNC('day', "createdAt") AS day,
      "userId",
      "referredById",
      COUNT(*) as count,
      SUM(wager)::INT AS wager,
      SUM(deposit)::INT AS deposit,
      SUM(ftd)::INT AS ftd,
      SUM(earned::double precision) AS earned
    FROM "AffiliateEarnings"
    WHERE DATE_TRUNC('day', "createdAt") < DATE_TRUNC('day', NOW() - INTERVAL '30 days')
    GROUP BY 1, 2, 3
    HAVING COUNT(*) >= 2
    ORDER BY COUNT(*) DESC
    LIMIT 10 * 1000`

    return earnings
  }

  async handleAccumulateBatch(batch: EarningsToAccumulate[]) {
    await this._ensureAccumulateBatchMatches(batch)

    return await this.prisma.$transaction(
      async (tx) => {
        const deletes = batch.map((earning) => {
          return tx.affiliateEarnings.deleteMany({
            where: {
              userId: earning.userId,
              referredById: earning.referredById,
              createdAt: {
                gte: earning.day,
                lt: new Date(earning.day.getTime() + DAY_MS),
              },
            },
          })
        })
        await Promise.all(deletes)

        await tx.affiliateEarnings.createMany({
          data: batch.map((earning) => ({
            createdAt: earning.day,
            userId: earning.userId,
            referredById: earning.referredById,
            wager: earning.wager,
            deposit: earning.deposit,
            ftd: earning.ftd,
            earned: earning.earned,
          })),
        })
      },
      { timeout: 2 * MINUTE_MS, maxWait: 2 * MINUTE_MS }
    )
  }

  private async _ensureAccumulateBatchMatches(batch: EarningsToAccumulate[]) {
    const aggregations = await this.prisma.affiliateEarnings.aggregate({
      where: {
        OR: batch.map((earning) => ({
          referredById: earning.referredById,
          userId: earning.userId,
          createdAt: {
            gte: earning.day,
            lt: new Date(earning.day.getTime() + DAY_MS),
          },
        })),
      },
      _sum: { deposit: true, wager: true, ftd: true, earned: true },
    })
    const totals = batch
      .sort((a, b) => a.earned - b.earned)
      .reduce(
        (acc, earning) => {
          acc.deposit += Number(earning.deposit)
          acc.wager += Number(earning.wager)
          acc.ftd += Number(earning.ftd)
          acc.earned += Number(earning.earned)
          return acc
        },
        { deposit: 0, wager: 0, ftd: 0, earned: 0 }
      )
    const aggregatedSums = aggregations._sum
    const matches =
      Number(aggregatedSums.deposit) === totals.deposit &&
      Number(aggregatedSums.wager) === totals.wager &&
      Number(aggregatedSums.ftd) === totals.ftd &&
      Math.abs(Number(aggregatedSums.earned) - totals.earned) / totals.earned <
        0.05
    assert(
      matches,
      `Mismatch ${JSON.stringify(aggregatedSums)} ${JSON.stringify(totals)}`
    )
  }

  async emitTransferByAffiliateEvent({
    userId,
    amountUsd,
    transactionId,
  }: {
    userId: Int
    amountUsd: Int
    transactionId: string
  }) {
    const payload: TransferEvent = {
      userId,
      amountUsd,
      transactionId,
      isDecisionNeeded: true,
    }

    const results = await this.eventEmitter.emitAsync(
      TRANSFER_EVENT,
      new TransferEvent(payload)
    )

    const antifraudResponse: AntifraudResponse = results.find(
      (e) => e?.type === ANTIFRAUD_SCORE
    )
    const decisions = extractSiftWorkflows(antifraudResponse)

    const banDecisions = [
      'rain_farmer_payment_abuse',
      'promo_abuser_payment_abuse',
    ]
    const isBlocked = decisions.some((decision) =>
      banDecisions.includes(decision.decision)
    )

    payload.isDecisionNeeded = false
    payload.isSuccessful = !isBlocked
    this.eventEmitter.emit(TRANSFER_EVENT, new TransferEvent(payload))

    if (isBlocked) {
      await this.antifraudService.blockUser(userId)

      decisions.forEach(async (decision) => {
        await this.antifraudService.fraudNotifyDiscord({
          type: FraudNotificationType.BLOCK,
          userId,
          decision,
          message: `Affiliate transfer blocked due to ${decision.decision}`,
          actionName: 'Transfer',
        })
      })

      throw new ForbiddenException('try_again_later')
    }
  }
}
