import { Injectable } from '@nestjs/common'
import { OnEvent } from '@nestjs/event-emitter'
import { RewardEarnings } from '@prisma/client'
import { RedisService } from 'src/modules/redis/redis.service'
import { AffiliatesService } from './affiliates.service'
import { AFFILIATE_EXPIRED_EVENT } from './affiliates.constants'

@Injectable()
export class AffiliatesListener {
  constructor(
    private readonly affiliatesService: AffiliatesService,
    private readonly redis: RedisService
  ) {}

  @OnEvent(AFFILIATE_EXPIRED_EVENT)
  async onAffiliateExpiredEvent(expiredRakeback: RewardEarnings) {
    return await this.affiliatesService.expireEarnings(expiredRakeback)
  }
}
