import { Currency, Int } from '@crashgg/types/dist'
import {
  BadRequestException,
  Body,
  Controller,
  Get,
  NotImplementedException,
  Param,
  ParseBoolPipe,
  ParseEnumPipe,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
  Req,
  SetMetadata,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { SkipThrottle, Throttle, minutes } from '@nestjs/throttler'
import { CurrencyUsed } from 'src/common/currency.decorator'
import { UserId } from 'src/common/user-id.decorator'
import { assert } from 'src/utils/assert'
import { AccessGuard } from '../auth/guards/access.guard'
import { ApiGuard } from '../auth/guards/api.guard'
import { BanGuard } from '../auth/guards/ban.guard'
import { AdminGuard, ModGuard, StaffGuard } from '../auth/guards/role.guard'
import { RedisService } from '../redis/redis.service'
import { UserService } from '../user/user.service'
import { AffiliatesService } from './affiliates.service'
import { HOUR_S } from 'src/utils/constants'
import { GraphVariant } from './affiliates.types'
import { SUCCESS } from 'src/common/constants'
import { affiliateTiers, MINUTE_MS } from '@crashgg/common/dist'
import { Ip } from 'src/common/ip.decorator'
import { TimeoutInterceptor } from 'src/common/interceptors/timeout.interceptor'
import { SetRequestTimeout } from 'src/common/decorators/set-timeout.decorator'

@Controller('affiliates')
export class AffiliatesController {
  constructor(
    private affiliatesService: AffiliatesService,
    private userService: UserService,
    private redis: RedisService,
    private config: ConfigService
  ) {}

  @Get('tiers')
  getTiers(): any {
    const tiers = Object.values(affiliateTiers).filter((t) => !t.isCustom)
    return { tiers }
  }

  @Get('v2')
  @UseGuards(AccessGuard)
  async getInfov2(@UserId() userId: Int) {
    return this.affiliatesService.getInfov2(userId)
  }

  @Get('users')
  @UseGuards(AccessGuard)
  async getInvited(@UserId() userId: Int) {
    const users = await this.affiliatesService.getUserSummary({ userId })

    return { users }
  }

  @Get('graph')
  @UseGuards(AccessGuard)
  async getGraph(
    @UserId() userId: Int,
    @Query('variant', new ParseEnumPipe(GraphVariant)) variant: GraphVariant
  ) {
    const data = await this.affiliatesService.getGraph(userId, variant)

    return { data }
  }

  @Get(['detailed-summary/v2/:since/:until', 'detailed-summary/v2/:since'])
  @SetMetadata('validator', (token) => token.scope === 'affiliates')
  @UseGuards(ApiGuard)
  @SkipThrottle()
  async getSummaryv2(
    @Req() req,
    @Param('since') since: string,
    @Param('until') until: string
  ) {
    const sinceDate = new Date(since)
    assert(!isNaN(sinceDate.getTime()), 'invalid_date')

    let untilDate = null
    if (until) {
      untilDate = new Date(until)
      assert(!isNaN(untilDate.getTime()), 'invalid_date')
    }

    return this.affiliatesService.getUserSummary({
      userId: req.api.userId,
      since: sinceDate,
      until: untilDate,
    })
  }

  @Get('detailed-summary/:since')
  @SetMetadata('validator', (token) => token.scope === 'affiliates')
  @UseGuards(ApiGuard)
  async getWagerSummary() {
    throw new NotImplementedException('deprecated')
  }

  @Get('currently-affiliated')
  @SetMetadata('validator', (token) => token.scope === 'affiliates')
  @UseGuards(ApiGuard)
  async getCurrentlyAffiliated(@Req() req) {
    return await this.affiliatesService.getCurrentlyAffiliated(req.api.userId)
  }

  @Get('is-affiliated/:id')
  @SetMetadata('validator', (token) => token.scope === 'affiliates')
  @UseGuards(ApiGuard)
  async getIsAffiliated(@Req() req, @Param('id', ParseIntPipe) id: Int) {
    const isAffiliated = await this.affiliatesService.isCurrentlyAffiliated(
      req.api.userId,
      { id }
    )

    return { isAffiliated }
  }

  @Get('is-affiliated/by-steamid/:steamId')
  @SetMetadata('validator', (token) => token.scope === 'affiliates')
  @UseGuards(ApiGuard)
  async getIsAffiliatedSteam(@Req() req, @Param('steamId') steamId: string) {
    const isAffiliated = await this.affiliatesService.isCurrentlyAffiliated(
      req.api.userId,
      { steamId }
    )

    return { isAffiliated }
  }

  @Get('/summary')
  @UseInterceptors(TimeoutInterceptor)
  @SetRequestTimeout(3 * MINUTE_MS)
  @UseGuards(AccessGuard, ModGuard)
  async getSummary(@Query('search') search?: string) {
    if (search) {
      return this.affiliatesService.getSummary(search)
    }

    return this.redis.getCachedJSON('affiliates:summary', 3 * HOUR_S, () => {
      return this.affiliatesService.getSummary()
    })
  }

  @Get('/cooldown')
  @UseGuards(AccessGuard)
  async getCooldown(@UserId() userId: Int) {
    const cooldown = await this.affiliatesService.getCodeCooldown(userId)

    return { cooldown }
  }

  @Get(':code/details')
  async getCodeInfo(@Param('code') code: string) {
    code = code.toUpperCase().trim()

    const info = await this.affiliatesService.getCodeInfo(code)

    return { info }
  }

  @Put(':code/use-code')
  @UseGuards(AccessGuard, BanGuard)
  async useCode(@Req() req, @Param('code') code: string, @Ip() ip: string) {
    code = code.toUpperCase().trim()
    const lock = await this.redis.lock([`affiliate:use:${req.user.userId}`])

    try {
      const user = await this.userService.byId(req.user.userId)
      await this.affiliatesService.useCode(user, code, ip)

      return SUCCESS
    } finally {
      lock.release()
    }
  }

  @Put('set-code')
  @UseGuards(AccessGuard, BanGuard)
  async setCode(@UserId() userId: Int, @Query('code') code: string) {
    assert(code?.length)
    code = code.toUpperCase().trim()

    // This is a special K that's distinguished by postgres/js, but somehow considered
    // a normal K when redeeming a code, making it possible to hijack codes with K.
    if (code.includes('K')) {
      throw new BadRequestException('nice!')
    }

    await this.affiliatesService.setCode(userId, code)

    return SUCCESS
  }

  @Patch(':userId/link-scaleo')
  @UseGuards(AccessGuard, StaffGuard)
  async linkScaleo(
    @Param('userId', ParseIntPipe) userId: Int,
    @Body('isScaleo', ParseBoolPipe) isScaleo: boolean
  ) {
    await this.affiliatesService.linkScaleo(userId, isScaleo)
    return SUCCESS
  }

  @Put('force-set-code')
  @UseGuards(AccessGuard, StaffGuard)
  async forceSetCode(
    @Query('code') code: string,
    @Query('userId') userId: string
  ) {
    assert(code?.length)
    code = code.toUpperCase().trim()

    await this.affiliatesService.forceSetCode(+userId, code)

    return SUCCESS
  }

  @Post('claim-free-cases')
  @UseGuards(AccessGuard, BanGuard)
  async claimFreeCases(@UserId() userId: Int) {
    const lock = await this.redis.lock([`affiliate:cases:${userId}`])
    const claimed = await this.affiliatesService
      .claimFreeCases(userId)
      .finally(lock.release)
    return { claimed }
  }

  @Patch(':userId/free-cases')
  @UseGuards(AccessGuard, AdminGuard)
  async toggleCases(
    @Param('userId', ParseIntPipe) userId: Int,
    @Body('enabled', ParseBoolPipe) enabled: boolean
  ) {
    await this.affiliatesService.toggleCases(userId, enabled)
    return SUCCESS
  }

  @Put('claim-earnings')
  @UseGuards(AccessGuard, BanGuard)
  async claimEarnings(
    @UserId() userId: Int,
    @CurrencyUsed([Currency.REAL, Currency.CCY]) currency: Currency
  ) {
    const lock = await this.redis.lock([
      `affiliate:claim:${userId}`,
      `reward-expiration:${userId}`,
    ])

    return this.affiliatesService
      .claimEarnings(userId, currency)
      .finally(lock.release)
  }

  @Put(':code/click')
  @Throttle({ default: { limit: 1, ttl: minutes(5) } })
  async registerClick(@Param('code') code: string) {
    assert(code && code.length && code.length < 30)

    await this.affiliatesService.registerClick(code)

    return SUCCESS
  }
}
