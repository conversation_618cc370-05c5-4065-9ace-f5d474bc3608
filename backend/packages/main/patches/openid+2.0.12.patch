diff --git a/node_modules/openid/http.js b/node_modules/openid/http.js
index 96fcaaa..ae56bb9 100644
--- a/node_modules/openid/http.js
+++ b/node_modules/openid/http.js
@@ -32,7 +32,9 @@ exports.get = (getUrl, params, callback, redirects) => {
         maxRedirects: redirects || 5,
         qs: params,
         headers: {
-            'Accept': 'application/xrds+xml,text/html,text/plain,*/*;q=0.9'
+            'Accept': 'application/xrds+xml,text/html,text/plain,*/*;q=0.9',
+            'referer': 'https://steamcommunity.com/',
+            'origin': 'https://steamcommunity.com',
         },
         transformResponse: a => a
     }).then(result => {
