name: Build Docker image and push
run-name: ${{ inputs.deployment_id }}

on:
  workflow_dispatch:
    inputs:
      deployment_id:
        description: 'Deployment ID'
        required: false
        type: string
      app_name:
        description: 'Application name'
        required: true
        options:
          - main
          - crash
          - inventory
          - voice
          - images
          - socket
          - steam
        type: choice
      branch:
        description: 'Branch name'
        default: main
        type: string

env:
  IMAGE_NAME: ${{ secrets.DOCKERHUB_USERNAME }}/${{ secrets.IMAGE_PREFIX }}-be-${{ inputs.app_name }}

jobs:
  build-and-push:

    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.branch }}

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Set up Docker metadata
        uses: docker/metadata-action@v5
        id: meta
        with:
          images: ${{ env.IMAGE_NAME }}
          tags: |
            latest
            type=sha,format=short,prefix=
            type=ref,event=branch

      - name: Build and Push Docker Image
        id: build_and_push
        uses: docker/build-push-action@v6
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            APPNAME=${{ inputs.app_name }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Image Digest
        id: digest
        run: echo "${{ steps.build_and_push.outputs.digest }}"

      - name: Create digest file
        run: echo "${{ steps.build_and_push.outputs.digest }}" > digest.txt

      - name: Upload digest artifact
        uses: actions/upload-artifact@v4
        id: upload-digest
        with:
          name: docker_digest
          path: digest.txt
